# -*- coding: utf-8 -*-
import pygame
import random
import math
from threading import Thread
import serial
import time

# 全局数据容器
data2 = []  # attention数组
data3 = []  # meditation数组

# 脑电采集线程
class EEGThread(Thread):
    def __init__(self):
        super().__init__(daemon=True)
        self.com = "/dev/ttyUSB0"  # 这里修改串口号
        self.bps = 57600
        self.running = True
        
    def run(self):
        global data2, data3
        try:
            t = serial.Serial(self.com, self.bps)
            b = t.read(3)
            print(f"[{time.ctime()}] 脑电波设备配对中...")
            
            while b[0] != 170 or b[1] != 170 or b[2] != 4:
                b = t.read(3)

            print(f"[{time.ctime()}] 配对成功！")
            a = b + t.read(5)

            while self.running:
                try:
                    a = t.read(8)
                    # 数据解析逻辑（保持您的原始处理逻辑）
                    if a[0] == a[1] == 170 and a[2] == 32:
                        c = a + t.read(28)
                        attention = c[32]
                        meditation = c[34]
                        
                        # 更新全局数据（保留最近20个值）
                        data2.append(attention)
                        data3.append(meditation)
                        data2 = data2[-20:] if len(data2) > 20 else data2
                        data3 = data3[-20:] if len(data3) > 20 else data3
                        
                except Exception as e:
                    print(f"数据读取错误: {str(e)}")
                    break
        
        except Exception as e:
            print(f"设备连接失败: {str(e)}")

# 修改后的数据获取类
class NeuroDataReceiver:
    @staticmethod
    def get_data():
        try:
            attention = data2[-1] if data2 else 0
            meditation = data3[-1] if data3 else 0
            return {'attention': attention, 'meditation': meditation}
        except IndexError:
            return {'attention': 0, 'meditation': 0}

# 游戏实体类
class Player(pygame.sprite.Sprite):
    def __init__(self):
        super().__init__()
        self.image = pygame.Surface((40, 40))
        self.image.fill((0, 255, 0))
        self.rect = self.image.get_rect(center=(400, 500))
        self.energy = 100
    
    def update(self, attention, meditation):
        speed = attention * 0.1
        keys = pygame.key.get_pressed()
        if keys[pygame.K_LEFT]:
            self.rect.x -= speed
        if keys[pygame.K_RIGHT]:
            self.rect.x += speed
        
        self.energy = min(100, self.energy + meditation * 0.01)  #能量恢复速度
        self.rect.clamp_ip(screen_rect)

class Obstacle(pygame.sprite.Sprite):
    def __init__(self):
        super().__init__()
        self.image = pygame.Surface((30, 30))
        self.image.fill((255, 0, 0))
        self.rect = self.image.get_rect(
            center=(random.randint(0, 800), -20)
        )
        self.speed = random.randint(3, 7)
    
    def update(self, meditation):
        self.rect.y += self.speed - meditation * 0.05
        if self.rect.top > 600:
            self.kill()

# 游戏初始化
pygame.init()
screen = pygame.display.set_mode((800, 600))
screen_rect = screen.get_rect()
clock = pygame.time.Clock()
font = pygame.font.Font(None, 32)

# 启动脑电采集线程
eeg_thread = EEGThread()
eeg_thread.start()

# 游戏组初始化
player = Player()
all_sprites = pygame.sprite.Group(player)
obstacles = pygame.sprite.Group()

# 游戏主循环
running = True
spawn_timer = 0
score = 0

while running:
    # 获取实时脑电数据
    data = NeuroDataReceiver.get_data()
    attention = data['attention']
    meditation = data['meditation']
    
    # 事件处理
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
    
    # 生成障碍物逻辑
    spawn_timer += 1
    if spawn_timer > 30 - attention//4:
        obstacles.add(Obstacle())
        spawn_timer = 0
    
    # 更新游戏状态
    all_sprites.update(attention, meditation)
    obstacles.update(meditation)
    
    # 碰撞检测
    hits = pygame.sprite.spritecollide(player, obstacles, True)
    for hit in hits:
        player.energy -= 20
        if player.energy <= 0:
            running = False
    
    # 渲染画面
    screen.fill((0, 0, 0))
    
    # 绘制HUD
    hud_params = [
        (f"Attention: {attention}", (10, 10)),
        (f"Meditation: {meditation}", (10, 40)),
        (f"Energy: {int(player.energy)}", (10, 70))
    ]
    
    for text, pos in hud_params:
        surface = font.render(text, True, (255, 255, 255))
        screen.blit(surface, pos)
    
    # 绘制游戏对象
    all_sprites.draw(screen)
    obstacles.draw(screen)
    
    pygame.display.flip()
    clock.tick(60)

pygame.quit()
eeg_thread.running = False