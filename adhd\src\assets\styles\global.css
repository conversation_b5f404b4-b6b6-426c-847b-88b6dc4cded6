/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

/* 核心变量 */
:root {
    --primary-color: #4e73df;
    --primary-light: #e8efff;
    --primary-dark: #3a5bbd;
    --secondary-color: #f8f9fc;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --text-color: #4e4e4e;
    --text-light: #7e7e7e;
    --border-color: #e3e6f0;
    --border-radius: 12px;
    --card-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
    --card-shadow-hover: 0 12px 28px rgba(71, 118, 230, 0.15);
    --transition-speed: 0.3s;
    --header-height: 60px;
}

body {
    background-color: #f5f7fa;
    color: var(--text-color);
    line-height: 1.6;
}

/* 通用按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: all var(--transition-speed);
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.btn-primary {
    background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
    color: white;
    box-shadow: 0 4px 10px rgba(71, 118, 230, 0.2);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(71, 118, 230, 0.3);
}

.btn-secondary {
    background: var(--success-color);
    color: white;
}

.btn-secondary:hover {
    background: #17a673;
    transform: translateY(-2px);
}

.btn-info {
    background: var(--info-color);
    color: white;
}

.btn-info:hover {
    background: #2c9faf;
    transform: translateY(-2px);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
}

/* 通用卡片样式 */
.card {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: transform var(--transition-speed), box-shadow var(--transition-speed);
    border: 1px solid var(--border-color);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: var(--card-shadow-hover);
}

.card-header {
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
    background: linear-gradient(to right, var(--primary-light), #f8f9fc);
}

.card-body {
    padding: 20px;
}

.card-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    background-color: #f8f9fc;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-color);
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-size: 14px;
    transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px var(--primary-light);
    outline: none;
}

.form-control.is-invalid {
    border-color: var(--danger-color);
}

.form-control.is-valid {
    border-color: var(--success-color);
}

/* 网格系统 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col {
    flex: 1;
    padding: 0 15px;
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* 工具类 */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-info { color: var(--info-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }
.text-muted { color: var(--text-light); }

.bg-primary { background-color: var(--primary-color); }
.bg-light { background-color: var(--secondary-color); }
.bg-white { background-color: white; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-grid { display: grid; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.m-0 { margin: 0; }
.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 1rem; }
.m-4 { margin: 1.5rem; }
.m-5 { margin: 3rem; }

.p-0 { padding: 0; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

/* 响应式工具类 */
@media (max-width: 768px) {
    .d-md-none { display: none; }
    .d-md-block { display: block; }
    
    .col-md-12 { flex: 0 0 100%; }
    .col-md-6 { flex: 0 0 50%; }
    .col-md-4 { flex: 0 0 33.333333%; }
    .col-md-3 { flex: 0 0 25%; }
}

@media (max-width: 576px) {
    .d-sm-none { display: none; }
    .d-sm-block { display: block; }
    
    .col-sm-12 { flex: 0 0 100%; }
}

/* 动画类 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 统一布局样式 - 所有界面通用 */

/* 页面容器 */
.page-container {
  background-color: #f5f7fa;
  color: var(--text-color);
  min-height: 100vh;
}

/* 内容包装区 */
.content-wrapper {
  display: flex;
  height: calc(100vh - var(--header-height));
}

/* 主内容区 */
.main-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 通用容器样式 */
.page-main-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.page-main-container:hover {
  box-shadow: 0 12px 28px rgba(71, 118, 230, 0.15);
}

/* 区域头部样式 */
.area-header {
  padding: 18px 25px;
  background: linear-gradient(to right, var(--primary-light), #f8f9fc);
  border-bottom: 1px solid var(--border-color);
}

.area-header h3 {
  font-size: 20px;
  color: var(--primary-dark);
  display: flex;
  align-items: center;
  margin: 0;
}

.area-header h3 i {
  margin-right: 12px;
  font-size: 1.3rem;
  color: #4e73df;
  font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  display: inline-block;
  min-width: 22px;
  text-align: center;
  line-height: 1;
}

/* 主要内容区域 */
.main-content {
  padding: 25px;
  height: 100%;
  overflow-y: auto;
}

/* 数据概览卡片网格 */
.data-overview-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

/* 卡片样式增强 */
.data-overview-cards .card {
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  border: none;
  background: linear-gradient(135deg, #fff 0%, #f8f9fc 100%);
}

.data-overview-cards .card-icon {
  width: 50px;
  height: 50px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  font-size: 1.5rem;
}

.data-overview-cards .card-content h3 {
  font-size: 14px;
  color: var(--text-light);
  margin-bottom: 5px;
  font-weight: 500;
}

.data-overview-cards .card-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-dark);
  margin: 0;
}

.data-overview-cards .card-trend {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-top: 5px;
  display: inline-block;
}

.data-overview-cards .card-trend.up {
  background: rgba(28, 200, 138, 0.15);
  color: #1cc88a;
}

.data-overview-cards .card-trend.down {
  background: rgba(231, 74, 59, 0.15);
  color: #e74a3b;
}

.data-overview-cards .card-trend.neutral {
  background: rgba(54, 185, 204, 0.15);
  color: #36b9cc;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .data-overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .btn {
    padding: 8px 16px;
    font-size: 13px;
  }

  .card {
    margin: 10px;
  }

  .card-header, .card-body, .card-footer {
    padding: 15px;
  }

  .main-area {
    padding: 15px;
  }

  .data-overview-cards {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .main-content {
    padding: 15px;
  }

  .area-header {
    padding: 15px 20px;
  }

  .area-header h3 {
    font-size: 18px;
  }
}
