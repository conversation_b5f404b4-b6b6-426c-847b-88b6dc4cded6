import { createRouter, createWebHistory } from 'vue-router'

// 页面组件懒加载
const Home = () => import('@/views/Home.vue')
const Login = () => import('@/views/NewLogin.vue')
const DataAnalysis = () => import('@/views/data-analysis.vue')
const AIRehabilitation = () => import('@/views/AI-intelligent-rehabilitation.vue')
const OnlineConsultation = () => import('@/views/OnlineConsultation.vue')
const KnowledgeDissemination = () => import('@/views/Knowledge-dissemination.vue')
const ADHDScale = () => import('@/views/ADHDScale.vue')
const AttentionGames = () => import('@/views/AttentionGames.vue')

const Profile = () => import('@/views/Profile.vue')

const routes = [
  {
    path: '/',
    name: 'ADHDScale',
    component: ADHDScale,
    meta: {
      title: '心理健康筛查量表 - 智能心理健康检测与筛查系统',
      requiresAuth: true
    }
  },
  {
    path: '/home',
    name: 'Home',
    component: Home,
    meta: {
      title: '智能筛查 - 智能心理健康检测与筛查系统',
      requiresAuth: true
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '登录 - 智能心理健康检测与筛查系统',
      requiresAuth: false
    }
  },

  {
    path: '/data-analysis',
    name: 'DataAnalysis',
    component: DataAnalysis,
    meta: {
      title: '数据分析 - 智能心理健康检测与筛查系统',
      requiresAuth: true
    }
  },
  {
    path: '/ai-rehabilitation',
    name: 'AIRehabilitation',
    component: AIRehabilitation,
    meta: {
      title: 'AI康复计划 - 智能心理健康检测与筛查系统',
      requiresAuth: true
    }
  },
  {
    path: '/online-consultation',
    name: 'OnlineConsultation',
    component: OnlineConsultation,
    meta: {
      title: '在线咨询 - 智能心理健康检测与筛查系统',
      requiresAuth: true
    }
  },
  {
    path: '/knowledge-dissemination',
    name: 'KnowledgeDissemination',
    component: KnowledgeDissemination,
    meta: {
      title: '知识课程 - 智能心理健康检测与筛查系统',
      requiresAuth: true
    }
  },
  {
    path: '/adhd-scale',
    redirect: '/'
  },
  {
    path: '/attention-games',
    name: 'AttentionGames',
    component: AttentionGames,
    meta: {
      title: '注意力游戏 - 智能心理健康检测与筛查系统',
      requiresAuth: true
    }
  },

  {
    path: '/profile',
    name: 'Profile',
    component: Profile,
    meta: {
      title: '个人中心 - 智能心理健康检测与筛查系统',
      requiresAuth: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 导入认证相关
import { localStorageUtils } from '@/api/ruoyiAPI.js'

// 路由守卫
router.beforeEach((to, from, next) => {
  // 设置页面标题
  if (to.meta.title) {
    document.title = to.meta.title
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    // 检查用户是否已登录
    if (!localStorageUtils.isLoggedIn()) {
      // 未登录，重定向到登录页
      next('/login')
      return
    }
  }

  // 如果已登录用户访问登录页，重定向到量表筛查页面
  if (to.path === '/login' && localStorageUtils.isLoggedIn()) {
    next('/')
    return
  }

  next()
})

export default router
