<template>
  <div class="page-container">
    <!-- 公共头部组件 -->
    <AppHeader
      :user="currentUser"
      current-route="knowledge-dissemination"
      @search="handleSearch"
      @show-notifications="showNotifications"
      @show-messages="showMessages"
      @show-settings="showSettings"
      @show-user-menu="showUserMenu"
    />

    <!-- 内容包装区 -->
    <div class="content-wrapper">
      <!-- 公共侧边栏组件 -->
      <AppSidebar
        current-route="knowledge-dissemination"
        :expanded="navExpanded"
        @expand="expandNav"
        @collapse="collapseNav"
      />

      <!-- 主内容区 -->
      <div class="main-area">
        <div class="page-main-container">
          <div class="area-header">
            <h3><i class="fas fa-graduation-cap"></i> ADHD知识学习中心</h3>
          </div>

          <!-- 主要内容 -->
          <div class="main-content">
            <!-- 数据概览卡片 -->
            <div class="data-overview-cards">
              <div class="card">
                <div class="card-icon">
                  <i class="fas fa-book"></i>
                </div>
                <div class="card-content">
                  <h3>学习资源</h3>
                  <p class="card-value">{{ totalResources }}</p>
                </div>
              </div>

              <div class="card">
                <div class="card-icon">
                  <i class="fas fa-layer-group"></i>
                </div>
                <div class="card-content">
                  <h3>知识模块</h3>
                  <p class="card-value">{{ modules.length }}</p>
                </div>
              </div>

              <div class="card">
                <div class="card-icon">
                  <i class="fas fa-users"></i>
                </div>
                <div class="card-content">
                  <h3>学习人数</h3>
                  <p class="card-value">12.5k+</p>
                </div>
              </div>

              <div class="card">
                <div class="card-icon">
                  <i class="fas fa-star"></i>
                </div>
                <div class="card-content">
                  <h3>推荐文章</h3>
                  <p class="card-value">{{ recommendedCount }}</p>
                </div>
              </div>
            </div>

            <!-- 搜索和筛选区域 -->
            <div class="chart-wrapper">
              <div class="chart-header">
                <h3>知识模块</h3>
                <div class="chart-controls">
                  <div class="search-box">
                    <i class="fas fa-search"></i>
                    <input
                      type="text"
                      placeholder="搜索课程、模块或链接..."
                      v-model="searchQuery"
                      @input="handleSearch"
                    >
                  </div>
                  <select v-model="activeCategory" @change="setActiveCategory(activeCategory)">
                    <option value="all">全部分类</option>
                    <option v-for="category in categories" :key="category.id" :value="category.id">
                      {{ category.name }}
                    </option>
                  </select>
                </div>
              </div>

              <!-- 文章列表 -->
              <div class="chart-body">
                <div class="articles-list">
                  <div
                    v-for="article in filteredArticles"
                    :key="article.id"
                    class="article-item"
                    :class="{ 'high-views': article.views > 1000, 'recommended': article.isRecommended }"
                    @click="openLink(article)"
                  >
                    <div class="article-icon">
                      <i :class="getLinkIcon(article.type)" :style="{ color: getCategoryColor(article.category) }"></i>
                    </div>
                    <div class="article-info">
                      <div class="article-title">
                        <h4>{{ article.title }}</h4>
                        <div class="article-badges">
                          <span class="article-category">{{ getCategoryName(article.category) }}</span>
                          <span class="article-type" :class="article.type">{{ getLinkTypeLabel(article.type) }}</span>
                          <span v-if="article.isRecommended" class="admin-recommended">
                            <i class="fas fa-star"></i> 管理员推荐
                          </span>
                          <span v-if="article.views > 1000" class="high-views-badge">
                            <i class="fas fa-fire"></i> 热门
                          </span>
                        </div>
                      </div>
                      <p class="article-description">{{ article.description }}</p>
                      <div class="article-meta">
                        <span><i class="fas fa-external-link-alt"></i> 点击访问</span>
                        <span><i class="fas fa-eye"></i> {{ formatViews(article.views) }} 阅读</span>
                        <span><i class="fas fa-star"></i> {{ article.rating || '4.5' }} 分</span>
                      </div>
                    </div>
                    <div class="article-arrow">
                      <i class="fas fa-chevron-right"></i>
                    </div>
                  </div>
                </div>

                <!-- 空状态 -->
                <div v-if="filteredArticles.length === 0" class="empty-state">
                  <i class="fas fa-search"></i>
                  <h3>未找到相关内容</h3>
                  <p>请尝试其他搜索关键词或选择不同的分类</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <footer>
      <p>© 2025 学生心理健康系统 | <a href="#">使用条款</a> | <a href="#">隐私政策</a></p>
    </footer>
  </div>
</template>


<script>
import AppHeader from '@/components/common/AppHeader.vue'
import AppSidebar from '@/components/common/AppSidebar.vue'

export default {
  name: 'KnowledgeDissemination',
  components: {
    AppHeader,
    AppSidebar
  },
  data() {
    return {
      navExpanded: false,
      currentUser: {
        name: '张医生',
        avatar: '../assets/images/avatar.jpg'
      },

      // 界面状态
      searchQuery: '',
      activeCategory: 'all',

      // 分类数据
      categories: [
        { id: 'all', name: '全部', icon: 'fas fa-th-large' },
        { id: 'basic', name: '基础知识', icon: 'fas fa-brain' },
        { id: 'treatment', name: '治疗方法', icon: 'fas fa-stethoscope' },
        { id: 'lifestyle', name: '生活方式', icon: 'fas fa-heart' },
        { id: 'education', name: '教育资源', icon: 'fas fa-graduation-cap' },
        { id: 'support', name: '支持工具', icon: 'fas fa-tools' },
        { id: 'research', name: '研究资料', icon: 'fas fa-microscope' }
      ],



      // 知识模块数据
      modules: [
        {
          id: 1,
          title: 'ADHD基础知识',
          description: '了解ADHD的基本概念、症状和诊断标准',
          icon: 'fas fa-brain',
          color: '#3498db',
          category: 'basic',
          rating: '4.9',
          learners: '2.3k',
          isRecommended: true,
          links: [
            {
              id: 1,
              url: 'https://www.nimh.nih.gov/health/topics/attention-deficit-hyperactivity-disorder-adhd',
              title: 'NIMH - ADHD官方指南',
              description: '美国国家心理健康研究所关于ADHD的权威指南，包含最新的诊断标准和治疗建议',
              type: 'article',
              rating: '4.9',
              views: 15420,
              isRecommended: true
            },
            {
              id: 2,
              url: 'https://www.who.int/news-room/fact-sheets/detail/attention-deficit-hyperactivity-disorder-(adhd)',
              title: 'WHO - ADHD事实清单',
              description: '世界卫生组织发布的ADHD基础知识和全球统计数据',
              type: 'article',
              rating: '4.8',
              views: 12350,
              isRecommended: true
            },
            {
              id: 3,
              url: 'https://www.youtube.com/watch?v=JowPOqRmxNs',
              title: 'ADHD症状识别视频教程',
              description: '专业医生详细讲解ADHD的主要症状和识别方法',
              type: 'video',
              rating: '4.7',
              views: 8920
            },
            {
              id: 4,
              url: 'https://www.additudemag.com/what-is-adhd-symptoms-causes-treatments/',
              title: 'ADHD完整指南：症状、原因与治疗',
              description: 'ADDitude杂志提供的ADHD全面指南，适合患者和家属阅读',
              type: 'article',
              rating: '4.6',
              views: 9850
            }
          ]
        },
        {
          id: 2,
          title: '诊断与评估',
          description: '专业的ADHD诊断流程和评估工具',
          icon: 'fas fa-stethoscope',
          color: '#e74c3c',
          category: 'basic',
          rating: '4.8',
          learners: '1.8k',
          links: [
            {
              id: 4,
              url: 'https://www.cdc.gov/ncbddd/adhd/diagnosis.html',
              title: 'CDC - ADHD诊断指南',
              description: '美国疾控中心发布的ADHD诊断标准和流程',
              type: 'article',
              rating: '4.9',
              views: 11200,
              isRecommended: true
            },
            {
              id: 5,
              url: 'https://www.additudemag.com/adhd-test/',
              title: 'ADHD自我评估量表',
              description: '专业的ADHD症状自我评估工具',
              type: 'tool',
              rating: '4.6',
              views: 7850
            },
            {
              id: 6,
              url: 'https://www.psychiatry.org/patients-families/adhd/what-is-adhd',
              title: 'APA - ADHD诊断标准详解',
              description: '美国精神病学协会关于ADHD诊断的详细说明',
              type: 'article',
              rating: '4.7',
              views: 6420
            }
          ]
        },
        {
          id: 3,
          title: '药物治疗',
          description: 'ADHD药物治疗的种类、效果和注意事项',
          icon: 'fas fa-pills',
          color: '#9b59b6',
          category: 'treatment',
          rating: '4.7',
          learners: '1.5k',
          isRecommended: true,
          links: [
            {
              id: 7,
              url: 'https://www.mayoclinic.org/diseases-conditions/adhd/diagnosis-treatment/drc-20350895',
              title: 'Mayo Clinic - ADHD药物治疗',
              description: '梅奥诊所关于ADHD药物治疗的权威指南',
              type: 'article',
              rating: '4.8',
              views: 13650,
              isRecommended: true
            },
            {
              id: 8,
              url: 'https://www.webmd.com/add-adhd/adhd-medication-chart',
              title: 'ADHD药物对比图表',
              description: '详细对比各种ADHD药物的效果和副作用',
              type: 'tool',
              rating: '4.5',
              views: 8920
            },
            {
              id: 9,
              url: 'https://www.drugs.com/condition/adhd.html',
              title: 'ADHD药物完整列表',
              description: '包含所有FDA批准的ADHD治疗药物信息',
              type: 'article',
              rating: '4.6',
              views: 5430
            }
          ]
        },
        {
          id: 4,
          title: '行为疗法',
          description: '认知行为疗法、行为干预等非药物治疗方法',
          icon: 'fas fa-user-md',
          color: '#1abc9c',
          category: 'treatment',
          rating: '4.6',
          learners: '1.2k',
          links: [
            {
              id: 10,
              url: 'https://www.apa.org/ptsd-guideline/patients-and-families/cognitive-behavioral',
              title: 'APA - 认知行为疗法指南',
              description: '美国心理学会关于ADHD认知行为疗法的专业指导',
              type: 'article',
              rating: '4.7',
              views: 7250
            },
            {
              id: 11,
              url: 'https://www.youtube.com/watch?v=example-cbt',
              title: 'ADHD行为干预技巧',
              description: '实用的ADHD行为管理和干预技巧演示',
              type: 'video',
              rating: '4.4',
              views: 4820
            },
            {
              id: 12,
              url: 'https://www.understood.org/en/articles/behavior-therapy-what-you-need-to-know',
              title: '行为疗法完整指南',
              description: '详细介绍各种行为疗法技术和实施方法',
              type: 'article',
              rating: '4.5',
              views: 6150
            }
          ]
        },
        {
          id: 5,
          title: '生活管理技巧',
          description: '日常生活中的注意力管理和时间规划技巧',
          icon: 'fas fa-calendar-check',
          color: '#2ecc71',
          category: 'lifestyle',
          rating: '4.8',
          learners: '2.1k',
          isRecommended: true,
          links: [
            {
              id: 13,
              url: 'https://chadd.org/about-adhd/overview/',
              title: 'CHADD - 生活管理策略',
              description: 'ADHD患者日常生活管理的实用建议和技巧',
              type: 'article',
              rating: '4.8',
              views: 14250,
              isRecommended: true
            },
            {
              id: 14,
              url: 'https://www.additudemag.com/time-management-tips/',
              title: '时间管理技巧大全',
              description: '专为ADHD患者设计的时间管理和组织技巧',
              type: 'tool',
              rating: '4.6',
              views: 9750
            },
            {
              id: 15,
              url: 'https://www.helpguide.org/articles/add-adhd/managing-adult-adhd.htm',
              title: '成人ADHD生活指南',
              description: '成年ADHD患者的生活管理完整指南',
              type: 'article',
              rating: '4.7',
              views: 11200
            },
            {
              id: 16,
              url: 'https://www.youtube.com/watch?v=example-lifestyle',
              title: 'ADHD日常管理技巧视频',
              description: '实用的ADHD日常生活管理技巧演示',
              type: 'video',
              rating: '4.5',
              views: 6850
            }
          ]
        },
        {
          id: 6,
          title: '学习与工作',
          description: '学习环境优化、工作效率提升等教育资源',
          icon: 'fas fa-graduation-cap',
          color: '#f39c12',
          category: 'education',
          rating: '4.5',
          learners: '1.7k',
          links: [
            {
              id: 17,
              url: 'https://www.understood.org/en/learning-thinking-differences/child-learning-disabilities/add-adhd',
              title: 'Understood - ADHD学习支持',
              description: '为ADHD学生提供的学习策略和教育资源',
              type: 'article',
              rating: '4.7',
              views: 8920
            },
            {
              id: 18,
              url: 'https://www.youtube.com/watch?v=example-study',
              title: 'ADHD学习方法视频',
              description: '适合ADHD学生的高效学习方法和技巧',
              type: 'video',
              rating: '4.3',
              views: 5420
            },
            {
              id: 19,
              url: 'https://www.additudemag.com/adhd-study-tips/',
              title: 'ADHD学习技巧指南',
              description: '专为ADHD学生设计的学习策略和技巧',
              type: 'article',
              rating: '4.6',
              views: 7850
            },
            {
              id: 20,
              url: 'https://www.understood.org/en/articles/workplace-accommodations-for-people-with-adhd',
              title: '职场ADHD支持指南',
              description: '工作场所的ADHD支持和适应策略',
              type: 'article',
              rating: '4.4',
              views: 6200
            }
          ]
        },
        {
          id: 7,
          title: '家庭支持',
          description: '家长指导、家庭环境优化等支持资源',
          icon: 'fas fa-home',
          color: '#e67e22',
          category: 'support',
          rating: '4.9',
          learners: '1.9k',
          isRecommended: true,
          links: [
            {
              id: 21,
              url: 'https://www.aacap.org/AACAP/Families_and_Youth/Facts_for_Families/FFF-Guide/Children-Who-Cant-Pay-Attention-ADHD-006.aspx',
              title: 'AACAP - 家长指导手册',
              description: '美国儿童青少年精神医学会为家长提供的ADHD指导',
              type: 'article',
              rating: '4.9',
              views: 16850,
              isRecommended: true
            },
            {
              id: 22,
              url: 'https://www.parentingscience.com/ADHD-parenting.html',
              title: 'ADHD育儿科学',
              description: '基于科学研究的ADHD儿童养育方法',
              type: 'article',
              rating: '4.6',
              views: 10250
            },
            {
              id: 23,
              url: 'https://chadd.org/for-parents/',
              title: 'CHADD家长资源中心',
              description: 'CHADD组织为ADHD儿童家长提供的全面资源',
              type: 'article',
              rating: '4.8',
              views: 12400
            }
          ]
        },
        {
          id: 8,
          title: '最新研究',
          description: '最新的ADHD研究成果和科学发现',
          icon: 'fas fa-microscope',
          color: '#34495e',
          category: 'research',
          rating: '4.4',
          learners: '0.8k',
          links: [
            {
              id: 24,
              url: 'https://www.ncbi.nlm.nih.gov/pmc/articles/PMC7851039/',
              title: 'ADHD最新研究综述',
              description: '2024年ADHD研究的最新进展和发现',
              type: 'research',
              rating: '4.5',
              views: 3250
            },
            {
              id: 25,
              url: 'https://www.nature.com/articles/s41380-021-01173-x',
              title: 'ADHD遗传学研究',
              description: '关于ADHD遗传因素的最新科学研究',
              type: 'research',
              rating: '4.3',
              views: 2180
            },
            {
              id: 26,
              url: 'https://www.sciencedirect.com/science/article/pii/S0890856721008237',
              title: 'ADHD治疗新进展',
              description: '最新的ADHD治疗方法和药物研究进展',
              type: 'research',
              rating: '4.4',
              views: 2850
            }
          ]
        },
        {
          id: 9,
          title: '支持工具',
          description: 'ADHD管理应用、工具和资源',
          icon: 'fas fa-tools',
          color: '#8e44ad',
          category: 'support',
          rating: '4.3',
          learners: '1.4k',
          links: [
            {
              id: 27,
              url: 'https://www.additudemag.com/adhd-apps/',
              title: 'ADHD管理应用推荐',
              description: '帮助ADHD患者管理症状的手机应用程序',
              type: 'tool',
              rating: '4.2',
              views: 8650
            },
            {
              id: 28,
              url: 'https://www.understood.org/en/articles/assistive-technology-for-adhd',
              title: 'ADHD辅助技术指南',
              description: '各种辅助技术工具帮助ADHD患者提高效率',
              type: 'tool',
              rating: '4.4',
              views: 5920
            }
          ]
        }
      ]
    }
  },

  computed: {
    // 将所有模块中的文章提取成一个统一的文章列表
    allArticles() {
      const articles = []
      this.modules.forEach(module => {
        module.links.forEach(link => {
          articles.push({
            ...link,
            category: module.category,
            moduleTitle: module.title
          })
        })
      })
      // 按点击率（阅读量）降序排序，阅读量高的文章排在前面
      return articles.sort((a, b) => {
        return (b.views || 0) - (a.views || 0)
      })
    },

    filteredArticles() {
      let filtered = this.allArticles

      // 按分类筛选
      if (this.activeCategory !== 'all') {
        filtered = filtered.filter(article => article.category === this.activeCategory)
      }

      // 按搜索关键词筛选
      if (this.searchQuery) {
        const query = this.searchQuery.toLowerCase()
        filtered = filtered.filter(article =>
          article.title.toLowerCase().includes(query) ||
          article.description.toLowerCase().includes(query) ||
          article.moduleTitle.toLowerCase().includes(query)
        )
      }

      return filtered
    },

    totalResources() {
      return this.allArticles.length
    },

    recommendedCount() {
      return this.allArticles.filter(article => article.isRecommended).length
    }
  },

  mounted() {
    // 初始化导航状态
    if (localStorage.getItem('navExpanded') === 'true') {
      this.navExpanded = true
    }
  },

  methods: {
    // === 界面控制方法 ===
    setActiveCategory(categoryId) {
      this.activeCategory = categoryId
    },

    handleSearch() {
      // 搜索逻辑已在computed中实现
    },

    // === 链接操作方法 ===
    openLink(link) {
      // 在新标签页打开链接
      window.open(link.url, '_blank', 'noopener,noreferrer')

      // 可以在这里添加访问统计逻辑
      console.log(`访问链接: ${link.title}`)
    },

    getLinkIcon(type) {
      const icons = {
        article: 'fas fa-file-alt',
        video: 'fas fa-play-circle',
        audio: 'fas fa-headphones',
        tool: 'fas fa-tools',
        course: 'fas fa-graduation-cap',
        research: 'fas fa-microscope',
        news: 'fas fa-newspaper',
        other: 'fas fa-link'
      }
      return icons[type] || 'fas fa-link'
    },


    // === 工具方法 ===
    getLinkTypeLabel(type) {
      const labels = {
        article: '文章',
        video: '视频',
        audio: '音频',
        tool: '工具',
        course: '课程',
        research: '研究',
        news: '新闻',
        other: '其他'
      }
      return labels[type] || '其他'
    },

    formatViews(views) {
      if (views >= 10000) {
        return (views / 1000).toFixed(1) + 'k'
      } else if (views >= 1000) {
        return (views / 1000).toFixed(1) + 'k'
      }
      return views.toString()
    },

    getCategoryName(category) {
      const categoryNames = {
        basic: 'ADHD基础知识',
        diagnosis: '诊断与评估',
        treatment: '药物治疗',
        therapy: '行为疗法',
        lifestyle: '生活管理',
        education: '学习与工作',
        support: '家庭支持',
        research: '最新研究',
        tools: '支持工具'
      }
      return categoryNames[category] || '其他'
    },

    getCategoryColor(category) {
      const categoryColors = {
        basic: '#3498db',
        diagnosis: '#e74c3c',
        treatment: '#2ecc71',
        therapy: '#f39c12',
        lifestyle: '#9b59b6',
        education: '#1abc9c',
        support: '#e67e22',
        research: '#34495e',
        tools: '#8e44ad'
      }
      return categoryColors[category] || '#95a5a6'
    },

    // === AppHeader 事件处理 ===
    showNotifications() {
      console.log('显示通知')
    },

    showMessages() {
      console.log('显示消息')
    },

    showSettings() {
      console.log('显示设置')
    },

    showUserMenu() {
      console.log('显示用户菜单')
    },

    // === AppSidebar 事件处理 ===
    expandNav() {
      this.navExpanded = true
      localStorage.setItem('navExpanded', 'true')
    },

    collapseNav() {
      this.navExpanded = false
      localStorage.setItem('navExpanded', 'false')
    }
  }
}
</script>

<style scoped>
/* 页面布局 */
.knowledge-dissemination-page {
  min-height: 100vh;
  background: #f8f9fa;
}

.content-wrapper {
  display: flex;
  min-height: calc(100vh - 60px);
}

.main-area {
  flex: 1;
  padding: 20px;
  margin-left: 250px;
  transition: margin-left 0.3s ease;
  overflow-y: auto;
}

/* 知识课程容器 */
.knowledge-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.knowledge-container:hover {
  box-shadow: 0 12px 28px rgba(71, 118, 230, 0.15);
}

/* 区域头部样式 */
.area-header {
  padding: 18px 25px;
  background: linear-gradient(to right, #e8efff, #f8f9fc);
  border-bottom: 1px solid #e3e6f0;
}

.area-header h3 {
  font-size: 20px;
  color: #1976d2;
  display: flex;
  align-items: center;
  margin: 0;
  font-weight: 600;
}

.area-header h3 i {
  margin-right: 12px;
  font-size: 1.3rem;
  color: #4e73df;
  font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  display: inline-block;
  min-width: 22px;
  text-align: center;
  line-height: 1;
}

/* 主内容区域 */
.main-content {
  padding: 25px;
  height: 100%;
}

/* 数据概览卡片 */
.data-overview-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  transition: transform 0.3s, box-shadow 0.3s;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4e73df, #36b9cc);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.card-icon i {
  font-size: 24px;
  color: white;
}

.card-content h3 {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.card-value {
  margin: 0;
  font-size: 28px;
  font-weight: 700;
  color: #333;
}

/* 图表容器 */
.chart-wrapper {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h3 {
  font-size: 18px;
  margin: 0;
  color: #333;
}

.chart-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.chart-controls select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  background: white;
}

.chart-body {
  position: relative;
}

/* 页面标题 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-left h1 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 28px;
}

.header-left p {
  color: #7f8c8d;
  font-size: 16px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 按钮样式 */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.btn-outline {
  background: transparent;
  border: 2px solid #3498db;
  color: #3498db;
}

.btn-outline:hover {
  background: #3498db;
  color: white;
}

.btn-icon {
  padding: 8px;
  background: transparent;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  color: #7f8c8d;
  transition: all 0.3s ease;
}

.btn-icon:hover {
  background: #ecf0f1;
  color: #2c3e50;
}

.btn-close {
  background: transparent;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #7f8c8d;
  padding: 5px;
}

.btn-close:hover {
  color: #e74c3c;
}

/* 搜索和筛选区域 */
.search-filter-section {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.search-box {
  position: relative;
  display: flex;
  align-items: center;
}

.search-box i {
  position: absolute;
  left: 12px;
  color: #666;
  font-size: 14px;
}

.search-box input {
  padding: 8px 12px 8px 35px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  width: 250px;
  background: white;
}

.search-box input:focus {
  outline: none;
  border-color: #4e73df;
}

.filter-tabs {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.filter-tab {
  padding: 10px 20px;
  background: #ecf0f1;
  border: none;
  border-radius: 25px;
  cursor: pointer;
  font-size: 14px;
  color: #7f8c8d;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-tab:hover {
  background: #d5dbdb;
  color: #2c3e50;
}

.filter-tab.active {
  background: #3498db;
  color: white;
}

/* 文章列表 */
.articles-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.article-item {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 20px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  cursor: pointer;
  border-left: 4px solid transparent;
}

.article-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.article-item.recommended {
  border-left-color: #f39c12;
  background: linear-gradient(135deg, #fff9e6 0%, #ffffff 100%);
}

.article-item.high-views {
  border-left-color: #e74c3c;
}

/* 文章图标 */
.article-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-right: 20px;
  flex-shrink: 0;
}

.article-icon i {
  font-size: 24px;
}

/* 文章信息 */
.article-info {
  flex: 1;
  min-width: 0;
}

.article-title {
  margin-bottom: 8px;
}

.article-title h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #333;
  font-weight: 600;
  line-height: 1.3;
}

.article-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.article-category {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.article-type {
  background: #d1ecf1;
  color: #0c5460;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.admin-recommended {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.admin-recommended i {
  margin-right: 4px;
}

.high-views-badge {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.high-views-badge i {
  margin-right: 4px;
}

.article-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.5;
}

.article-meta {
  display: flex;
  gap: 15px;
  font-size: 13px;
  color: #888;
}

.article-meta span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.article-meta i {
  font-size: 12px;
}

/* 文章箭头 */
.article-arrow {
  margin-left: 15px;
  color: #ccc;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.article-item:hover .article-arrow {
  color: #3498db;
  transform: translateX(5px);
}

.link-description {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 8px;
}

.link-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #95a5a6;
}

.link-actions {
  display: flex;
  gap: 5px;
  margin-left: 10px;
}

/* 添加链接区域 */
.add-link-section {
  text-align: center;
  padding: 15px;
  border: 2px dashed #ecf0f1;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.add-link-section:hover {
  border-color: #3498db;
  background: #f8f9fa;
}

/* 模块统计 */
.module-stats {
  display: flex;
  justify-content: space-between;
  padding: 15px 20px;
  background: #f8f9fa;
  border-top: 1px solid #ecf0f1;
  font-size: 12px;
  color: #7f8c8d;
}

/* 空状态 */
.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: #7f8c8d;
}

.empty-state i {
  font-size: 64px;
  margin-bottom: 20px;
  color: #bdc3c7;
}

.empty-state h3 {
  margin-bottom: 10px;
  color: #2c3e50;
}

.empty-state p {
  margin-bottom: 30px;
  font-size: 16px;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #ecf0f1;
}

.modal-header h3 {
  color: #2c3e50;
  margin: 0;
}

.modal-body {
  padding: 20px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  margin-top: 20px;
}

/* 表单样式 */
.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #ecf0f1;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #3498db;
}

.form-group small {
  display: block;
  margin-top: 5px;
  color: #7f8c8d;
  font-size: 12px;
}

.btn-small {
  padding: 5px 10px;
  font-size: 12px;
  margin-top: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-area {
    margin-left: 0;
    padding: 15px;
  }

  .page-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .header-actions {
    width: 100%;
    justify-content: center;
  }

  .articles-list {
    gap: 10px;
  }

  .article-item {
    padding: 15px;
  }

  .article-icon {
    width: 40px;
    height: 40px;
    margin-right: 15px;
  }

  .article-title h4 {
    font-size: 16px;
  }

  .filter-tabs {
    justify-content: center;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    margin: 20px;
  }
}

@media (max-width: 480px) {
  .link-item {
    flex-direction: column;
    gap: 10px;
  }

  .link-actions {
    margin-left: 0;
    justify-content: flex-end;
  }

  .module-header {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .module-actions {
    justify-content: center;
  }
}
</style>
