<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字字母序列训练 - 注意力评估系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #2c3e50;
            line-height: 1.6;
        }

        .game-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 90%;
            text-align: center;
            border: 1px solid #e9ecef;
        }

        .header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .header h1 {
            color: #2c5aa0;
            font-size: 1.8rem;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .header p {
            color: #6c757d;
            font-size: 1rem;
            margin-bottom: 0;
        }

        .stats-container {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .stat-item {
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #2c5aa0;
            display: block;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            margin-top: 5px;
        }

        .round-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 25px;
            border-left: 4px solid #2c5aa0;
        }

        .round-text {
            font-size: 1.1rem;
            color: #1565c0;
            font-weight: 500;
        }

        .sequence-area {
            background: #ffffff;
            border: 2px solid #2c5aa0;
            border-radius: 8px;
            padding: 30px;
            margin: 25px 0;
            font-size: 2rem;
            font-weight: bold;
            color: #2c5aa0;
            min-height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            letter-spacing: 8px;
        }

        .input-section {
            margin: 25px 0;
        }

        .input-label {
            display: block;
            margin-bottom: 10px;
            font-weight: 500;
            color: #495057;
        }

        .sequence-input {
            width: 100%;
            padding: 15px;
            font-size: 1.2rem;
            border: 2px solid #ced4da;
            border-radius: 6px;
            text-align: center;
            letter-spacing: 4px;
            font-weight: bold;
            transition: border-color 0.3s ease;
        }

        .sequence-input:focus {
            outline: none;
            border-color: #2c5aa0;
            box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
        }

        .button-group {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 25px;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 1rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            min-width: 120px;
        }

        .btn-primary {
            background: #2c5aa0;
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: #1e3d72;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover:not(:disabled) {
            background: #545b62;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover:not(:disabled) {
            background: #1e7e34;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .feedback {
            margin: 20px 0;
            padding: 15px;
            border-radius: 6px;
            font-weight: 500;
            min-height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .feedback.correct {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .feedback.incorrect {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .timer-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
            display: none;
        }

        .timer-text {
            color: #856404;
            font-weight: 500;
            margin-bottom: 10px;
        }

        .countdown {
            font-size: 2rem;
            font-weight: bold;
            color: #d63031;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin-top: 10px;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #2c5aa0, #4a90e2);
            transition: width 0.3s ease;
            border-radius: 4px;
        }

        @media (max-width: 768px) {
            .game-container {
                padding: 20px;
                margin: 20px;
            }
            
            .stats-container {
                flex-direction: column;
                gap: 15px;
            }
            
            .button-group {
                flex-direction: column;
            }
            
            .sequence-area {
                font-size: 1.5rem;
                letter-spacing: 4px;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <div class="header">
            <h1>🧠 数字字母序列训练</h1>
            <p>注意力与工作记忆评估训练系统</p>
        </div>

        <div class="stats-container">
            <div class="stat-item">
                <span class="stat-value" id="scoreValue">0</span>
                <div class="stat-label">得分</div>
            </div>
            <div class="stat-item">
                <span class="stat-value" id="streakValue">0</span>
                <div class="stat-label">连击</div>
            </div>
            <div class="stat-item">
                <span class="stat-value" id="accuracyValue">0%</span>
                <div class="stat-label">正确率</div>
            </div>
        </div>

        <div class="round-info">
            <div class="round-text" id="roundText">点击开始按钮开始训练</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
        </div>

        <div class="sequence-area" id="sequenceArea">
            准备开始训练
        </div>

        <div class="timer-box" id="timerBox">
            <div class="timer-text">记忆时间剩余</div>
            <div class="countdown" id="countdown">5</div>
        </div>

        <div class="input-section">
            <label class="input-label" for="sequenceInput">请按倒序输入刚才看到的序列：</label>
            <input type="text" id="sequenceInput" class="sequence-input" 
                   placeholder="例如：L 7 5" disabled>
        </div>

        <div class="feedback" id="feedback"></div>

        <div class="button-group">
            <button id="startBtn" class="btn btn-primary">开始训练</button>
            <button id="submitBtn" class="btn btn-success" disabled>提交答案</button>
            <button id="nextBtn" class="btn btn-secondary" style="display: none;">下一题</button>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            score: 0,
            streak: 0,
            level: 1,
            gameActive: false,
            currentRound: 1,
            totalRounds: 10,
            currentSequence: '',
            correctSequence: '',
            totalQuestions: 0,
            correctAnswers: 0,
            showingSequence: false
        };

        // 生成随机序列
        function generateSequence() {
            const numbers = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];
            const letters = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

            // 根据轮次确定序列长度：从3开始，逐渐增加到6
            let sequenceLength = Math.min(3 + Math.floor((gameState.currentRound - 1) / 2), 6);

            let sequence = [];
            let allItems = [...numbers, ...letters];

            for (let i = 0; i < sequenceLength; i++) {
                const randomIndex = Math.floor(Math.random() * allItems.length);
                sequence.push(allItems[randomIndex]);
                allItems.splice(randomIndex, 1); // 避免重复
            }

            gameState.currentSequence = sequence.join(' ');
            gameState.correctSequence = sequence.reverse().join(' ');
        }

        // 开始游戏
        function startGame() {
            gameState = {
                score: 0,
                streak: 0,
                level: 1,
                gameActive: true,
                currentRound: 1,
                totalRounds: 10,
                currentSequence: '',
                correctSequence: '',
                totalQuestions: 0,
                correctAnswers: 0,
                showingSequence: false
            };

            document.getElementById('startBtn').disabled = true;
            document.getElementById('feedback').textContent = '';
            document.getElementById('feedback').className = 'feedback';

            nextRound();
        }

        // 下一轮
        function nextRound() {
            if (gameState.currentRound > gameState.totalRounds) {
                endGame();
                return;
            }

            generateSequence();
            gameState.showingSequence = true;

            document.getElementById('roundText').textContent =
                `第${gameState.currentRound}轮/共${gameState.totalRounds}轮 - 记住序列并倒序输入`;

            const sequenceArea = document.getElementById('sequenceArea');
            sequenceArea.textContent = gameState.currentSequence;
            sequenceArea.style.background = '#e3f2fd';
            sequenceArea.style.animation = 'sequencePulse 1s ease-in-out infinite';

            document.getElementById('sequenceInput').value = '';
            document.getElementById('feedback').textContent = '';
            document.getElementById('feedback').className = 'feedback';
            document.getElementById('nextBtn').style.display = 'none';

            document.getElementById('submitBtn').disabled = true;
            document.getElementById('sequenceInput').disabled = true;

            showSequenceTimer();
            updateDisplay();
        }

        // 显示序列计时器
        function showSequenceTimer() {
            const timerBox = document.getElementById('timerBox');
            const countdown = document.getElementById('countdown');

            timerBox.style.display = 'block';
            let timeLeft = 5;
            countdown.textContent = timeLeft;

            const timer = setInterval(() => {
                timeLeft--;
                if (timeLeft > 0) {
                    countdown.textContent = timeLeft;
                } else {
                    clearInterval(timer);
                    hideSequenceAndStartInput();
                }
            }, 1000);
        }

        // 隐藏序列并开始输入
        function hideSequenceAndStartInput() {
            gameState.showingSequence = false;

            const sequenceArea = document.getElementById('sequenceArea');
            sequenceArea.textContent = '请按倒序输入刚才看到的序列';
            sequenceArea.style.background = '#ffffff';
            sequenceArea.style.animation = 'none';
            document.getElementById('timerBox').style.display = 'none';

            document.getElementById('submitBtn').disabled = false;
            document.getElementById('sequenceInput').disabled = false;
            document.getElementById('sequenceInput').focus();

            updateDisplay();
        }

        // 检查答案
        function checkAnswer() {
            const userInput = document.getElementById('sequenceInput').value.trim();
            const feedback = document.getElementById('feedback');

            // 更强大的标准化处理函数
            const normalizeAnswer = (answer) => {
                // 1. 转换为大写
                let normalized = answer.toUpperCase();
                // 2. 去除所有空格
                normalized = normalized.replace(/\s+/g, '');
                // 3. 将连续字符转换为空格分隔的格式
                normalized = normalized.split('').join(' ');
                return normalized.trim();
            };

            const userAnswer = normalizeAnswer(userInput);
            const correctAnswer = normalizeAnswer(gameState.correctSequence);

            gameState.totalQuestions++;

            console.log('用户原始输入:', userInput);
            console.log('正确答案原始:', gameState.correctSequence);
            console.log('标准化用户答案:', userAnswer);
            console.log('标准化正确答案:', correctAnswer);
            console.log('是否匹配:', userAnswer === correctAnswer);

            if (userAnswer === correctAnswer) {
                const points = 10 * gameState.currentRound;
                gameState.score += points;
                gameState.streak++;
                gameState.correctAnswers++;
                feedback.textContent = `✓ 回答正确！获得 ${points} 分`;
                feedback.className = 'feedback correct';
            } else {
                gameState.streak = 0;
                feedback.textContent = `✗ 回答错误。正确答案是：${gameState.correctSequence}`;
                feedback.className = 'feedback incorrect';
            }

            gameState.currentRound++;

            document.getElementById('submitBtn').disabled = true;
            document.getElementById('sequenceInput').disabled = true;
            document.getElementById('nextBtn').style.display = 'inline-block';
            document.getElementById('nextBtn').disabled = false;

            updateDisplay();
        }

        // 更新显示
        function updateDisplay() {
            document.getElementById('scoreValue').textContent = gameState.score;
            document.getElementById('streakValue').textContent = gameState.streak;

            const accuracy = gameState.totalQuestions > 0 ?
                Math.round((gameState.correctAnswers / gameState.totalQuestions) * 100) : 0;
            document.getElementById('accuracyValue').textContent = accuracy + '%';

            const progress = ((gameState.currentRound - 1) / gameState.totalRounds) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 结束游戏
        function endGame() {
            gameState.gameActive = false;

            const accuracy = gameState.totalQuestions > 0 ?
                Math.round((gameState.correctAnswers / gameState.totalQuestions) * 100) : 0;

            document.getElementById('sequenceArea').textContent = '🎊 训练完成！';
            document.getElementById('roundText').textContent =
                `训练完成！正确率: ${accuracy}% (${gameState.correctAnswers}/${gameState.totalQuestions})`;
            document.getElementById('feedback').textContent = `最终得分: ${gameState.score} 分`;
            document.getElementById('feedback').className = 'feedback correct';

            document.getElementById('startBtn').disabled = false;
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('sequenceInput').disabled = true;
            document.getElementById('nextBtn').style.display = 'none';
            document.getElementById('timerBox').style.display = 'none';

            // 发送结果给父窗口
            if (window.opener) {
                window.opener.postMessage({
                    type: 'gameEnd',
                    score: gameState.score,
                    accuracy: accuracy,
                    totalQuestions: gameState.totalQuestions,
                    correctAnswers: gameState.correctAnswers
                }, '*');
            }
        }

        // 事件监听器
        document.getElementById('startBtn').addEventListener('click', startGame);
        document.getElementById('submitBtn').addEventListener('click', checkAnswer);
        document.getElementById('nextBtn').addEventListener('click', nextRound);

        // 回车键提交
        document.getElementById('sequenceInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !document.getElementById('submitBtn').disabled) {
                checkAnswer();
            }
        });

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes sequencePulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.02); }
                100% { transform: scale(1); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
