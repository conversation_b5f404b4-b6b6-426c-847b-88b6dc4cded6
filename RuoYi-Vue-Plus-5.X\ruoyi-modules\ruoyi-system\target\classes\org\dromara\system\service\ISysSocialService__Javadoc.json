{"doc": " 社会化关系Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.String"], "doc": " 查询社会化关系\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": " 查询社会化关系列表\n"}, {"name": "queryListByUserId", "paramTypes": ["java.lang.Long"], "doc": " 查询社会化关系列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": " 新增授权关系\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysSocialBo"], "doc": " 更新社会化关系\n"}, {"name": "deleteWithValidById", "paramTypes": ["java.lang.Long"], "doc": " 删除社会化关系信息\n"}, {"name": "selectByAuthId", "paramTypes": ["java.lang.String"], "doc": " 根据 authId 查询\n"}], "constructors": []}