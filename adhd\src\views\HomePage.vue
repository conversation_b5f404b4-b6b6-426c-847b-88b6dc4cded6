<template>
  <div class="page-container">
    <!-- 公共头部组件 -->
    <AppHeader
      :user="currentUser"
      current-route="homepage"
      @search="handleSearch"
      @show-notifications="showNotifications"
      @show-messages="showMessages"
      @show-settings="showSettings"
      @show-user-menu="showUserMenu"
    />

    <!-- 内容包装区 -->
    <div class="content-wrapper">
      <!-- 公共侧边栏组件 -->
      <AppSidebar
        current-route="homepage"
        :expanded="navExpanded"
        @expand="expandNav"
        @collapse="collapseNav"
      />

      <!-- 主内容区 -->
      <div class="main-area">
        <div class="page-main-container">
          <!-- 页面标题 -->
          <div class="area-header">
            <h3><i class="fas fa-home"></i> 个人中心首页</h3>
            <p class="welcome-text">欢迎回来，{{ currentUser?.patientName || currentUser?.username || '用户' }}！</p>
          </div>

          <!-- 主要内容 -->
          <div class="main-content">
            <!-- 功能区域 - 移到顶部 -->
            <div class="top-features-row">
              <!-- 个人档案卡片 - 移到左边 -->
              <div class="feature-wrapper medium">
                <div class="feature-header">
                  <h3><i class="fas fa-user"></i> 个人档案</h3>
                  <div class="status-badge online">
                    <span class="status-dot"></span>
                    在线
                  </div>
                </div>
                <div class="feature-body">
                  <div class="profile-content">
                    <div class="avatar-section">
                      <div class="user-avatar">
                        {{ currentUser?.patientName?.charAt(0) || currentUser?.username?.charAt(0) || 'U' }}
                      </div>
                      <div class="user-info">
                        <h4>{{ currentUser?.patientName || currentUser?.username || '用户' }}</h4>
                        <p class="user-meta">
                          <span class="age-info">{{ currentUser?.age ? currentUser.age + '岁' : '年龄未知' }}</span>
                          <span class="gender-info">{{ currentUser?.gender === '0' ? '男' : currentUser?.gender === '1' ? '女' : '' }}</span>
                        </p>
                      </div>
                    </div>
                    <div class="progress-summary">
                      <div class="progress-item">
                        <span class="progress-label">训练进度</span>
                        <div class="progress-bar">
                          <div class="progress-fill" :style="{ width: '65%' }"></div>
                        </div>
                        <span class="progress-value">65%</span>
                      </div>
                      <div class="user-level">
                        <span class="level-badge">Lv.{{ Math.floor(patientStats[1]?.value / 10) + 1 }}</span>
                        <span class="exp-text">经验值 {{ (patientStats[1]?.value * 10) % 100 }}/100</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 快捷功能访问 - 移到右边 -->
              <div class="feature-wrapper large">
                <div class="feature-header">
                  <h3><i class="fas fa-rocket"></i> 快捷功能</h3>
                  <div class="feature-controls">
                    <div class="time-info">
                      <div class="current-time">{{ currentTime }}</div>
                      <div class="current-date">{{ currentDate }}</div>
                    </div>
                  </div>
                </div>
                <div class="feature-body">
                  <div class="quick-actions-grid">
                    <div v-for="action in quickActions.slice(0, 6)" :key="action.id" 
                         class="action-item" 
                         @click="executeAction(action)">
                      <div class="action-icon" :style="{ backgroundColor: action.color + '20', color: action.color }">
                        <span class="action-icon-text">{{ action.iconText }}</span>
                      </div>
                      <div class="action-content">
                        <h4>{{ action.name }}</h4>
                        <p>{{ action.description }}</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 个人数据概览卡片 -->
            <div class="data-overview-cards">
              <div v-for="stat in patientStats" :key="stat.id" class="card">
                <div class="card-icon" :style="{ backgroundColor: stat.color + '20', color: stat.color }">
                  <span class="card-icon-text">{{ stat.iconText }}</span>
                </div>
                <div class="card-content">
                  <h3>{{ stat.label }}</h3>
                  <p class="card-value">
                    <animated-number 
                      :value="stat.value" 
                      :duration="1500"
                      :format="stat.format"
                    />
                  </p>
                  <span :class="['card-trend', stat.trend]">{{ stat.change }}</span>
                </div>
              </div>
            </div>

            <!-- 功能区域 -->
            <div class="features-container">
              <!-- 第二行：推荐功能和训练记录 -->
              <div class="feature-row">
                <!-- 推荐功能 -->
                <div class="feature-wrapper medium">
                  <div class="feature-header">
                    <h3><i class="fas fa-lightbulb"></i> 为您推荐</h3>
                    <span class="recommendation-badge">个性化</span>
                  </div>
                  <div class="feature-body">
                    <div class="recommendation-list">
                      <div v-for="feature in recommendedFeatures" :key="feature.id" class="recommendation-item">
                        <div class="rec-icon" :style="{ backgroundColor: feature.color + '20', color: feature.color }">
                          <span class="rec-icon-text">{{ feature.iconText }}</span>
                        </div>
                        <div class="rec-content">
                          <h4>{{ feature.name }}</h4>
                          <p>{{ feature.description }}</p>
                          <div class="rec-progress" v-if="feature.progress !== undefined">
                            <div class="progress-circle-mini">
                              <span>{{ feature.progress }}%</span>
                            </div>
                          </div>
                        </div>
                        <div class="rec-action">
                          <button class="rec-btn" @click="startFeature(feature)">
                            {{ feature.buttonText || '开始' }}
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 训练记录 -->
                <div class="feature-wrapper medium">
                  <div class="feature-header">
                    <h3><i class="fas fa-history"></i> 最近训练</h3>
                  </div>
                  <div class="feature-body">
                    <div class="training-summary">
                      <div class="summary-stats">
                        <div class="stat-item">
                          <span class="stat-label">本周训练</span>
                          <span class="stat-value">{{ weeklyTraining.sessions }}次</span>
                        </div>
                        <div class="stat-item">
                          <span class="stat-label">平均专注</span>
                          <span class="stat-value">{{ weeklyTraining.avgFocusTime }}分钟</span>
                        </div>
                        <div class="stat-item">
                          <span class="stat-label">完成率</span>
                          <span class="stat-value">{{ weeklyTraining.completionRate }}%</span>
                        </div>
                      </div>
                      <div class="recent-activities">
                        <h5>近期活动</h5>
                        <div class="activity-list">
                          <div class="activity-item">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                              <span class="activity-title">完成注意力训练</span>
                              <span class="activity-time">2小时前</span>
                            </div>
                          </div>
                          <div class="activity-item">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                              <span class="activity-title">参与量表筛查</span>
                              <span class="activity-time">昨天</span>
                            </div>
                          </div>
                          <div class="activity-item">
                            <div class="activity-dot"></div>
                            <div class="activity-content">
                              <span class="activity-title">查看康复计划</span>
                              <span class="activity-time">3天前</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import AnimatedNumber from '@/components/AnimatedNumber.vue'
import AppHeader from '@/components/common/AppHeader.vue'
import AppSidebar from '@/components/common/AppSidebar.vue'
import { localStorageUtils } from '@/api/authAPI.js'

const router = useRouter()

// 系统布局相关
const navExpanded = ref(false)
const currentUser = computed(() => {
  const user = localStorageUtils.getUser()
  return user || null
})

// 布局相关方法
const expandNav = () => {
  navExpanded.value = true
}

const collapseNav = () => {
  navExpanded.value = false
}

const handleSearch = (query) => {
  console.log('搜索:', query)
}

const showNotifications = () => {
  ElMessage.info('显示通知')
}

const showMessages = () => {
  ElMessage.info('显示消息')
}

const showSettings = () => {
  router.push('/settings')
}

const showUserMenu = () => {
  ElMessage.info('显示用户菜单')
}

// 响应式数据
const currentTime = ref('')
const currentDate = ref('')

// 患者个人统计数据
const patientStats = ref([
  {
    id: 1,
    label: '训练总时长',
    value: 45,
    format: (val) => val + '小时',
    color: '#409EFF',
    iconText: '时长',
    trend: 'up',
    change: '+5小时'
  },
  {
    id: 2,
    label: '完成训练数',
    value: 28,
    format: (val) => val.toString(),
    color: '#67C23A',
    iconText: '训练',
    trend: 'up',
    change: '+3次'
  },
  {
    id: 3,
    label: '连续训练天数',
    value: 12,
    format: (val) => val + '天',
    color: '#E6A23C',
    iconText: '天数',
    trend: 'up',
    change: '+2天'
  },
  {
    id: 4,
    label: '平均专注度',
    value: 85,
    format: (val) => val + '%',
    color: '#F56C6C',
    iconText: '专注',
    trend: 'up',
    change: '+8%'
  },
  {
    id: 5,
    label: '康复进度',
    value: 68,
    format: (val) => val + '%',
    color: '#909399',
    iconText: '进度',
    trend: 'up',
    change: '+12%'
  },
  {
    id: 6,
    label: '情绪稳定性',
    value: 92,
    format: (val) => val + '分',
    color: '#606266',
    iconText: '情绪',
    trend: 'up',
    change: '+5分'
  }
])

// 快速操作
const quickActions = ref([
  {
    id: 1,
    name: '量表筛查',
    description: '心理健康量表测试',
    iconText: '量表',
    color: '#409EFF',
    route: '/'
  },
  {
    id: 2,
    name: '智能筛查',
    description: 'AI情绪识别分析',
    iconText: '筛查',
    color: '#67C23A',
    route: '/home'
  },
  {
    id: 3,
    name: '康复计划',
    description: 'AI智能康复训练',
    iconText: '康复',
    color: '#E6A23C',
    route: '/ai-rehabilitation'
  },
  {
    id: 4,
    name: '在线咨询',
    description: '专业心理咨询服务',
    iconText: '咨询',
    color: '#F56C6C',
    route: '/online-consultation'
  },
  {
    id: 5,
    name: '注意力游戏',
    description: '注意力训练小游戏',
    iconText: '游戏',
    color: '#909399',
    route: '/attention-games'
  },
  {
    id: 6,
    name: '知识课程',
    description: '心理健康知识学习',
    iconText: '课程',
    color: '#606266',
    route: '/knowledge-dissemination'
  }
])

// 推荐功能
const recommendedFeatures = ref([
  {
    id: 1,
    name: '注意力评估',
    description: '基于最新测试结果的个性化评估',
    iconText: '评估',
    color: '#409EFF',
    progress: 75,
    buttonText: '继续'
  },
  {
    id: 2,
    name: '专注力训练',
    description: '提升专注力的个性化训练方案',
    iconText: '训练',
    color: '#67C23A',
    progress: 45,
    buttonText: '开始'
  },
  {
    id: 3,
    name: '情绪管理',
    description: '情绪调节技巧学习和练习',
    iconText: '情绪',
    color: '#E6A23C',
    progress: undefined,
    buttonText: '开始'
  }
])

// 训练数据
const weeklyTraining = reactive({
  sessions: 8,
  avgFocusTime: 25,
  completionRate: 87
})

// 方法
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

const executeAction = (action) => {
  if (action.route) {
    router.push(action.route)
  } else {
    ElMessage.info(`执行操作：${action.name}`)
  }
}

const startFeature = (feature) => {
  if (feature.name === '注意力评估') {
    router.push('/')
  } else if (feature.name === '专注力训练') {
    router.push('/attention-games')
  } else if (feature.name === '情绪管理') {
    router.push('/home')
  } else {
    ElMessage.info(`开始${feature.name}`)
  }
}

// 生命周期
onMounted(() => {
  updateTime()
  // 每秒更新时间
  setInterval(updateTime, 1000)
  
  // 模拟训练数据更新
  setInterval(() => {
    // 随机更新一些训练相关数据，模拟实时性
    if (Math.random() > 0.7) {
      weeklyTraining.avgFocusTime = 20 + Math.floor(Math.random() * 15)
    }
  }, 10000)
})
</script>

<style scoped>
/* 继承系统全局样式 */
.page-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background: #f8f9fa;
}

.content-wrapper {
  display: flex;
  flex: 1;
  background: #f8f9fa;
}

.main-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 参照数据分析页面的布局样式 */
.page-main-container {
  flex: 1;
  padding: 20px;
  background: #f8f9fa;
  overflow-y: auto;
}

/* 页面标题 */
.area-header {
  margin-bottom: 20px;
}

.area-header h3 {
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.area-header h3 i {
  color: #409EFF;
  font-size: 22px;
}

.welcome-text {
  font-size: 16px;
  color: #7f8fa6;
  margin: 0;
}

.main-content {
  width: 100%;
  height: 100%;
  overflow-y: auto;
}

/* 数据概览卡片 - 参照数据分析页面 */
.data-overview-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  padding: 24px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4e73df, #36b9cc);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover::before {
  opacity: 1;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  position: relative;
}

.card-icon-text {
  font-size: 14px;
  font-weight: 600;
  color: inherit;
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: 14px;
  color: #8c8c8c;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 6px 0;
}

.card-trend {
  font-size: 12px;
  font-weight: 500;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.card-trend.up {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.card-trend.down {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.card-trend.neutral {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

/* 功能区域布局 */
.top-features-row {
  display: flex;
  gap: 20px;
  align-items: stretch;
  margin-bottom: 30px;
}

.features-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-row {
  display: flex;
  gap: 20px;
  align-items: stretch;
}

.feature-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  border: 1px solid #f0f0f0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.feature-wrapper:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.feature-wrapper.large {
  flex: 2;
}

.feature-wrapper.medium {
  flex: 1;
}

.feature-header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.feature-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-header h3 i {
  color: #409EFF;
  font-size: 14px;
}

.feature-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.time-info {
  text-align: right;
}

.current-time {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  font-family: 'Courier New', monospace;
}

.current-date {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 2px;
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.online {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.status-dot {
  width: 6px;
  height: 6px;
  background: #22c55e;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.recommendation-badge {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.feature-body {
  padding: 24px;
}

/* 快捷功能网格 */
.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.action-item {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 20px 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #e9ecef;
}

.action-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 12px;
}

.action-icon-text {
  font-size: 14px;
  font-weight: 600;
  color: inherit;
}

.action-content h4 {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.action-content p {
  font-size: 12px;
  color: #8c8c8c;
  margin: 0;
  line-height: 1.4;
}

/* 个人档案样式 */
.profile-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.avatar-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
  font-weight: 600;
  box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
}

.user-info h4 {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 6px 0;
}

.user-meta {
  display: flex;
  gap: 8px;
  margin: 0;
}

.age-info, .gender-info {
  background: rgba(64, 158, 255, 0.1);
  color: #409EFF;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.gender-info {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.progress-summary {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.progress-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.progress-label {
  font-size: 14px;
  color: #6b7280;
  min-width: 60px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
  border-radius: 3px;
  transition: width 1s ease-out;
}

.progress-value {
  font-size: 12px;
  font-weight: 500;
  color: #6b7280;
  min-width: 35px;
  text-align: right;
}

.user-level {
  display: flex;
  align-items: center;
  gap: 8px;
}

.level-badge {
  background: linear-gradient(135deg, #3b82f6, #8b5cf6);
  color: white;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
}

.exp-text {
  font-size: 12px;
  color: #8c8c8c;
}

/* 推荐功能列表 */
.recommendation-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
  transition: all 0.3s ease;
}

.recommendation-item:hover {
  background: #e9ecef;
  transform: translateX(4px);
}

.rec-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.rec-icon-text {
  font-size: 12px;
  font-weight: 600;
  color: inherit;
}

.rec-content {
  flex: 1;
}

.rec-content h4 {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 4px 0;
}

.rec-content p {
  font-size: 12px;
  color: #8c8c8c;
  margin: 0;
  line-height: 1.4;
}

.rec-progress {
  margin-top: 6px;
}

.progress-circle-mini {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: rgba(64, 158, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: 600;
  color: #409EFF;
}

.rec-action {
  flex-shrink: 0;
}

.rec-btn {
  background: #409EFF;
  color: white;
  border: none;
  padding: 6px 16px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.rec-btn:hover {
  background: #3b82f6;
  transform: translateY(-1px);
}

/* 训练记录样式 */
.training-summary {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.summary-stats {
  display: flex;
  justify-content: space-between;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 12px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
  display: block;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.recent-activities h5 {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 12px 0;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.activity-dot {
  width: 8px;
  height: 8px;
  background: #409EFF;
  border-radius: 50%;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-title {
  font-size: 13px;
  color: #2c3e50;
  font-weight: 500;
}

.activity-time {
  font-size: 12px;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .data-overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .top-features-row,
  .feature-row {
    flex-direction: column;
  }
  
  .quick-actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .page-main-container {
    padding: 15px;
  }
  
  .data-overview-cards {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .card {
    padding: 20px;
  }
  
  .card-icon {
    width: 50px;
    height: 50px;
    margin-right: 15px;
  }
  
  .card-value {
    font-size: 24px;
  }
  
  .quick-actions-grid {
    grid-template-columns: 1fr;
  }
  
  .avatar-section {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .summary-stats {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .area-header h3 {
    font-size: 20px;
  }
  
  .card-value {
    font-size: 20px;
  }
  
  .feature-body {
    padding: 16px;
  }
  
  .action-item {
    padding: 16px 12px;
  }
}
</style>
