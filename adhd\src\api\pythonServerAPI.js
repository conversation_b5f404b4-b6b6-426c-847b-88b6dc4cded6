/**
 * Python服务器管理API
 * 用于启动和停止Python WebRTC服务器
 */

class PythonServerAPI {
  constructor() {
    this.serverProcess = null;
    this.serverStatus = 'stopped'; // stopped, starting, running, error
  }

  /**
   * 启动Python服务器
   */
  async startServer() {
    try {
      console.log('🐍 尝试启动Python WebRTC服务器...');

      // 检查服务器是否已经运行
      const isRunning = await this.checkServerHealth();
      if (isRunning) {
        console.log('✅ Python服务器已在运行');
        this.serverStatus = 'running';
        return { success: true, message: 'Python服务器已在运行' };
      }

      this.serverStatus = 'starting';

      // 方案1: 通过后端API启动（推荐）
      try {
        console.log('📡 通过后端API启动Python服务器...');
        const response = await fetch('http://localhost:3001/api/python-server/start', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            script: 'python/start_webrtc_server.py',
            port: 8765
          })
        });

        const result = await response.json();

        if (response.ok && result.success) {
          this.serverStatus = 'running';
          console.log('✅ Python服务器启动成功');
          return { success: true, message: result.message };
        } else {
          throw new Error(result.message || '后端API启动失败');
        }
      } catch (apiError) {
        console.warn('后端API启动失败:', apiError.message);

        // 方案2: 检查后端API是否运行
        try {
          const healthResponse = await fetch('http://localhost:3001/api/health', {
            method: 'GET',
            timeout: 3000
          });

          if (!healthResponse.ok) {
            throw new Error('后端API服务器未运行');
          }
        } catch (healthError) {
          console.error('后端API服务器检查失败:', healthError);
          this.serverStatus = 'error';
          return {
            success: false,
            message: '后端API服务器未运行。请先启动后端服务器:\n\n1. 打开新的命令行窗口\n2. 进入server目录: cd server\n3. 安装依赖: npm install\n4. 启动后端: npm start\n\n然后重试启动Python服务器',
            requiresBackendStart: true
          };
        }

        // 方案3: 提示用户手动启动
        this.serverStatus = 'error';
        return {
          success: false,
          message: `启动失败: ${apiError.message}\n\n手动启动方法:\n1. 打开命令行\n2. 进入项目目录\n3. 运行: python python/start_webrtc_server.py`,
          requiresManualStart: true
        };
      }

    } catch (error) {
      console.error('❌ 启动Python服务器失败:', error);
      this.serverStatus = 'error';
      return {
        success: false,
        message: `启动失败: ${error.message}`
      };
    }
  }

  /**
   * 停止Python服务器
   */
  async stopServer() {
    try {
      console.log('🛑 停止Python服务器...');

      // 通过后端API停止
      try {
        const response = await fetch('http://localhost:3001/api/python-server/stop', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          }
        });

        const result = await response.json();

        if (response.ok && result.success) {
          this.serverStatus = 'stopped';
          console.log('✅ Python服务器已停止');
          return { success: true, message: result.message };
        } else {
          throw new Error(result.message || '后端API停止失败');
        }
      } catch (apiError) {
        console.warn('后端API停止失败:', apiError.message);

        // 如果API不可用，标记为已停止（用户需要手动停止）
        this.serverStatus = 'stopped';
        return {
          success: true,
          message: '后端API不可用，请手动停止Python服务器（Ctrl+C）',
          requiresManualStop: true
        };
      }

    } catch (error) {
      console.error('❌ 停止Python服务器失败:', error);
      return {
        success: false,
        message: `停止失败: ${error.message}`
      };
    }
  }

  /**
   * 检查服务器健康状态
   */
  async checkServerHealth() {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 3000);

      const response = await fetch('http://localhost:8765/health', {
        method: 'GET',
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  /**
   * 等待服务器就绪
   */
  async waitForServerReady(maxAttempts = 15, interval = 1000) {
    console.log(`⏳ 等待Python服务器就绪 (最多${maxAttempts}秒)...`);
    
    for (let i = 0; i < maxAttempts; i++) {
      const isReady = await this.checkServerHealth();
      if (isReady) {
        console.log('✅ Python服务器已就绪');
        this.serverStatus = 'running';
        return true;
      }
      
      console.log(`⏳ 等待中... (${i + 1}/${maxAttempts})`);
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    console.error('❌ Python服务器启动超时');
    this.serverStatus = 'error';
    throw new Error('Python服务器启动超时');
  }

  /**
   * 获取服务器状态
   */
  getStatus() {
    return this.serverStatus;
  }

  /**
   * 获取详细服务器状态（通过后端API）
   */
  async getDetailedStatus() {
    try {
      const response = await fetch('http://localhost:3001/api/python-server/status', {
        method: 'GET'
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          // 更新本地状态
          this.serverStatus = result.status.status;
          return result.status;
        }
      }

      throw new Error('无法获取服务器状态');
    } catch (error) {
      console.warn('获取详细状态失败:', error.message);
      return {
        status: this.serverStatus,
        isHealthy: false,
        error: error.message
      };
    }
  }

  /**
   * 获取服务器信息
   */
  async getServerInfo() {
    try {
      const response = await fetch('http://localhost:8765/info', {
        method: 'GET'
      });

      if (response.ok) {
        return await response.json();
      } else {
        throw new Error('无法获取服务器信息');
      }
    } catch (error) {
      return {
        status: 'unknown',
        message: error.message
      };
    }
  }

  /**
   * 自动启动服务器（带重试）
   */
  async autoStartServer(maxRetries = 3) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      console.log(`🔄 第${attempt}次尝试启动Python服务器...`);
      
      const result = await this.startServer();
      
      if (result.success) {
        // 等待服务器就绪
        try {
          await this.waitForServerReady();
          return { success: true, message: 'Python服务器启动成功' };
        } catch (error) {
          console.warn(`第${attempt}次启动失败:`, error.message);
          if (attempt === maxRetries) {
            return { success: false, message: error.message };
          }
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      } else {
        if (result.requiresManualStart || attempt === maxRetries) {
          return result;
        }
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }
}

// 创建单例实例
const pythonServerAPI = new PythonServerAPI();

export default pythonServerAPI;
