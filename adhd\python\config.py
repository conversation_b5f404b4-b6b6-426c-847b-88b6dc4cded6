"""
增强版发呆检测系统配置文件
针对眼动模型 + MediaPipe面部关键点优化的参数
"""

# MediaPipe 配置
MEDIAPIPE_CONFIG = {
    'static_image_mode': False,
    'max_num_faces': 1,
    'refine_landmarks': True,
    'min_detection_confidence': 0.7,
    'min_tracking_confidence': 0.5
}

# 眼动追踪配置
EYE_TRACKING_CONFIG = {
    'history_size': 30,
    'blink_threshold': 0.25,  # EAR阈值
    'normal_blink_rate_min': 8,   # 正常眨眼频率范围
    'normal_blink_rate_max': 25,
    'gaze_stability_threshold': 0.8,
    'fixation_duration_threshold': 3.0,  # 秒
    'pupil_movement_threshold': 5.0,
    'eye_openness_min': 0.2,
    'eye_openness_max': 0.8,
    'gaze_movement_threshold': 20  # 像素
}

# 面部分析配置
FACE_ANALYSIS_CONFIG = {
    'history_size': 30,
    'expression_change_threshold': 0.1,
    'head_pose_stability_threshold': 0.9,
    'facial_tension_threshold': 0.3
}

# 发呆检测配置
DAYDREAM_DETECTION_CONFIG = {
    'threshold': 0.6,
    'history_size': 10,
    'weights': {
        'eye_score': 0.5,      # 眼动模型权重
        'face_score': 0.3,     # 面部识别权重
        'motion_score': 0.2    # 动作识别权重
    }
}

# 眼部评分权重
EYE_SCORE_WEIGHTS = {
    'blink_rate_abnormal': 0.3,
    'gaze_stability_high': 0.4,
    'fixation_duration_long': 0.3,
    'pupil_movement_low': 0.2,
    'eye_openness_abnormal': 0.1
}

# 面部评分权重
FACE_SCORE_WEIGHTS = {
    'expression_change_low': 0.4,
    'head_pose_stability_high': 0.4,
    'facial_tension_low': 0.2
}

# 显示配置
DISPLAY_CONFIG = {
    'show_landmarks': True,
    'show_eye_points': True,
    'show_iris_points': True,
    'landmark_color': (0, 255, 0),  # 绿色
    'iris_color': (0, 0, 255),      # 红色
    'eye_point_size': 2,
    'iris_point_size': 3,
    'text_color': (255, 255, 255),
    'status_color_normal': (0, 255, 0),
    'status_color_daydream': (0, 0, 255)
}

# MediaPipe 面部关键点索引
LANDMARK_INDICES = {
    # 眼部关键点
    'LEFT_EYE': [33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246],
    'RIGHT_EYE': [362, 382, 381, 380, 374, 373, 390, 249, 263, 466, 388, 387, 386, 385, 384, 398],
    
    # 虹膜关键点
    'LEFT_IRIS': [474, 475, 476, 477],
    'RIGHT_IRIS': [469, 470, 471, 472],
    
    # 嘴部关键点
    'MOUTH': [61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318],
    
    # 眉毛关键点
    'EYEBROW': [70, 63, 105, 66, 107, 55, 65, 52, 53, 46],
    
    # 头部姿态关键点
    'NOSE_TIP': [1],
    'CHIN': [175],
    'LEFT_EYE_CENTER': [33],
    'RIGHT_EYE_CENTER': [362]
}

# 发呆行为特征阈值
DAYDREAM_THRESHOLDS = {
    # 眼部特征
    'blink_rate_too_low': 8,      # 眨眼过少
    'blink_rate_too_high': 25,    # 眨眼过多
    'gaze_too_stable': 0.8,       # 注视过于稳定
    'fixation_too_long': 3.0,     # 注视时间过长
    'pupil_movement_too_low': 5.0, # 瞳孔运动过少
    
    # 面部特征
    'expression_change_too_low': 0.1,  # 表情变化过少
    'head_too_stable': 0.9,            # 头部过于稳定
    'facial_tension_too_low': 0.3,     # 面部紧张度过低
    
    # 综合特征
    'overall_daydream_threshold': 0.6   # 总体发呆阈值
}

# 算法参数
ALGORITHM_PARAMS = {
    # EAR (Eye Aspect Ratio) 计算参数
    'ear_calculation': {
        'vertical_weight': 1.0,
        'horizontal_weight': 2.0
    },
    
    # 注视稳定性计算参数
    'gaze_stability': {
        'smoothing_factor': 0.1,
        'stability_window': 30
    },
    
    # 头部姿态计算参数
    'head_pose': {
        'pitch_weight': 1.0,
        'yaw_weight': 1.0,
        'roll_weight': 1.0
    },
    
    # 滑动窗口平滑参数
    'smoothing': {
        'window_size': 10,
        'alpha': 0.3  # 指数移动平均参数
    }
}

# 性能优化配置
PERFORMANCE_CONFIG = {
    'max_fps': 30,
    'skip_frames': 1,  # 跳帧处理
    'resize_factor': 1.0,  # 图像缩放因子
    'enable_gpu': True,
    'batch_processing': False
}

# 日志配置
LOGGING_CONFIG = {
    'level': 'INFO',
    'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    'save_to_file': True,
    'log_file': 'daydream_detection.log',
    'max_file_size': 10 * 1024 * 1024,  # 10MB
    'backup_count': 5
}

# 数据保存配置
DATA_SAVE_CONFIG = {
    'save_detection_results': False,
    'save_video': False,
    'save_landmarks': False,
    'output_directory': 'detection_output',
    'video_codec': 'mp4v',
    'video_fps': 30
}

# 校准配置
CALIBRATION_CONFIG = {
    'enable_auto_calibration': True,
    'calibration_duration': 30,  # 秒
    'calibration_samples': 100,
    'adaptive_thresholds': True
}

# 用户个性化配置
USER_CONFIG = {
    'user_id': 'default',
    'age_group': 'adult',  # child, adult, elderly
    'glasses': False,
    'eye_color': 'brown',  # 可能影响虹膜检测
    'sensitivity': 'medium'  # low, medium, high
}

# 根据用户配置调整参数的函数
def get_user_adjusted_config(user_config=None):
    """根据用户配置调整检测参数"""
    if user_config is None:
        user_config = USER_CONFIG
    
    config = DAYDREAM_THRESHOLDS.copy()
    
    # 根据年龄组调整
    if user_config['age_group'] == 'child':
        config['blink_rate_too_low'] = 10
        config['blink_rate_too_high'] = 30
    elif user_config['age_group'] == 'elderly':
        config['blink_rate_too_low'] = 6
        config['blink_rate_too_high'] = 20
    
    # 根据是否戴眼镜调整
    if user_config['glasses']:
        config['eye_openness_abnormal'] = 0.15
    
    # 根据敏感度调整
    if user_config['sensitivity'] == 'high':
        config['overall_daydream_threshold'] = 0.5
    elif user_config['sensitivity'] == 'low':
        config['overall_daydream_threshold'] = 0.7
    
    return config



# 调试配置
DEBUG_CONFIG = {
    'show_debug_info': False,
    'save_debug_images': False,
    'print_metrics': False,
    'show_fps': True,
    'show_processing_time': False
}
