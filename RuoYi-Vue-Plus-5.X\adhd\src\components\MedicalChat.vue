<template>
  <div class="medical-chat">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="header-left">
        <button class="back-btn" @click="$emit('back-to-list')">
          <i class="fas fa-arrow-left"></i>
        </button>
        <div class="doctor-info">
          <img :src="doctorInfo.avatar" :alt="doctorInfo.name" class="doctor-avatar">
          <div class="doctor-details">
            <h3>{{ doctorInfo.name }}</h3>
            <p>{{ doctorInfo.title }} | {{ doctorInfo.department }}</p>
            <span class="status" :class="doctorInfo.status">
              <i class="fas fa-circle"></i>
              {{ getStatusText(doctorInfo.status) }}
            </span>
          </div>
        </div>
      </div>
      <div class="chat-actions">
        <button class="btn-action" @click="shareGameReport" title="分享游戏报告">
          <i class="fas fa-chart-line"></i>
        </button>
        <button class="btn-action" @click="startVoiceCall" :disabled="!canStartCall" title="语音通话">
          <i class="fas fa-phone"></i>
        </button>
        <button class="btn-action" @click="startVideoCall" :disabled="!canStartCall" title="视频通话">
          <i class="fas fa-video"></i>
        </button>
      </div>
    </div>

    <!-- 聊天消息区域 -->
    <div class="chat-messages" ref="messagesContainer">
      <div v-for="message in messages" :key="message.id" 
           class="message" 
           :class="{ 'own-message': message.senderId === patientId }">
        
        <!-- 系统消息 -->
        <div v-if="message.type === 'system'" class="system-message">
          <i class="fas fa-info-circle"></i>
          {{ message.content }}
        </div>

        <!-- 普通消息 -->
        <div v-else class="message-bubble">
          <div class="message-header">
            <img :src="message.senderAvatar" :alt="message.senderName" class="sender-avatar">
            <span class="sender-name">{{ message.senderName }}</span>
            <span class="message-time">{{ formatTime(message.timestamp) }}</span>
          </div>
          
          <div class="message-content">
            <!-- 文本消息 -->
            <div v-if="message.messageType === 'text'" class="text-content">
              {{ message.content }}
            </div>
            
            <!-- 图片消息 -->
            <div v-else-if="message.messageType === 'image'" class="image-content">
              <img :src="message.content" @click="previewImage(message.content)" class="message-image">
            </div>
            
            <!-- 游戏报告 -->
            <div v-else-if="message.messageType === 'game_report'" class="report-content">
              <div class="report-header">
                <i class="fas fa-chart-bar"></i>
                <span>注意力训练报告</span>
              </div>
              <div class="report-summary">
                <p>训练时间: {{ message.reportData.duration }}分钟</p>
                <p>完成游戏: {{ message.reportData.completedGames }}个</p>
                <p>平均得分: {{ message.reportData.averageScore }}分</p>
              </div>
              <button @click="viewFullReport(message.reportData)" class="view-report-btn">查看详细报告</button>
            </div>

            <!-- 医生建议 -->
            <div v-else-if="message.messageType === 'prescription'" class="prescription-content">
              <div class="prescription-header">
                <i class="fas fa-prescription"></i>
                <span>医生建议</span>
              </div>
              <div class="prescription-body">
                <h4>{{ message.prescriptionData.title }}</h4>
                <p>{{ message.prescriptionData.content }}</p>
              </div>
            </div>
          </div>

          <!-- 消息状态 -->
          <div class="message-status">
            <i v-if="message.status === 'sending'" class="fas fa-clock text-gray"></i>
            <i v-else-if="message.status === 'sent'" class="fas fa-check text-gray"></i>
            <i v-else-if="message.status === 'delivered'" class="fas fa-check-double text-blue"></i>
            <i v-else-if="message.status === 'read'" class="fas fa-check-double text-green"></i>
          </div>
        </div>
      </div>

      <!-- 正在输入指示器 -->
      <div v-if="isTyping" class="typing-indicator">
        <img :src="doctorInfo.avatar" class="typing-avatar">
        <div class="typing-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <span class="typing-text">{{ doctorInfo.name }}正在输入...</span>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="chat-input">
      <div class="input-toolbar">
        <button class="toolbar-btn" @click="selectImage" title="发送图片">
          <i class="fas fa-image"></i>
        </button>
        <button class="toolbar-btn" @click="selectFile" title="发送文件">
          <i class="fas fa-paperclip"></i>
        </button>
        <button class="toolbar-btn" @click="insertEmoji" title="表情">
          <i class="fas fa-smile"></i>
        </button>
      </div>
      
      <div class="input-container">
        <textarea 
          v-model="messageInput" 
          @keydown="handleKeyDown"
          @input="handleTyping"
          placeholder="输入消息..."
          class="message-input"
          rows="1"
          ref="messageTextarea">
        </textarea>
        <button 
          class="send-button" 
          @click="sendMessage"
          :disabled="!messageInput.trim()">
          <i class="fas fa-paper-plane"></i>
        </button>
      </div>
    </div>

    <!-- 文件上传隐藏input -->
    <input type="file" ref="imageInput" @change="handleImageSelect" accept="image/*" style="display: none">
    <input type="file" ref="fileInput" @change="handleFileSelect" style="display: none">
  </div>
</template>

<script>
import { Client } from '@stomp/stompjs'
import SockJS from 'sockjs-client'

export default {
  name: 'MedicalChat',
  props: {
    consultationId: {
      type: String,
      required: true
    },
    patientId: {
      type: String,
      required: true
    }
  },
  emits: ['back-to-list', 'start-video-call', 'start-voice-call'],
  data() {
    return {
      messages: [
        {
          id: 'msg_001',
          senderId: 'doctor_001',
          senderName: '李心理医生',
          senderAvatar: 'https://randomuser.me/api/portraits/women/44.jpg',
          messageType: 'text',
          content: '您好！我是李医生，很高兴为您提供咨询服务。请问今天有什么可以帮助您的吗？',
          timestamp: new Date(Date.now() - 300000).toISOString(),
          status: 'read',
          type: 'message'
        },
        {
          id: 'msg_002',
          senderId: 'system',
          messageType: 'text',
          content: '咨询会话已开始，请放心交流。所有对话内容都会严格保密。',
          timestamp: new Date(Date.now() - 240000).toISOString(),
          status: 'read',
          type: 'system'
        }
      ],
      messageInput: '',
      isTyping: false,
      typingTimer: null,

      // WebSocket相关
      stompClient: null,
      isConnected: false,
      connectionStatus: 'disconnected', // disconnected, connecting, connected

      doctorInfo: {
        id: 'doctor_001',
        name: '李心理医生',
        title: '主治医师',
        department: '儿童心理科',
        avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
        status: 'online'
      },

      // 当前用户信息
      currentUser: {
        id: '',
        name: '',
        avatar: '',
        type: 'patient'
      }
    }
  },
  computed: {
    canStartCall() {
      return this.doctorInfo.status === 'online'
    }
  },
  mounted() {
    this.initCurrentUser()
    this.connectWebSocket()
    this.scrollToBottom()
  },

  beforeUnmount() {
    this.disconnectWebSocket()
  },
  methods: {
    // 初始化当前用户信息
    initCurrentUser() {
      const user = JSON.parse(localStorage.getItem('currentUser') || '{}')
      this.currentUser = {
        id: user.userId || user.patientId || this.patientId,
        name: user.patientName || user.username || '患者',
        avatar: user.avatar || 'https://randomuser.me/api/portraits/men/44.jpg',
        type: 'patient'
      }
      console.log('🔥 初始化用户信息:', this.currentUser)
    },

    // 连接WebSocket
    connectWebSocket() {
      try {
        console.log('🔌 开始连接WebSocket...')
        this.connectionStatus = 'connecting'

        // 创建SockJS连接
        const socket = new SockJS('http://localhost:80/ws-chat')

        // 创建STOMP客户端
        this.stompClient = new Client({
          webSocketFactory: () => socket,
          connectHeaders: {
            // 可以添加认证头
            // Authorization: `Bearer ${getToken()}`
          },
          debug: (str) => {
            console.log('🔍 STOMP Debug:', str)
          },
          reconnectDelay: 5000,
          heartbeatIncoming: 4000,
          heartbeatOutgoing: 4000,
          onConnect: this.onWebSocketConnect,
          onDisconnect: this.onWebSocketDisconnect,
          onStompError: this.onWebSocketError
        })

        // 激活连接
        this.stompClient.activate()

      } catch (error) {
        console.error('❌ WebSocket连接失败:', error)
        this.connectionStatus = 'disconnected'
      }
    },

    // WebSocket连接成功
    onWebSocketConnect(frame) {
      console.log('✅ WebSocket连接成功:', frame)
      this.isConnected = true
      this.connectionStatus = 'connected'

      // 订阅私聊消息
      this.stompClient.subscribe(`/user/queue/private`, this.handlePrivateMessage)

      // 订阅消息确认
      this.stompClient.subscribe(`/user/queue/confirmation`, this.handleMessageConfirmation)

      // 发送用户上线通知
      this.stompClient.publish({
        destination: '/app/chat/online',
        body: JSON.stringify({
          userId: this.currentUser.id,
          userName: this.currentUser.name,
          userType: this.currentUser.type
        })
      })

      // 添加连接成功的系统消息
      this.addSystemMessage('已连接到聊天服务器')
    },

    // WebSocket断开连接
    onWebSocketDisconnect(frame) {
      console.log('🔌 WebSocket连接断开:', frame)
      this.isConnected = false
      this.connectionStatus = 'disconnected'
      this.addSystemMessage('与服务器的连接已断开')
    },

    // WebSocket错误处理
    onWebSocketError(frame) {
      console.error('❌ WebSocket错误:', frame)
      this.connectionStatus = 'disconnected'
      this.addSystemMessage('连接出现错误，请刷新页面重试')
    },

    // 断开WebSocket连接
    disconnectWebSocket() {
      if (this.stompClient && this.isConnected) {
        // 发送用户下线通知
        this.stompClient.publish({
          destination: '/app/chat/offline',
          body: JSON.stringify({
            userId: this.currentUser.id,
            userName: this.currentUser.name
          })
        })

        this.stompClient.deactivate()
        console.log('🔌 WebSocket连接已断开')
      }
    },

    // 处理接收到的私聊消息
    handlePrivateMessage(message) {
      try {
        const chatMessage = JSON.parse(message.body)
        console.log('📨 收到私聊消息:', chatMessage)

        // 添加到消息列表
        this.messages.push({
          id: chatMessage.messageId,
          senderId: chatMessage.senderId,
          senderName: chatMessage.senderName,
          senderAvatar: this.doctorInfo.avatar, // 使用医生头像
          messageType: chatMessage.messageType || 'text',
          content: chatMessage.content,
          timestamp: chatMessage.timestamp,
          status: 'delivered',
          type: 'message'
        })

        this.$nextTick(() => {
          this.scrollToBottom()
        })

        // 发送已读确认
        this.sendReadConfirmation(chatMessage.messageId)

      } catch (error) {
        console.error('❌ 处理私聊消息失败:', error)
      }
    },

    // 处理消息确认
    handleMessageConfirmation(message) {
      try {
        const confirmation = JSON.parse(message.body)
        console.log('✅ 收到消息确认:', confirmation)

        // 更新消息状态
        const msg = this.messages.find(m => m.id === confirmation.messageId)
        if (msg) {
          msg.status = confirmation.status
        }

      } catch (error) {
        console.error('❌ 处理消息确认失败:', error)
      }
    },

    // 发送已读确认
    sendReadConfirmation(messageId) {
      if (this.stompClient && this.isConnected) {
        this.stompClient.publish({
          destination: '/app/chat/read-confirmation',
          body: JSON.stringify({
            messageId: messageId,
            readerId: this.currentUser.id,
            readerName: this.currentUser.name
          })
        })
      }
    },

    // 添加系统消息
    addSystemMessage(content) {
      this.messages.push({
        id: 'sys_' + Date.now(),
        senderId: 'system',
        messageType: 'text',
        content: content,
        timestamp: new Date().toISOString(),
        status: 'read',
        type: 'system'
      })

      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 发送消息
    sendMessage() {
      if (!this.messageInput.trim()) return

      if (!this.isConnected) {
        this.addSystemMessage('连接已断开，请刷新页面重试')
        return
      }

      const messageContent = this.messageInput.trim()
      const messageId = this.generateMessageId()

      // 创建消息对象
      const message = {
        id: messageId,
        senderId: this.currentUser.id,
        senderName: this.currentUser.name,
        senderAvatar: this.currentUser.avatar,
        messageType: 'text',
        content: messageContent,
        timestamp: new Date().toISOString(),
        status: 'sending',
        type: 'message'
      }

      // 添加到消息列表
      this.addMessage(message)
      this.messageInput = ''
      this.adjustTextareaHeight()

      // 通过WebSocket发送消息
      try {
        const chatMessage = {
          messageId: messageId,
          senderId: this.currentUser.id,
          senderName: this.currentUser.name,
          senderType: this.currentUser.type,
          receiverId: this.doctorInfo.id,
          receiverName: this.doctorInfo.name,
          content: messageContent,
          messageType: 'text'
        }

        this.stompClient.publish({
          destination: '/app/chat/private',
          body: JSON.stringify(chatMessage)
        })

        console.log('📤 发送消息:', chatMessage)

        // 更新消息状态为已发送
        setTimeout(() => {
          message.status = 'sent'
        }, 500)

      } catch (error) {
        console.error('❌ 发送消息失败:', error)
        message.status = 'failed'
        this.addSystemMessage('消息发送失败，请重试')
      }
    },

    // 模拟医生回复
    simulateDoctorResponse() {
      const responses = [
        '我理解您的担心，这是很常见的情况。',
        '根据您的描述，我建议我们先做一些基础的评估。',
        '您可以详细描述一下具体的症状吗？',
        '这种情况持续多长时间了？',
        '让我为您制定一个个性化的训练计划。'
      ]

      setTimeout(() => {
        this.isTyping = true
        setTimeout(() => {
          this.isTyping = false
          const response = {
            id: this.generateMessageId(),
            senderId: this.doctorInfo.id,
            senderName: this.doctorInfo.name,
            senderAvatar: this.doctorInfo.avatar,
            messageType: 'text',
            content: responses[Math.floor(Math.random() * responses.length)],
            timestamp: new Date().toISOString(),
            status: 'read',
            type: 'message'
          }
          this.addMessage(response)
        }, 2000)
      }, 1000)
    },

    // 添加消息
    addMessage(message) {
      this.messages.push(message)
      this.$nextTick(() => {
        this.scrollToBottom()
      })
    },

    // 处理键盘事件
    handleKeyDown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },

    // 处理输入
    handleTyping() {
      this.adjustTextareaHeight()
    },

    // 自动调整输入框高度
    adjustTextareaHeight() {
      const textarea = this.$refs.messageTextarea
      if (textarea) {
        textarea.style.height = 'auto'
        textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px'
      }
    },

    // 分享游戏报告
    shareGameReport() {
      const reportData = this.generateGameReport()
      const message = {
        id: this.generateMessageId(),
        senderId: this.patientId,
        senderName: '我',
        senderAvatar: '/images/default-avatar.jpg',
        messageType: 'game_report',
        reportData: reportData,
        timestamp: new Date().toISOString(),
        status: 'sent',
        type: 'message'
      }
      this.addMessage(message)
    },

    // 生成游戏报告
    generateGameReport() {
      const gameData = JSON.parse(localStorage.getItem('gameHistory') || '[]')
      return {
        duration: gameData.reduce((sum, game) => sum + (game.duration || 0), 0),
        completedGames: gameData.length,
        averageScore: gameData.length > 0 ? 
          Math.round(gameData.reduce((sum, game) => sum + (game.score || 0), 0) / gameData.length) : 0,
        detailedData: gameData
      }
    },

    // 工具方法
    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    },

    getStatusText(status) {
      const statusMap = {
        online: '在线',
        busy: '忙碌',
        offline: '离线'
      }
      return statusMap[status] || '未知'
    },

    generateMessageId() {
      return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
    },

    scrollToBottom() {
      const container = this.$refs.messagesContainer
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },

    // 文件选择
    selectImage() {
      this.$refs.imageInput.click()
    },

    selectFile() {
      this.$refs.fileInput.click()
    },

    handleImageSelect(event) {
      const file = event.target.files[0]
      if (file) {
        console.log('选择图片:', file.name)
      }
    },

    handleFileSelect(event) {
      const file = event.target.files[0]
      if (file) {
        console.log('选择文件:', file.name)
      }
    },

    // 通话功能
    startVideoCall() {
      this.$emit('start-video-call', {
        doctorId: this.doctorInfo.id,
        consultationId: this.consultationId
      })
    },

    startVoiceCall() {
      this.$emit('start-voice-call', {
        doctorId: this.doctorInfo.id,
        consultationId: this.consultationId
      })
    },

    // 其他功能
    insertEmoji() {
      console.log('插入表情')
    },

    previewImage(url) {
      console.log('预览图片:', url)
    },

    viewFullReport(reportData) {
      console.log('查看完整报告:', reportData)
    }
  }
}
</script>

<style scoped>
.medical-chat {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f0f8ff; /* 冰蓝色背景 */
  max-width: 100%;
  margin: 0 auto;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%); /* 冰蓝色渐变 */
  border-bottom: 1px solid #b3d9ff;
  box-shadow: 0 2px 8px rgba(51, 153, 255, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 15px;
}

.back-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  color: #4a90e2; /* 冰蓝色 */
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.2);
}

.back-btn:hover {
  background: rgba(255, 255, 255, 1);
  color: #2c5aa0;
  transform: translateY(-1px);
}

.doctor-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.doctor-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
}

.doctor-details h3 {
  margin: 0;
  font-size: 16px;
  color: #333;
}

.doctor-details p {
  margin: 4px 0;
  font-size: 14px;
  color: #666;
}

.status {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.status.online { color: #4CAF50; }
.status.busy { color: #FF9800; }
.status.offline { color: #9E9E9E; }

.chat-actions {
  display: flex;
  gap: 8px;
}

.btn-action {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  color: #4a90e2; /* 冰蓝色 */
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.2);
}

.btn-action:hover:not(:disabled) {
  background: rgba(255, 255, 255, 1);
  color: #2c5aa0;
  transform: translateY(-1px);
}

.btn-action:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px 20px; /* 增加左右padding确保居中 */
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 100%;
  margin: 0 auto; /* 确保消息区域居中 */
}

.message {
  display: flex;
  flex-direction: column;
}

.message.own-message {
  align-items: flex-end;
}

.message.own-message .message-bubble {
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); /* 冰蓝色渐变 */
  color: white;
  border-radius: 18px 18px 4px 18px; /* 微信风格圆角 */
}

.system-message {
  text-align: center;
  color: #666;
  font-size: 14px;
  padding: 8px;
  background: rgba(0,0,0,0.05);
  border-radius: 16px;
  margin: 8px 0;
}

.message-bubble {
  max-width: 70%;
  background: white;
  border-radius: 18px 18px 18px 4px; /* 微信风格圆角 */
  padding: 12px 16px;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.15);
  word-wrap: break-word; /* 确保长文本换行 */
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.sender-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.sender-name {
  font-weight: 600;
  font-size: 14px;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-left: auto;
}

.message-image {
  max-width: 200px;
  border-radius: 8px;
  cursor: pointer;
}

.report-content, .prescription-content {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  background: #f9f9f9;
}

.report-header, .prescription-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  margin-bottom: 8px;
}

.view-report-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-top: 8px;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: white;
  border-radius: 16px;
  width: fit-content;
}

.typing-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #999;
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) { animation-delay: 0.2s; }
.typing-dots span:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-10px); }
}

.chat-input {
  background: linear-gradient(135deg, #f8fcff 0%, #e6f3ff 100%); /* 冰蓝色渐变背景 */
  border-top: 1px solid #b3d9ff;
  padding: 16px 20px; /* 增加左右padding确保居中 */
  max-width: 100%;
  margin: 0 auto;
}

.input-toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.toolbar-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.8);
  color: #4a90e2; /* 冰蓝色 */
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.2);
}

.toolbar-btn:hover {
  background: rgba(255, 255, 255, 1);
  color: #2c5aa0;
  transform: translateY(-1px);
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 12px;
  width: 100%;
  max-width: 100%; /* 确保不超出容器 */
}

.message-input {
  flex: 1;
  border: 1px solid #b3d9ff; /* 冰蓝色边框 */
  border-radius: 20px;
  padding: 12px 16px;
  resize: none;
  outline: none;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  max-height: 120px;
  background: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.message-input:focus {
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
  background: white;
}

.send-button {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%); /* 冰蓝色渐变 */
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #357abd 0%, #2c5aa0 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.message-status {
  text-align: right;
  margin-top: 4px;
}

.text-gray { color: #999; }
.text-blue { color: #007bff; }
.text-green { color: #28a745; }
</style>
