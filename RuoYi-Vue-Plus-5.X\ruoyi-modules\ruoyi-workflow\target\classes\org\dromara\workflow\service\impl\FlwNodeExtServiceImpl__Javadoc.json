{"doc": " 流程设计器-节点扩展属性\n\n <AUTHOR>\n", "fields": [{"name": "CHILD_NODE_MAP", "doc": " 存储不同 dictType 对应的配置信息\n"}], "enumConstants": [], "methods": [{"name": "getNodeExt", "paramTypes": [], "doc": " 获取节点扩展属性\n\n @return 节点扩展属性列表\n"}, {"name": "buildNodeExt", "paramTypes": ["java.lang.String", "java.lang.String", "int", "java.util.List"], "doc": " 构建一个 `NodeExt` 对象\n\n @param code    唯一编码\n @param name    名称（新页签时，作为页签名称）\n @param type    节点类型（1: 基础设置，2: 新页签）\n @param sources 数据来源（枚举类或字典类型）\n @return 构建的 `NodeExt` 对象\n"}, {"name": "buildChildNode", "paramTypes": ["java.lang.Class"], "doc": " 根据枚举类型构建一个 `ChildNode` 对象\n\n @param enumClass 枚举类，必须实现 `NodeExtEnum` 接口\n @return 构建的 `ChildNode` 对象\n"}, {"name": "buildChildNode", "paramTypes": ["java.lang.String"], "doc": " 根据字典类型构建 `ChildNode` 对象\n\n @param dictType 字典类型\n @return 构建的 `ChildNode` 对象\n"}, {"name": "buildChildNodeMap", "paramTypes": ["java.lang.String"], "doc": " 根据 CHILD_NODE_MAP 中的配置信息，构建一个基本的 ChildNode 对象\n 该方法用于设置 ChildNode 的常规属性，例如 label、type、是否必填、是否多选等\n\n @param key CHILD_NODE_MAP 的 key\n @return 返回构建好的 ChildNode 对象\n"}, {"name": "buildButtonPermissionsFromExt", "paramTypes": ["java.lang.String"], "doc": " 从扩展属性构建按钮权限列表：根据 ext 中记录的权限值，标记每个按钮是否勾选\n\n @param ext 扩展属性 JSON 字符串\n @return 按钮权限 VO 列表\n"}, {"name": "buildPermissionsFromSources", "paramTypes": ["java.util.Map", "java.util.List"], "doc": " 将权限映射与按钮权限来源（枚举类或字典类型）进行匹配，生成权限视图列表\n <p>\n 使用说明：\n - sources 支持传入多个来源类型，支持 NodeExtEnum 枚举类 或 字典类型字符串（dictType）\n - 若需要扩展更多按钮权限，只需在 sources 中新增对应的枚举类或字典类型\n <p>\n 示例：\n buildPermissionsFromSources(permissionMap, List.of(ButtonPermissionEnum.class, \"custom_button_dict\"));\n\n @param permissionMap 权限映射\n @param sources       枚举类或字典类型列表\n @return 按钮权限视图对象列表\n"}, {"name": "extractDictItems", "paramTypes": ["org.dromara.warm.flow.ui.vo.NodeExt.ChildNode", "java.util.Set"], "doc": " 从节点子项中提取字典项，并构建按钮权限视图对象列表\n\n @param childNode   子节点\n @param selectedSet 已选中的值集\n @return 按钮权限视图对象列表\n"}], "constructors": []}