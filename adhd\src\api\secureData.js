// 安全数据管理API
// 确保所有数据操作都通过RuoYi后端的RBAC权限控制

const RUOYI_BASE_URL = 'http://localhost:8088'

// 获取认证token
function getAuthToken() {
  return localStorage.getItem('ruoyi_token') || sessionStorage.getItem('ruoyi_token')
}

// 安全请求函数
async function secureDataRequest(url, options = {}) {
  const token = getAuthToken()
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers
    },
    ...options
  }

  try {
    const response = await fetch(`${RUOYI_BASE_URL}${url}`, config)
    const data = await response.json()

    // 处理RuoYi标准响应格式
    if (data.code !== undefined) {
      if (data.code === 200) {
        return data
      } else if (data.code === 401) {
        throw new Error('认证失败，请重新登录')
      } else {
        throw new Error(data.msg || '请求失败')
      }
    }

    return data
  } catch (error) {
    console.error('安全数据请求失败:', error)
    throw error
  }
}

// 患者信息安全管理API
export const securePatientAPI = {
  // 创建患者信息（需要认证）
  async createPatient(patientData) {
    console.log('🔒 安全创建患者信息:', patientData)
    return secureDataRequest('/system/info', {
      method: 'POST',
      body: JSON.stringify(patientData)
    })
  },

  // 更新患者信息（需要认证）
  async updatePatient(patientData) {
    console.log('🔒 安全更新患者信息:', patientData)
    return secureDataRequest('/system/info', {
      method: 'PUT',
      body: JSON.stringify(patientData)
    })
  },

  // 获取患者信息（需要认证）
  async getPatient(userId) {
    console.log('🔒 安全获取患者信息:', userId)
    return secureDataRequest(`/system/info/${userId}`, {
      method: 'GET'
    })
  },

  // 获取患者列表（需要认证和权限）
  async getPatientList(params = {}) {
    console.log('🔒 安全获取患者列表:', params)
    const queryString = new URLSearchParams(params).toString()
    const url = queryString ? `/system/info/list?${queryString}` : '/system/info/list'
    return secureDataRequest(url, {
      method: 'GET'
    })
  },

  // 删除患者信息（需要认证和权限）
  async deletePatient(userIds) {
    console.log('🔒 安全删除患者信息:', userIds)
    return secureDataRequest(`/system/info/${userIds}`, {
      method: 'DELETE'
    })
  }
}

// 量表测试记录安全管理API
export const secureScaleAPI = {
  // 创建量表测试记录（需要认证）
  async createScaleRecord(recordData) {
    console.log('🔒 安全创建量表记录:', recordData)
    return secureDataRequest('/system/scale', {
      method: 'POST',
      body: JSON.stringify(recordData)
    })
  },

  // 获取量表测试记录（需要认证）
  async getScaleRecords(userId, params = {}) {
    console.log('🔒 安全获取量表记录:', userId, params)
    const queryParams = new URLSearchParams({ userId, ...params })
    return secureDataRequest(`/system/scale/list?${queryParams.toString()}`, {
      method: 'GET'
    })
  },

  // 更新量表测试记录（需要认证）
  async updateScaleRecord(recordData) {
    console.log('🔒 安全更新量表记录:', recordData)
    return secureDataRequest('/system/scale', {
      method: 'PUT',
      body: JSON.stringify(recordData)
    })
  }
}

// 检测会话安全管理API
export const secureDetectionAPI = {
  // 创建检测会话（需要认证）
  async createDetectionSession(sessionData) {
    console.log('🔒 安全创建检测会话:', sessionData)
    return secureDataRequest('/system/detection', {
      method: 'POST',
      body: JSON.stringify(sessionData)
    })
  },

  // 获取检测会话记录（需要认证）
  async getDetectionSessions(userId, params = {}) {
    console.log('🔒 安全获取检测会话:', userId, params)
    const queryParams = new URLSearchParams({ userId, ...params })
    return secureDataRequest(`/system/detection/list?${queryParams.toString()}`, {
      method: 'GET'
    })
  },

  // 更新检测会话（需要认证）
  async updateDetectionSession(sessionData) {
    console.log('🔒 安全更新检测会话:', sessionData)
    return secureDataRequest('/system/detection', {
      method: 'PUT',
      body: JSON.stringify(sessionData)
    })
  }
}

// 数据安全工具函数
export const secureDataUtils = {
  // 验证用户权限
  async checkUserPermission(action, resource) {
    try {
      const response = await secureDataRequest('/system/auth/check-permission', {
        method: 'POST',
        body: JSON.stringify({ action, resource })
      })
      return response.code === 200 && response.data === true
    } catch (error) {
      console.error('权限检查失败:', error)
      return false
    }
  },

  // 记录操作日志
  async logOperation(operation, details) {
    try {
      await secureDataRequest('/system/log/operation', {
        method: 'POST',
        body: JSON.stringify({
          operation,
          details,
          timestamp: new Date().toISOString()
        })
      })
    } catch (error) {
      console.error('记录操作日志失败:', error)
    }
  },

  // 数据加密（客户端预处理）
  encryptSensitiveData(data) {
    // 这里可以添加客户端数据加密逻辑
    // 实际应用中应该使用专业的加密库
    console.log('🔐 加密敏感数据')
    return data
  },

  // 数据脱敏（显示时使用）
  maskSensitiveData(data, type) {
    if (!data) return data
    
    switch (type) {
      case 'idCard':
        return data.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2')
      case 'phone':
        return data.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
      case 'email':
        return data.replace(/(.{2}).*(@.*)/, '$1***$2')
      default:
        return data
    }
  }
}

// 导出所有安全API
export default {
  patient: securePatientAPI,
  scale: secureScaleAPI,
  detection: secureDetectionAPI,
  utils: secureDataUtils
}
