<template>
  <div class="page-container">
    <!-- 公共头部组件 -->
    <AppHeader
      current-route="online-consultation"
    />

    <!-- 内容包装区 -->
    <div class="content-wrapper">
      <!-- 公共侧边栏组件 -->
      <AppSidebar
        current-route="online-consultation"
        :expanded="navExpanded"
        @expand="expandNav"
        @collapse="collapseNav"
      />

      <!-- 聊天主内容区 -->
      <div class="online-consultation">
    <!-- 左侧会话列表面板 -->
    <section class="conversation-list-panel-container">
      <!-- 搜索区域 -->
      <div class="search-view">
        <div class="search-container">
          <div class="search-input-wrapper">
            <i class="el-icon-search search-icon"></i>
            <input
              type="text"
              placeholder="搜索会话、联系人"
              v-model="searchKeyword"
              @input="handleSearch"
              @focus="onSearchFocus"
              @blur="onSearchBlur"
              class="search-input">
            <i v-if="searchKeyword"
               class="el-icon-circle-close clear-icon"
               @click="clearSearch"></i>
          </div>
          <div class="search-actions">
            <div class="action-btn" @click="createNewConversation" title="新建会话">
              <i class="el-icon-plus"></i>
            </div>
            <div class="action-btn" @click="showContactList" title="联系人">
              <i class="el-icon-user"></i>
            </div>
          </div>
        </div>

        <!-- 搜索结果 -->
        <div v-if="searchKeyword && searchResults.length > 0" class="search-results">
          <div class="search-section">
            <div class="section-title">联系人</div>
            <div v-for="contact in searchResults.contacts" :key="contact.id"
                 class="search-result-item" @click="startConversationWithContact(contact)">
              <img :src="contact.avatar" class="result-avatar" />
              <div class="result-info">
                <div class="result-name">{{ contact.name }}</div>
                <div class="result-desc">{{ contact.title || '医生' }}</div>
              </div>
            </div>
          </div>

          <div class="search-section" v-if="searchResults.conversations.length > 0">
            <div class="section-title">会话</div>
            <div v-for="conv in searchResults.conversations" :key="conv.id"
                 class="search-result-item" @click="selectConversation(conv.index, conv)">
              <img :src="conv.avatar" class="result-avatar" />
              <div class="result-info">
                <div class="result-name">{{ conv.name }}</div>
                <div class="result-desc">{{ conv.lastMessage }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 会话列表容器 -->
      <div class="conversation-panel">
        <div v-if="searchKeyword && searchResults.length === 0" class="search-empty">
          <div class="empty-icon">
            <i class="el-icon-search"></i>
          </div>
          <div class="empty-text">未找到相关结果</div>
        </div>

        <!-- 会话列表视图 -->
        <div class="conversation-list-view">
          <div class="conversation-list">
            <div
              v-for="(conversation, index) in filteredConversationList"
              :key="conversation.id"
              class="conversation-item-container"
              :class="{
                'active': index === activeIndex,
                'top': conversation.isPinned,
                'muted': conversation.isMuted
              }"
              @click="selectConversation(index, conversation)"
              @contextmenu.prevent="showConversationContextMenu($event, conversation)">

              <div class="conversation-item">
                <!-- 头像区域 -->
                <div class="header">
                  <div class="avatar-wrapper">
                    <img v-if="conversation.avatar"
                         :src="conversation.avatar"
                         class="avatar"
                         @error="handleAvatarError" />
                    <div v-else
                         class="avatar default-avatar"
                         :style="{ background: conversation.avatarColor || getAvatarColor(conversation.name) }">
                      {{ conversation.name.charAt(0).toUpperCase() }}
                    </div>

                    <!-- 未读消息徽章 -->
                    <div v-if="conversation.unreadCount > 0"
                         class="badge"
                         :class="{ silent: conversation.isMuted }">
                      {{ conversation.unreadCount > 99 ? '···' : conversation.unreadCount }}
                    </div>

                    <!-- 在线状态 -->
                    <div v-if="conversation.type === 'private'"
                         class="online-status"
                         :class="{ 'online': conversation.isOnline }">
                    </div>
                  </div>
                </div>

                <!-- 内容区域 -->
                <div class="content-container">
                  <div class="title-time-container">
                    <!-- 会话标题 -->
                    <div class="title-wrapper">
                      <i v-if="conversation.type === 'secret'" class="el-icon-lock title-icon"></i>
                      <h2 class="title single-line">{{ conversation.name }}</h2>
                      <div v-if="conversation.isOfficial" class="official-badge">官方</div>
                      <div v-if="conversation.isDomain" class="domain-badge">{{ conversation.domainName }}</div>
                    </div>

                    <!-- 时间 -->
                    <p class="time single-line">{{ formatTime(conversation.lastMessageTime) }}</p>
                  </div>

                  <div class="content">
                    <!-- 草稿提示 -->
                    <p v-if="conversation.draft" class="draft single-line">[草稿] {{ conversation.draft }}</p>

                    <!-- 正在输入提示 -->
                    <p v-else-if="conversation.isTyping" class="typing single-line">
                      <span class="typing-text">正在输入</span>
                      <span class="typing-dots">
                        <span></span>
                        <span></span>
                        <span></span>
                      </span>
                    </p>

                    <!-- 最后一条消息 -->
                    <p v-else class="last-message-desc single-line">
                      <i v-if="conversation.unreadMention > 0" class="mention-indicator">[有人@我]</i>
                      <i v-if="conversation.lastMessageType === 'image'" class="el-icon-picture-outline message-type-icon"></i>
                      <i v-else-if="conversation.lastMessageType === 'file'" class="el-icon-document message-type-icon"></i>
                      <i v-else-if="conversation.lastMessageType === 'voice'" class="el-icon-microphone message-type-icon"></i>
                      <i v-else-if="conversation.lastMessageType === 'video'" class="el-icon-video-camera message-type-icon"></i>
                      {{ conversation.lastMessage }}
                    </p>

                    <!-- 免打扰图标 -->
                    <i v-if="conversation.isMuted" class="el-icon-bell-slash mute-icon" title="免打扰"></i>

                    <!-- 置顶图标 -->
                    <i v-if="conversation.isPinned" class="el-icon-top pin-icon" title="已置顶"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="filteredConversationList.length === 0" class="empty-state">
            <div class="empty-icon">
              <i class="el-icon-chat-dot-round"></i>
            </div>
            <div class="empty-text">暂无会话</div>
            <div class="empty-subtext">开始您的第一次咨询对话</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 右侧聊天区域 -->
    <div class="chat-main">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <div class="chat-title">
          <span v-if="currentConversation">
            {{ currentConversation.name }}
            <span v-if="currentConversation.type === 'group'">
              ({{ memberCount }})
            </span>
          </span>
        </div>
        <div class="chat-actions">
          <div
            class="action-tab"
            :class="{ 'active': activeTab === 'chat' }"
            @click="switchTab('chat')">
            聊天
          </div>
          <div
            class="action-tab"
            :class="{ 'active': activeTab === 'files' }"
            @click="switchTab('files')">
            文件
          </div>
          <div class="settings-btn">
            <i class="el-icon-setting"></i>
          </div>
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div v-show="activeTab === 'chat'" class="chat-content">
        <div class="chat-container" :style="chatContainerStyle">
          <!-- 展开/收起按钮 -->
          <div
            v-if="showToggleBtn"
            class="toggle-btn"
            :style="toggleBtnStyle"
            @click="toggleMemberList">
            <i :class="showMemberList ? 'el-icon-d-arrow-right' : 'el-icon-d-arrow-left'"></i>
          </div>

          <!-- 消息区域 -->
          <div class="message-area">
            <!-- 历史消息提示 -->
            <div class="history-tip" v-if="hasMoreHistory" @click="loadMoreHistory">
              {{ historyTip }}
            </div>

            <!-- 消息列表 -->
            <div class="message-list" id="messageContainer">
              <div
                class="message-item"
                v-for="message in messageList"
                :key="message.id"
                @contextmenu.prevent="showMessageMenu(message)"
                @click="handleMessageClick(message)">

                <!-- 消息头像和用户信息 -->
                <div v-if="message.type !== 'system'" class="message-wrapper" :class="{ 'own': message.isOwn }">
                  <div :class="['message-avatar', message.isOwn ? 'own' : 'other']">
                    <div class="avatar-text" :style="{ background: message.avatarColor || (message.isOwn ? '#67c23a' : '#409eff') }">
                      {{ message.userName.charAt(0) }}
                    </div>
                  </div>

                  <div class="message-info">
                    <div :class="['user-time', message.isOwn ? 'own' : 'other']">
                      <span class="user-name">{{ message.userName }}</span>
                      <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                      <!-- 消息状态指示器 -->
                      <span v-if="message.isOwn" class="message-status">
                        <i v-if="message.status === 'sending'" class="el-icon-loading rotating"></i>
                        <i v-else-if="message.status === 'sent'" class="el-icon-check"></i>
                        <i v-else-if="message.status === 'delivered'" class="el-icon-check message-delivered"></i>
                        <i v-else-if="message.status === 'read'" class="el-icon-check message-read"></i>
                        <i v-else-if="message.status === 'failed'" class="el-icon-warning-outline message-failed" title="发送失败，点击重试"></i>
                      </span>
                    </div>

                    <!-- 消息内容 -->
                    <div :class="['message-content', message.isOwn ? 'own' : 'other']">
                      <!-- 文本消息 -->
                      <div v-if="message.type === 'text'" class="text-message" v-html="message.content"></div>

                      <!-- 图片消息 -->
                      <div v-else-if="message.type === 'image'" class="image-message">
                        <el-image
                          :src="message.content"
                          :preview-src-list="[message.content]"
                          fit="cover"
                          class="message-image"
                          @click="previewImage(message.content)">
                          <template #error>
                            <div class="image-error">
                              <i class="el-icon-picture-outline"></i>
                              <span>图片加载失败</span>
                            </div>
                          </template>
                        </el-image>
                      </div>

                      <!-- 文件消息 -->
                      <div v-else-if="message.type === 'file'" class="file-message" @click="downloadFile(message)">
                        <div class="file-icon">
                          <i :class="getFileIcon(message.fileName)"></i>
                        </div>
                        <div class="file-info">
                          <div class="file-name">{{ message.fileName }}</div>
                          <div class="file-size">{{ formatFileSize(message.fileSize) }}</div>
                        </div>
                        <div class="file-download">
                          <i class="el-icon-download"></i>
                        </div>
                      </div>

                      <!-- 语音消息 -->
                      <div v-else-if="message.type === 'voice'" class="voice-message" @click="playVoice(message)">
                        <div class="voice-icon">
                          <i :class="message.isPlaying ? 'el-icon-video-pause' : 'el-icon-microphone'"></i>
                        </div>
                        <div class="voice-duration">{{ message.duration }}''</div>
                        <div class="voice-waves" v-if="message.isPlaying">
                          <span></span>
                          <span></span>
                          <span></span>
                        </div>
                      </div>

                      <!-- 系统消息 -->
                      <div v-else-if="message.type === 'system'" class="system-message">
                        {{ message.content }}
                      </div>

                      <!-- 撤回消息 -->
                      <div v-else-if="message.type === 'withdraw'" class="withdraw-message">
                        <i class="el-icon-refresh-left"></i>
                        {{ message.content }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 撤回消息提示 -->
            <div v-show="showWithdrawTip" class="withdraw-tip">
              <span @click="withdrawMessage" class="withdraw-btn">撤回</span>
            </div>
          </div>

          <!-- 表情面板 -->
          <div v-if="showEmojiPanel" class="emoji-panel">
            <div class="emoji-list">
              <div
                class="emoji-item"
                v-for="(emoji, index) in emojiList"
                :key="index"
                @click="insertEmoji(emoji)">
                <img :src="emoji.url" :alt="emoji.name" />
              </div>
            </div>
          </div>

          <!-- 输入区域 -->
          <div class="input-area">
            <!-- 工具栏 -->
            <div class="input-toolbar">
              <!-- 表情按钮 -->
              <div class="toolbar-btn" @click="toggleEmojiPanel" title="表情">
                <i class="el-icon-sunny"></i>
              </div>

              <!-- 图片上传 -->
              <div class="toolbar-btn" @click="openImageUpload" title="图片">
                <i class="el-icon-picture"></i>
              </div>

              <!-- 文件上传 -->
              <div class="toolbar-btn" @click="openFileUpload" title="文件">
                <i class="el-icon-folder"></i>
              </div>

              <!-- 语音录制 -->
              <div class="toolbar-btn" @click="toggleVoiceRecord" title="语音" :class="{ active: isRecording }">
                <i :class="isRecording ? 'el-icon-video-pause' : 'el-icon-microphone'"></i>
              </div>

              <!-- 截图 -->
              <div class="toolbar-btn" @click="takeScreenshot" title="截图">
                <i class="el-icon-camera"></i>
              </div>

              <!-- 更多功能 -->
              <el-dropdown trigger="click" @command="handleToolbarCommand">
                <div class="toolbar-btn" title="更多">
                  <i class="el-icon-more"></i>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="location">
                      <i class="el-icon-location"></i> 位置
                    </el-dropdown-item>
                    <el-dropdown-item command="video-call">
                      <i class="el-icon-video-camera"></i> 视频通话
                    </el-dropdown-item>
                    <el-dropdown-item command="voice-call">
                      <i class="el-icon-phone"></i> 语音通话
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>

            <!-- 输入框和发送按钮 -->
            <div class="input-container">
              <!-- 正在输入提示 -->
              <div v-if="someoneTyping" class="typing-indicator-bar">
                <span class="typing-text">{{ typingUsers.join(', ') }} 正在输入...</span>
                <div class="typing-animation">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>

              <!-- 输入框 -->
              <div class="input-wrapper">
                <el-input
                  type="textarea"
                  :rows="inputRows"
                  :placeholder="inputPlaceholder"
                  v-model="messageInput"
                  class="message-input"
                  :disabled="isRecording"
                  @keydown="handleKeyDown"
                  @input="handleInputChange"
                  @focus="handleInputFocus"
                  @blur="handleInputBlur">
                </el-input>

                <!-- 语音录制界面 -->
                <div v-if="isRecording" class="voice-recording">
                  <div class="recording-animation">
                    <div class="recording-circle"></div>
                  </div>
                  <div class="recording-text">正在录音... {{ recordingTime }}s</div>
                  <div class="recording-actions">
                    <el-button size="small" @click="cancelRecording">取消</el-button>
                    <el-button type="primary" size="small" @click="finishRecording">完成</el-button>
                  </div>
                </div>
              </div>

              <!-- 发送按钮 -->
              <div class="send-container">
                <el-button
                  type="primary"
                  :disabled="!canSend"
                  :loading="isSending"
                  @click="sendMessage">
                  {{ isRecording ? '录音中' : '发送' }}
                </el-button>
              </div>
            </div>
          </div>
        </div>

        <!-- 成员列表 -->
        <div v-show="showMemberList" class="member-list">
          <div class="member-header">
            <div class="member-title">
              <i
                :class="memberListExpanded ? 'el-icon-arrow-down' : 'el-icon-arrow-right'"
                @click="memberListExpanded = !memberListExpanded">
              </i>
              <span>成员列表</span>
            </div>
            <div class="add-member-btn" @click="addMember">
              <i class="el-icon-plus"></i>
            </div>
          </div>

          <div v-show="memberListExpanded" class="member-container">
            <div
              class="member-item"
              v-for="member in memberList"
              :key="member.id">
              <div class="member-avatar">
                <div class="avatar-text">{{ member.name.charAt(0) }}</div>
              </div>
              <div class="member-name">{{ member.name }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 文件列表区域 -->
      <div v-show="activeTab === 'files'" class="file-content">
        <div class="file-list">
          <div
            class="file-item"
            v-for="file in fileList"
            :key="file.id">
            <div class="file-info">
              <div class="file-icon">
                <i class="el-icon-document"></i>
              </div>
              <div class="file-details">
                <div class="file-name">{{ file.name }}</div>
                <div class="file-meta">
                  <span>与{{ file.sender }}的聊天</span>
                  <span>{{ formatTime(file.timestamp) }}</span>
                </div>
              </div>
            </div>
            <div class="file-actions">
              <el-button type="text" @click="downloadFile(file)">下载</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 图片上传对话框 -->
    <el-dialog
      title="选择图片"
      v-model="imageUploadVisible"
      width="30%">
      <el-upload
        action="#"
        list-type="picture-card"
        :auto-upload="false"
        :multiple="false"
        :on-change="handleImageChange"
        :on-remove="handleImageRemove"
        accept="image/jpeg,image/png">
        <i class="el-icon-plus"></i>
      </el-upload>
      <template #footer>
        <div>
          <el-button @click="imageUploadVisible = false">取消</el-button>
          <el-button type="primary" @click="uploadImage">发送</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文件上传对话框 -->
    <el-dialog
      title="选择文件"
      v-model="fileUploadVisible"
      width="30%">
      <el-upload
        action="#"
        :auto-upload="false"
        :multiple="false"
        :on-change="handleFileChange"
        :on-remove="handleFileRemove">
        <template #trigger>
          <el-button type="primary">选择文件</el-button>
        </template>
      </el-upload>
      <template #footer>
        <div>
          <el-button @click="fileUploadVisible = false">取消</el-button>
          <el-button type="primary" @click="uploadFile">发送</el-button>
        </div>
      </template>
    </el-dialog>
      </div>
    </div>
  </div>
</template>

<script>
import AppHeader from '@/components/common/AppHeader.vue'
import AppSidebar from '@/components/common/AppSidebar.vue'

export default {
  name: 'OnlineConsultation',

  components: {
    AppHeader,
    AppSidebar
  },
  computed: {
    // 过滤后的会话列表
    filteredConversationList() {
      if (!this.searchKeyword) {
        return this.conversationList
      }
      return this.conversationList.filter(conversation =>
        conversation.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
        conversation.lastMessage.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    },

    // 聊天容器样式
    chatContainerStyle() {
      return {
        width: this.showMemberList ? 'calc(100% - 200px)' : '100%'
      }
    },

    // 发送按钮样式
    sendBtnStyle() {
      return {
        opacity: this.canSend ? 1 : 0.5
      }
    }
  },
  data() {
    return {
      // 导航状态
      navExpanded: true,

      // 搜索关键词
      searchKeyword: '',
      searchResults: {
        contacts: [],
        conversations: []
      },
      isSearchFocused: false,

      // 会话列表
      conversationList: [],
      activeIndex: -1,
      currentConversation: null,

      // 输入相关
      inputRows: 3,
      inputPlaceholder: '请输入消息内容...',
      isSending: false,

      // 语音录制
      isRecording: false,
      recordingTime: 0,
      recordingTimer: null,
      mediaRecorder: null,

      // 正在输入状态
      someoneTyping: false,
      typingUsers: [],
      typingTimer: null,

      // 消息状态
      canSend: false,

      // 聊天相关
      activeTab: 'chat',
      messageList: [],
      messageInput: '',

      // 表情面板
      showEmojiPanel: false,
      emojiList: [],

      // 成员列表
      showMemberList: true,
      memberListExpanded: true,
      memberList: [],
      memberCount: 0,

      // 文件列表
      fileList: [],

      // 上传对话框
      imageUploadVisible: false,
      fileUploadVisible: false,

      // 样式控制
      showToggleBtn: true,
      hasMoreHistory: false,
      historyTip: '查看更多历史消息',
      showWithdrawTip: false,

      // 样式对象
      toggleBtnStyle: {
        width: '35px',
        height: '80px',
        backgroundColor: '#DADADA',
        borderRadius: '10px 0 0 10px',
        position: 'absolute',
        top: '50%',
        right: '19%',
        zIndex: 100,
        cursor: 'pointer',
        transform: 'translateY(-50%)'
      }
    }
  },
  watch: {
    // 监听消息输入长度
    messageInput() {
      const maxLength = 500
      if (this.messageInput.length > maxLength) {
        this.messageInput = this.messageInput.substring(0, maxLength)
        this.$message.warning('输入内容过长')
      }
    }
  },
  methods: {
    // 格式化时间
    formatTime(timestamp) {
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      return `${year}/${month}/${day} ${hours}:${minutes}`
    },

    // 搜索处理
    handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = { contacts: [], conversations: [] }
        return
      }

      // 搜索联系人
      this.searchContacts()

      // 搜索会话
      this.searchConversations()
    },

    // 搜索联系人
    searchContacts() {
      // 模拟联系人搜索
      const mockContacts = [
        { id: 'doctor1', name: '张医生', title: '心理医生', avatar: '/avatars/doctor1.jpg' },
        { id: 'doctor2', name: '李心理师', title: '心理咨询师', avatar: '/avatars/doctor2.jpg' },
        { id: 'doctor3', name: '王老师', title: '康复师', avatar: '/avatars/doctor3.jpg' }
      ]

      this.searchResults.contacts = mockContacts.filter(contact =>
        contact.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
        contact.title.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    },

    // 搜索会话
    searchConversations() {
      this.searchResults.conversations = this.conversationList
        .map((conv, index) => ({ ...conv, index }))
        .filter(conversation =>
          conversation.name.toLowerCase().includes(this.searchKeyword.toLowerCase()) ||
          conversation.lastMessage.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
    },

    // 搜索框聚焦
    onSearchFocus() {
      this.isSearchFocused = true
    },

    // 搜索框失焦
    onSearchBlur() {
      setTimeout(() => {
        this.isSearchFocused = false
      }, 200)
    },

    // 清除搜索
    clearSearch() {
      this.searchKeyword = ''
      this.searchResults = { contacts: [], conversations: [] }
    },

    // 显示联系人列表
    showContactList() {
      this.$message.info('联系人功能待实现')
    },

    // 与联系人开始会话
    startConversationWithContact(contact) {
      // 检查是否已存在会话
      const existingConv = this.conversationList.find(conv => conv.contactId === contact.id)
      if (existingConv) {
        const index = this.conversationList.indexOf(existingConv)
        this.selectConversation(index, existingConv)
      } else {
        // 创建新会话
        const newConversation = {
          id: Date.now(),
          contactId: contact.id,
          name: contact.name,
          type: 'private',
          avatar: contact.avatar,
          lastMessage: '',
          lastMessageTime: Date.now(),
          lastMessageType: 'text',
          unreadCount: 0,
          isOnline: true,
          isTyping: false,
          isPinned: false,
          isMuted: false,
          avatarColor: this.getAvatarColor(contact.name)
        }

        this.conversationList.unshift(newConversation)
        this.selectConversation(0, newConversation)
      }

      this.clearSearch()
    },

    // 显示会话右键菜单
    showConversationContextMenu(event, conversation) {
      // 这里可以实现右键菜单
      console.log('右键菜单:', event, conversation)
    },

    // 处理头像错误
    handleAvatarError(event) {
      event.target.style.display = 'none'
      event.target.nextElementSibling.style.display = 'flex'
    },

    // 获取头像颜色
    getAvatarColor(name) {
      const colors = [
        '#4682b4', '#67c23a', '#e6a23c', '#f56c6c',
        '#909399', '#409eff', '#9c27b0', '#ff5722'
      ]
      const index = name.charCodeAt(0) % colors.length
      return colors[index]
    },

    // 创建新会话
    createNewConversation() {
      this.$message.info('创建新会话功能待实现')
    },

    // 选择会话
    selectConversation(index, conversation) {
      this.activeIndex = index
      this.currentConversation = conversation
      this.loadMessageList(conversation.id)
      this.loadMemberList(conversation.id)
    },

    // 会话操作
    handleConversationAction(command) {
      switch (command.action) {
        case 'delete':
          this.deleteConversation(command.id)
          break
        case 'pin':
          this.togglePin(command.id)
          break
        case 'mute':
          this.toggleMute(command.id)
          break
        case 'clear':
          this.clearChatHistory(command.id)
          break
      }
    },

    // 置顶/取消置顶
    togglePin(conversationId) {
      const conversation = this.conversationList.find(c => c.id === conversationId)
      if (conversation) {
        conversation.isPinned = !conversation.isPinned
        this.$message.success(conversation.isPinned ? '已置顶' : '已取消置顶')
        // 重新排序会话列表，置顶的在前面
        this.sortConversationList()
      }
    },

    // 免打扰/取消免打扰
    toggleMute(conversationId) {
      const conversation = this.conversationList.find(c => c.id === conversationId)
      if (conversation) {
        conversation.isMuted = !conversation.isMuted
        this.$message.success(conversation.isMuted ? '已开启免打扰' : '已关闭免打扰')
      }
    },

    // 清空聊天记录
    clearChatHistory(conversationId) {
      this.$confirm('确定要清空聊天记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里调用清空API
        console.log('清空会话记录:', conversationId)
        this.messageList = []
        this.$message.success('聊天记录已清空')
      }).catch(() => {
        this.$message.info('已取消')
      })
    },

    // 排序会话列表
    sortConversationList() {
      this.conversationList.sort((a, b) => {
        if (a.isPinned && !b.isPinned) return -1
        if (!a.isPinned && b.isPinned) return 1
        return new Date(b.lastMessageTime) - new Date(a.lastMessageTime)
      })
    },

    // 删除会话
    deleteConversation(conversationId) {
      this.$confirm('确定要删除这个会话吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 这里调用删除API
        this.conversationList = this.conversationList.filter(
          conversation => conversation.id !== conversationId
        )
        this.$message.success('删除成功')
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 切换标签页
    switchTab(tab) {
      this.activeTab = tab
      if (tab === 'files') {
        this.loadFileList()
      }
    },

    // 切换成员列表显示
    toggleMemberList() {
      this.showMemberList = !this.showMemberList

      // 调整聊天容器宽度
      if (this.showMemberList) {
        this.toggleBtnStyle.right = '19%'
      } else {
        this.toggleBtnStyle.right = '5px'
      }
    },

    // 加载更多历史消息
    loadMoreHistory() {
      this.$message.info('加载历史消息功能待实现')
    },

    // 显示消息菜单
    showMessageMenu(message) {
      if (message.isOwn) {
        this.showWithdrawTip = true
        this.selectedMessage = message

        // 3秒后自动隐藏
        setTimeout(() => {
          this.showWithdrawTip = false
        }, 3000)
      }
    },

    // 处理消息点击
    handleMessageClick(message) {
      if (message.type === 'file') {
        this.downloadFile(message)
      }
    },

    // 撤回消息
    withdrawMessage() {
      if (this.selectedMessage) {
        // 这里调用撤回API
        this.$message.success('消息已撤回')
        this.showWithdrawTip = false
      }
    },

    // 切换表情面板
    toggleEmojiPanel() {
      this.showEmojiPanel = !this.showEmojiPanel
    },

    // 插入表情
    insertEmoji(emoji) {
      this.messageInput += emoji.name
      this.showEmojiPanel = false
    },

    // 打开图片上传
    openImageUpload() {
      if (!this.currentConversation) {
        this.$message.error('请先选择会话')
        return
      }
      this.imageUploadVisible = true
    },

    // 打开文件上传
    openFileUpload() {
      if (!this.currentConversation) {
        this.$message.error('请先选择会话')
        return
      }
      this.fileUploadVisible = true
    },

    // 处理图片变化
    handleImageChange(file) {
      this.selectedImage = file.raw
    },

    // 移除图片
    handleImageRemove() {
      this.selectedImage = null
    },

    // 上传图片
    uploadImage() {
      if (!this.selectedImage) {
        this.$message.error('请选择图片')
        return
      }

      // 这里调用上传API
      this.$message.success('图片发送成功')
      this.imageUploadVisible = false
      this.selectedImage = null
    },

    // 处理文件变化
    handleFileChange(file) {
      this.selectedFile = file.raw
    },

    // 移除文件
    handleFileRemove() {
      this.selectedFile = null
    },

    // 上传文件
    uploadFile() {
      if (!this.selectedFile) {
        this.$message.error('请选择文件')
        return
      }

      // 这里调用上传API
      this.$message.success('文件发送成功')
      this.fileUploadVisible = false
      this.selectedFile = null
    },

    // 侧边栏事件处理
    expandNav() {
      this.navExpanded = true
    },

    collapseNav() {
      this.navExpanded = false
    },

    // 键盘事件处理
    handleKeyDown(event) {
      if (event.shiftKey && event.keyCode === 13) {
        // Shift + Enter 换行
        return
      } else if (event.keyCode === 13) {
        // Enter 发送消息
        event.preventDefault()
        this.sendMessage()
      }
    },

    // 输入框变化处理
    handleInputChange() {
      this.canSend = this.messageInput.trim().length > 0
      this.sendTypingStatus()
    },

    // 输入框聚焦
    handleInputFocus() {
      this.inputRows = 4
    },

    // 输入框失焦
    handleInputBlur() {
      if (!this.messageInput.trim()) {
        this.inputRows = 3
      }
    },

    // 发送正在输入状态
    sendTypingStatus() {
      // 这里可以发送正在输入的状态给其他用户
      if (this.typingTimer) {
        clearTimeout(this.typingTimer)
      }
      this.typingTimer = setTimeout(() => {
        // 停止输入状态
      }, 3000)
    },

    // 语音录制相关方法
    async toggleVoiceRecord() {
      if (this.isRecording) {
        this.finishRecording()
      } else {
        await this.startRecording()
      }
    },

    async startRecording() {
      try {
        const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
        this.mediaRecorder = new MediaRecorder(stream)
        this.isRecording = true
        this.recordingTime = 0

        this.recordingTimer = setInterval(() => {
          this.recordingTime++
        }, 1000)

        this.mediaRecorder.start()
        this.$message.success('开始录音')
      } catch (error) {
        this.$message.error('无法访问麦克风')
        console.error('录音失败:', error)
      }
    },

    finishRecording() {
      if (this.mediaRecorder && this.isRecording) {
        this.mediaRecorder.stop()
        this.isRecording = false
        clearInterval(this.recordingTimer)

        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            // 处理录音数据
            this.sendVoiceMessage(event.data)
          }
        }
      }
    },

    cancelRecording() {
      if (this.mediaRecorder && this.isRecording) {
        this.mediaRecorder.stop()
        this.isRecording = false
        clearInterval(this.recordingTimer)
        this.recordingTime = 0
        this.$message.info('录音已取消')
      }
    },

    // 发送语音消息
    sendVoiceMessage(audioBlob) {
      // 这里处理语音消息发送
      console.log('发送语音消息:', audioBlob)
      this.$message.success('语音消息发送成功')
    },

    // 工具栏命令处理
    handleToolbarCommand(command) {
      switch (command) {
        case 'location':
          this.sendLocation()
          break
        case 'video-call':
          this.startVideoCall()
          break
        case 'voice-call':
          this.startVoiceCall()
          break
      }
    },

    // 发送位置
    sendLocation() {
      this.$message.info('位置功能待实现')
    },

    // 开始视频通话
    startVideoCall() {
      this.$message.info('视频通话功能待实现')
    },

    // 开始语音通话
    startVoiceCall() {
      this.$message.info('语音通话功能待实现')
    },

    // 截图功能
    takeScreenshot() {
      this.$message.info('截图功能待实现')
    },

    // 消息相关方法
    previewImage(src) {
      // 图片预览
      console.log('预览图片:', src)
    },

    downloadFile(message) {
      // 文件下载
      console.log('下载文件:', message.fileName)
      this.$message.success('开始下载文件')
    },

    playVoice(message) {
      // 播放语音
      message.isPlaying = !message.isPlaying
      console.log('播放语音:', message)
    },

    // 获取文件图标
    getFileIcon(fileName) {
      const ext = fileName.split('.').pop().toLowerCase()
      const iconMap = {
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        xls: 'el-icon-s-grid',
        xlsx: 'el-icon-s-grid',
        ppt: 'el-icon-present',
        pptx: 'el-icon-present',
        zip: 'el-icon-folder-opened',
        rar: 'el-icon-folder-opened',
        txt: 'el-icon-document',
        default: 'el-icon-document'
      }
      return iconMap[ext] || iconMap.default
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 发送消息
    sendMessage() {
      if (!this.currentConversation) {
        this.$message.error('请先选择会话')
        return
      }

      if (!this.messageInput.trim()) {
        this.$message.error('请输入消息内容')
        return
      }

      if (this.messageInput.length > 500) {
        this.$message.error('消息内容过长')
        return
      }

      // 创建新消息
      const newMessage = {
        id: Date.now(),
        content: this.messageInput,
        timestamp: Date.now(),
        isOwn: true,
        userName: this.user.userName || '我',
        type: 'text'
      }

      // 添加到消息列表
      this.messageList.push(newMessage)

      // 清空输入框
      this.messageInput = ''

      // 滚动到底部
      this.$nextTick(() => {
        this.scrollToBottom()
      })

      // 这里调用发送消息API
      this.$message.success('消息发送成功')
    },

    // 滚动到底部
    scrollToBottom() {
      const container = document.getElementById('messageContainer')
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    },

    // 添加成员
    addMember() {
      this.$message.info('添加成员功能待实现')
    },



    // 加载会话列表
    loadConversationList() {
      // 模拟数据，实际应该调用API
      this.conversationList = [
        {
          id: 1,
          name: '张医生',
          type: 'private',
          lastMessage: '您好，有什么可以帮助您的吗？',
          lastMessageTime: Date.now() - 300000, // 5分钟前
          lastMessageType: 'text',
          unreadCount: 2,
          isOnline: true,
          isTyping: false,
          isPinned: true,
          isMuted: false,
          avatarColor: '#4682b4'
        },
        {
          id: 2,
          name: 'ADHD康复群',
          type: 'group',
          lastMessage: '大家好，今天的训练任务完成了吗？',
          lastMessageTime: Date.now() - 1800000, // 30分钟前
          lastMessageType: 'text',
          unreadCount: 0,
          isOnline: false,
          isTyping: false,
          isPinned: false,
          isMuted: false
        },
        {
          id: 3,
          name: '李心理师',
          type: 'private',
          lastMessage: '[图片]',
          lastMessageTime: Date.now() - 3600000, // 1小时前
          lastMessageType: 'image',
          unreadCount: 1,
          isOnline: false,
          isTyping: false,
          isPinned: false,
          isMuted: true,
          avatarColor: '#67c23a'
        },
        {
          id: 4,
          name: '王老师',
          type: 'private',
          lastMessage: '训练报告.pdf',
          lastMessageTime: Date.now() - 7200000, // 2小时前
          lastMessageType: 'file',
          unreadCount: 0,
          isOnline: true,
          isTyping: true,
          isPinned: false,
          isMuted: false,
          avatarColor: '#e6a23c'
        }
      ]

      // 排序会话列表
      this.sortConversationList()
    },

    // 加载消息列表
    loadMessageList(conversationId) {
      // 模拟数据，实际应该调用API
      console.log('Loading messages for conversation:', conversationId)
      this.messageList = [
        {
          id: 1,
          content: '你好！',
          timestamp: Date.now() - 3600000,
          isOwn: false,
          userName: '张三',
          type: 'text'
        },
        {
          id: 2,
          content: '你好，最近怎么样？',
          timestamp: Date.now() - 1800000,
          isOwn: true,
          userName: '我',
          type: 'text'
        }
      ]
    },

    // 加载成员列表
    loadMemberList(conversationId) {
      // 模拟数据，实际应该调用API
      console.log('Loading members for conversation:', conversationId)
      this.memberList = [
        { id: 1, name: '张三' },
        { id: 2, name: '李四' },
        { id: 3, name: '王五' }
      ]
      this.memberCount = this.memberList.length
    },

    // 加载文件列表
    loadFileList() {
      // 模拟数据，实际应该调用API
      this.fileList = [
        {
          id: 1,
          name: '项目文档.pdf',
          sender: '张三',
          timestamp: Date.now() - 86400000
        },
        {
          id: 2,
          name: '会议纪要.docx',
          sender: '李四',
          timestamp: Date.now() - 172800000
        }
      ]
    }
  },

  mounted() {
    // 组件挂载后加载数据
    this.loadConversationList()

    // 初始化表情列表
    this.emojiList = [
      { name: '[微笑]', url: '/assets/emoji/smile.png' },
      { name: '[大笑]', url: '/assets/emoji/laugh.png' },
      // 更多表情...
    ]
  }
}
</script>

<style scoped>
/* 页面容器 */
.page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f5f5f5;
}

/* 内容包装区 */
.content-wrapper {
  display: flex;
  height: calc(100vh - var(--header-height, 80px));
  overflow: hidden;
}

/* 聊天主容器 - 专业医疗主题 */
.online-consultation {
  display: flex;
  flex: 1;
  height: 100%;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  margin: 16px;
  overflow: hidden;
}

/* 会话列表面板容器 */
.conversation-list-panel-container {
  display: flex;
  flex-direction: column;
  width: 320px;
  border-right: 1px solid #e8eaec;
  background: #fafbfc;
}

/* 搜索视图 */
.search-view {
  background: #ffffff;
  border-bottom: 1px solid #e8eaec;
  padding: 16px;
}

.search-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
  display: flex;
  align-items: center;
  background: #f5f6f7;
  border-radius: 20px;
  padding: 8px 16px;
  transition: all 0.3s ease;
}

.search-input-wrapper:focus-within {
  background: #ffffff;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.search-icon {
  color: #8c9196;
  font-size: 14px;
  margin-right: 8px;
}

.search-input {
  flex: 1;
  border: none;
  background: transparent;
  outline: none;
  font-size: 14px;
  color: #2c3e50;
}

.search-input::placeholder {
  color: #8c9196;
}

.clear-icon {
  color: #8c9196;
  font-size: 14px;
  cursor: pointer;
  margin-left: 8px;
  transition: color 0.3s ease;
}

.clear-icon:hover {
  color: #409eff;
}

.search-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 36px;
  height: 36px;
  border-radius: 18px;
  background: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 16px;
}

.action-btn:hover {
  background: #66b1ff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

/* 搜索结果 */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 400px;
  overflow-y: auto;
  margin-top: 4px;
}

.search-section {
  padding: 8px 0;
}

.section-title {
  padding: 8px 16px;
  font-size: 12px;
  color: #8c9196;
  font-weight: 500;
  background: #f5f6f7;
}

.search-result-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.search-result-item:hover {
  background: #f5f6f7;
}

.result-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  margin-right: 12px;
  object-fit: cover;
}

.result-info {
  flex: 1;
}

.result-name {
  font-size: 14px;
  color: #2c3e50;
  font-weight: 500;
  margin-bottom: 2px;
}

.result-desc {
  font-size: 12px;
  color: #8c9196;
}

.search-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #8c9196;
}

.search-empty .empty-icon {
  font-size: 48px;
  margin-bottom: 12px;
  opacity: 0.5;
}

.search-empty .empty-text {
  font-size: 14px;
}

/* 会话面板 */
.conversation-panel {
  height: calc(100% - 80px);
  max-height: calc(100% - 80px);
  position: relative;
  background-color: #fafbfc;
  overflow-y: auto;
  flex: 1;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .online-consultation {
    flex-direction: column;
    margin: 10px;
    border-radius: 8px;
  }

  .conversation-list-panel-container {
    width: 100%;
    max-height: 200px;
  }

  .content-wrapper {
    height: calc(100vh - var(--header-height, 80px) - 20px);
  }
}

/* 左侧会话列表样式 - 冰蓝主题 */
.conversation-sidebar {
  width: 100%;
  max-height: 200px;
  background: linear-gradient(135deg, rgba(240, 248, 255, 0.95) 0%, rgba(230, 243, 255, 0.9) 100%);
  border-bottom: 1px solid rgba(135, 206, 235, 0.3);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  backdrop-filter: blur(15px);
  border-radius: 16px 16px 0 0;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.5);
}

@media (min-width: 769px) {
  .conversation-sidebar {
    width: 300px;
    max-height: 100vh;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    border-bottom: none;
    border-radius: 12px 0 0 12px;
  }

  .online-consultation {
    flex-direction: row;
  }
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #e8e8e8;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #333;
}

.header-search {
  margin-bottom: 15px;
}

.search-input {
  width: 100%;
}

.header-add {
  text-align: center;
}

.add-btn {
  width: 40px;
  height: 40px;
  background-color: #409eff;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: background-color 0.3s;
}

.add-btn:hover {
  background-color: #66b1ff;
}

.conversation-list {
  flex: 1;
  overflow-y: auto;
}

.conversation-item {
  padding: 15px 20px;
  border-bottom: 1px solid rgba(135, 206, 235, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 4px 8px;
}

.conversation-item:hover {
  background: linear-gradient(135deg, rgba(173, 216, 230, 0.3) 0%, rgba(135, 206, 235, 0.2) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(70, 130, 180, 0.15);
}

.conversation-item.active {
  background: linear-gradient(135deg, rgba(70, 130, 180, 0.2) 0%, rgba(135, 206, 235, 0.3) 100%);
  border: 1px solid rgba(70, 130, 180, 0.4);
  box-shadow: 0 4px 16px rgba(70, 130, 180, 0.2);
}

.conversation-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.conversation-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.avatar-container {
  margin-right: 12px;
}

.avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4682b4 0%, #87ceeb 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
  box-shadow: 0 4px 12px rgba(70, 130, 180, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.8);
}

.avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
}

.default-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  font-weight: bold;
}

.group-avatar {
  background-color: #67c23a;
}

.user-avatar {
  background: linear-gradient(135deg, #4682b4 0%, #5a9fd4 100%);
}

/* 头像包装器和在线状态 */
.avatar-wrapper {
  position: relative;
  display: inline-block;
}

.online-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.online-status.online {
  background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
  animation: pulse 2s infinite;
}

.online-status.offline {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 255, 136, 0.7);
  }
  70% {
    box-shadow: 0 0 0 6px rgba(0, 255, 136, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 255, 136, 0);
  }
}

/* 正在输入指示器 */
.typing-indicator {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
  border-radius: 50%;
  border: 2px solid white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.typing-dots {
  display: flex;
  gap: 1px;
}

.typing-dots span {
  width: 2px;
  height: 2px;
  background: white;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #999;
}

.empty-icon {
  font-size: 48px;
  color: #d3d4d6;
  margin-bottom: 16px;
}

.empty-text {
  font-size: 16px;
  color: #666;
  margin-bottom: 8px;
}

.empty-subtext {
  font-size: 14px;
  color: #999;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 5px;
}

.conversation-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
}

.conversation-time {
  font-size: 12px;
  color: #999;
  margin-left: 8px;
  white-space: nowrap;
}

.last-message-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.last-message {
  font-size: 14px;
  color: #999;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  display: flex;
  align-items: center;
}

.last-message.unread {
  color: #333;
  font-weight: 500;
}

.message-type-icon {
  margin-right: 4px;
  font-size: 12px;
}

.pin-indicator {
  color: #f56c6c;
  font-size: 12px;
  margin-left: 8px;
}

.conversation-right {
  margin-left: 10px;
}

.more-btn {
  color: #999;
  cursor: pointer;
  padding: 5px;
}

.more-btn:hover {
  color: #409eff;
}

/* 聊天主区域样式 - 冰蓝主题 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  min-height: 0;
  background: linear-gradient(135deg, rgba(248, 252, 255, 0.95) 0%, rgba(240, 248, 255, 0.9) 100%);
  backdrop-filter: blur(15px);
  border-radius: 0 16px 16px 0;
  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

/* 移动端竖屏布局 */
@media (max-width: 768px) {
  .chat-main {
    height: calc(100vh - 200px);
    border-radius: 0 0 12px 12px;
  }
}

.chat-header {
  height: 60px;
  background: linear-gradient(135deg, rgba(248, 252, 255, 0.95) 0%, rgba(230, 243, 255, 0.9) 100%);
  border-bottom: 1px solid rgba(135, 206, 235, 0.3);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  backdrop-filter: blur(15px);
  box-shadow: 0 2px 8px rgba(70, 130, 180, 0.1);
}

.chat-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
}

.chat-actions {
  display: flex;
  align-items: center;
}

.action-tab {
  padding: 10px 18px;
  margin-right: 12px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #4682b4;
  background: rgba(240, 248, 255, 0.6);
  border: 1px solid rgba(135, 206, 235, 0.3);
  font-weight: 500;
}

.action-tab.active {
  background: linear-gradient(135deg, #4682b4 0%, #5a9fd4 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(70, 130, 180, 0.3);
  transform: translateY(-1px);
}

.action-tab:hover:not(.active) {
  background: linear-gradient(135deg, rgba(173, 216, 230, 0.4) 0%, rgba(135, 206, 235, 0.3) 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(70, 130, 180, 0.2);
}

.settings-btn {
  margin-left: 10px;
  color: #999;
  cursor: pointer;
  padding: 5px;
}

.settings-btn:hover {
  color: #409eff;
}

.chat-content {
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
  min-height: 0;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  min-height: 0;
}

/* 移动端调整 */
@media (max-width: 768px) {
  .chat-content {
    flex-direction: column;
  }

  .chat-container {
    width: 100%;
  }
}

.toggle-btn {
  position: absolute;
  top: 50%;
  right: 0;
  transform: translateY(-50%);
  background-color: #e8e8e8;
  border-radius: 4px 0 0 4px;
  padding: 10px 5px;
  cursor: pointer;
  z-index: 100;
  color: #666;
}

.toggle-btn:hover {
  background-color: #d8d8d8;
}

.message-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
}

.history-tip {
  text-align: center;
  padding: 10px;
  color: #409eff;
  cursor: pointer;
  border-bottom: 1px solid #f0f0f0;
}

.history-tip:hover {
  background-color: #f5f7fa;
}

.message-list {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  min-height: 0;
  scroll-behavior: smooth;
}

/* 自定义滚动条样式 */
.message-list::-webkit-scrollbar {
  width: 6px;
}

.message-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.message-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 移动端消息列表优化 */
@media (max-width: 768px) {
  .message-list {
    padding: 10px;
    font-size: 14px;
  }
}

.message-item {
  margin-bottom: 20px;
}

.message-wrapper {
  display: flex;
  align-items: flex-start;
}

.message-wrapper.own {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  margin: 0 10px;
}

.message-avatar.own {
  background-color: #67c23a;
}

.avatar-text {
  font-weight: bold;
}

.message-info {
  max-width: 60%;
}

.user-time {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 12px;
  color: #999;
}

.user-time.own {
  flex-direction: row-reverse;
}

.user-name {
  margin-right: 10px;
}

.user-time.own .user-name {
  margin-right: 0;
  margin-left: 10px;
}

.message-time {
  font-size: 12px;
}

/* 消息状态样式 */
.message-status {
  margin-left: 8px;
  font-size: 12px;
  color: #87ceeb;
}

.message-status .el-icon-loading {
  animation: rotating 2s linear infinite;
  color: #87ceeb;
}

.message-status .el-icon-check {
  color: #87ceeb;
}

.message-status .message-delivered {
  color: #4682b4;
}

.message-status .message-read {
  color: #00ff88;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.message-content {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 252, 255, 0.9) 100%);
  padding: 12px 18px;
  border-radius: 18px;
  box-shadow: 0 4px 12px rgba(70, 130, 180, 0.15);
  word-wrap: break-word;
  line-height: 1.5;
  border: 1px solid rgba(135, 206, 235, 0.2);
  backdrop-filter: blur(10px);
  position: relative;
}

.message-content.own {
  background: linear-gradient(135deg, #4682b4 0%, #5a9fd4 100%);
  color: white;
  box-shadow: 0 4px 16px rgba(70, 130, 180, 0.3);
}

.message-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 18px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
  pointer-events: none;
}

/* 不同类型消息样式 */
.text-message {
  line-height: 1.5;
}

.image-message {
  max-width: 200px;
  border-radius: 12px;
  overflow: hidden;
}

.message-image {
  width: 100%;
  max-width: 200px;
  border-radius: 8px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.message-image:hover {
  transform: scale(1.02);
}

.image-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #999;
  background: #f5f5f5;
  border-radius: 8px;
}

.file-message {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

.file-message:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.file-icon {
  font-size: 24px;
  color: #409eff;
  margin-right: 12px;
}

.file-info {
  flex: 1;
}

.file-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  font-weight: 500;
}

.file-size {
  font-size: 12px;
  color: #999;
}

.file-download {
  font-size: 16px;
  color: #409eff;
  margin-left: 8px;
}

.voice-message {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.voice-message:hover {
  background: rgba(255, 255, 255, 1);
  transform: scale(1.02);
}

.voice-icon {
  font-size: 18px;
  color: #67c23a;
  margin-right: 8px;
}

.voice-duration {
  font-size: 14px;
  color: #333;
  margin-right: 8px;
}

.voice-waves {
  display: flex;
  align-items: center;
  gap: 2px;
}

.voice-waves span {
  width: 2px;
  height: 12px;
  background: #67c23a;
  border-radius: 1px;
  animation: wave 1s infinite ease-in-out;
}

.voice-waves span:nth-child(1) {
  animation-delay: 0s;
}

.voice-waves span:nth-child(2) {
  animation-delay: 0.1s;
}

.voice-waves span:nth-child(3) {
  animation-delay: 0.2s;
}

@keyframes wave {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}

.system-message {
  text-align: center;
  color: #999;
  font-size: 12px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  margin: 8px 0;
}

.withdraw-message {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 12px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  margin: 8px 0;
}

.withdraw-message i {
  margin-right: 4px;
}

.withdraw-tip {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 20px;
  border-radius: 4px;
  z-index: 1000;
}

.withdraw-btn {
  cursor: pointer;
}

.emoji-panel {
  position: absolute;
  bottom: 120px;
  left: 20px;
  width: 300px;
  height: 200px;
  background-color: white;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.emoji-list {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  overflow-y: auto;
  height: 100%;
}

.emoji-item {
  width: 40px;
  height: 40px;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.emoji-item:hover {
  background-color: #f5f7fa;
}

.emoji-item img {
  width: 30px;
  height: 30px;
}

.input-area {
  background: linear-gradient(135deg, rgba(248, 252, 255, 0.95) 0%, rgba(240, 248, 255, 0.9) 100%);
  border-top: 1px solid rgba(135, 206, 235, 0.3);
  padding: 20px;
  flex-shrink: 0;
  backdrop-filter: blur(15px);
  box-shadow: 0 -2px 8px rgba(70, 130, 180, 0.1);
}

/* 移动端输入区域优化 */
@media (max-width: 768px) {
  .input-area {
    padding: 10px;
    position: sticky;
    bottom: 0;
    z-index: 100;
  }

  .input-toolbar {
    flex-wrap: wrap;
    gap: 5px;
  }

  .toolbar-btn {
    margin-right: 8px;
    padding: 8px;
  }
}

.input-toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.toolbar-btn {
  margin-right: 15px;
  color: #4682b4;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  background: rgba(240, 248, 255, 0.6);
  border: 1px solid rgba(135, 206, 235, 0.2);
  font-size: 16px;
}

.toolbar-btn:hover {
  background: linear-gradient(135deg, rgba(173, 216, 230, 0.4) 0%, rgba(135, 206, 235, 0.3) 100%);
  color: #2c5aa0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(70, 130, 180, 0.2);
}

.toolbar-btn.active {
  background: linear-gradient(135deg, #4682b4 0%, #5a9fd4 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(70, 130, 180, 0.3);
}

/* 正在输入提示栏 */
.typing-indicator-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  background: rgba(64, 158, 255, 0.1);
  border-radius: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #409eff;
}

.typing-text {
  flex: 1;
}

.typing-animation {
  display: flex;
  gap: 2px;
}

.typing-animation span {
  width: 4px;
  height: 4px;
  background: #409eff;
  border-radius: 50%;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-animation span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-animation span:nth-child(2) {
  animation-delay: -0.16s;
}

/* 输入框包装器 */
.input-wrapper {
  position: relative;
  flex: 1;
}

/* 语音录制界面 */
.voice-recording {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(103, 194, 58, 0.1) 0%, rgba(103, 194, 58, 0.05) 100%);
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.recording-animation {
  margin-bottom: 12px;
}

.recording-circle {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #67c23a 0%, #85ce61 100%);
  border-radius: 50%;
  animation: recording-pulse 1.5s infinite ease-in-out;
}

@keyframes recording-pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0.7);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10px rgba(103, 194, 58, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(103, 194, 58, 0);
  }
}

.recording-text {
  font-size: 14px;
  color: #67c23a;
  margin-bottom: 12px;
  font-weight: 500;
}

.recording-actions {
  display: flex;
  gap: 8px;
}

/* 发送按钮容器 */
.send-container {
  margin-left: 12px;
  display: flex;
  align-items: flex-end;
}

/* 消息状态样式增强 */
.message-status {
  margin-left: 8px;
  font-size: 12px;
  color: #87ceeb;
}

.message-status .rotating {
  animation: rotating 2s linear infinite;
}

.message-status .message-delivered {
  color: #4682b4;
}

.message-status .message-read {
  color: #00ff88;
}

.message-status .message-failed {
  color: #f56c6c;
  cursor: pointer;
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.input-container {
  display: flex;
  align-items: flex-end;
  gap: 10px;
}

.message-input {
  flex: 1;
}

.send-container {
  display: flex;
  align-items: center;
}

.member-list {
  width: 250px;
  background-color: rgba(255, 255, 255, 0.9);
  border-left: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

/* 移动端成员列表调整 */
@media (max-width: 768px) {
  .member-list {
    width: 100%;
    max-height: 150px;
    border-left: none;
    border-top: 1px solid #e8e8e8;
    order: 3;
  }
}

.member-header {
  padding: 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.member-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
}

.member-title i {
  margin-right: 8px;
}

.add-member-btn {
  color: #409eff;
  cursor: pointer;
  padding: 5px;
}

.add-member-btn:hover {
  background-color: #f5f7fa;
  border-radius: 4px;
}

.member-container {
  flex: 1;
  overflow-y: auto;
  padding: 10px 20px;
}

.member-item {
  display: flex;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.member-avatar {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background-color: #409eff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 12px;
  margin-right: 10px;
}

.member-name {
  font-size: 14px;
  color: #333;
}

.file-content {
  flex: 1;
  padding: 20px;
  background-color: #fafafa;
}

.file-list {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  margin-right: 15px;
  color: #409eff;
  font-size: 24px;
}

.file-details {
  flex: 1;
}

.file-name {
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.file-meta {
  font-size: 12px;
  color: #999;
}

.file-meta span {
  margin-right: 15px;
}

.file-actions {
  margin-left: 15px;
}

/* 移动端全局优化 */
@media (max-width: 768px) {
  /* 触摸滚动优化 */
  .conversation-list,
  .message-list,
  .member-container {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 防止页面缩放 */
  .online-consultation {
    touch-action: manipulation;
  }

  /* 消息气泡在移动端的优化 */
  .message-content {
    max-width: 85%;
    word-break: break-word;
    hyphens: auto;
  }

  /* 头像在移动端的调整 */
  .message-avatar {
    width: 35px;
    height: 35px;
    font-size: 12px;
  }

  /* 会话项在移动端的调整 */
  .conversation-item {
    padding: 12px 15px;
  }

  .conversation-name {
    font-size: 15px;
  }

  .last-message {
    font-size: 13px;
    max-width: 120px;
  }
}

/* 横屏模式优化 */
@media (max-width: 768px) and (orientation: landscape) {
  .conversation-sidebar {
    max-height: 120px;
  }

  .chat-main {
    height: calc(100vh - 120px);
  }

  .member-list {
    max-height: 100px;
  }
}
</style>