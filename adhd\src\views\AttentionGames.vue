<template>
    <div class="page-container">
    <!-- 公共头部组件 -->
    <AppHeader
      :user="currentUser"
      current-route="attention-games"
      @search="handleSearch"
      @show-notifications="showNotifications"
      @show-messages="showMessages"
      @show-settings="showSettings"
      @show-user-menu="showUserMenu"
    />

    <!-- 内容包装区 -->
    <div class="content-wrapper">
      <!-- 公共侧边栏组件 -->
      <AppSidebar
        current-route="attention-games"
        :expanded="navExpanded"
        @expand="expandNav"
        @collapse="collapseNav"
      />

      <!-- 主内容区 -->
      <div class="main-area">
        <div class="page-main-container">
          <div class="area-header">
            <h3><i class="fas fa-gamepad"></i> 注意力训练游戏</h3>
            <div class="header-actions">
              <button @click="showGameHistory" class="btn-secondary">
                <i class="fas fa-history"></i> 游戏记录
              </button>
              <button @click="clearGameData" class="btn-danger">
                <i class="fas fa-trash"></i> 清除数据
              </button>
            </div>
          </div>

          <!-- 游戏统计信息 -->
          <div class="game-stats">
            <div class="stat-item">
              <i class="fas fa-play"></i>
              <span>总游戏次数: {{ totalPlayCount }}</span>
            </div>
            <div class="stat-item">
              <i class="fas fa-check"></i>
              <span>已完成游戏: {{ completedGamesCount }}/{{ games.length }}</span>
            </div>
            <div class="stat-item">
              <i class="fas fa-clock"></i>
              <span>最近记录: {{ gameHistory.length }} 条</span>
            </div>
          </div>

          <!-- 主要内容 -->
          <div class="main-content">
        <!-- 搜索和筛选 -->
        <div class="search-filter-section">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input
              type="text"
              v-model="searchQuery"
              placeholder="搜索游戏名称或描述..."
              class="search-input"
            />
            <button v-if="searchQuery" @click="searchQuery = ''" class="clear-search">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>



        <!-- 游戏分类 -->
        <div class="game-categories">
          <button
            v-for="category in gameCategories"
            :key="category.id"
            @click="selectCategory(category.id)"
            :class="['category-btn', { active: selectedCategory === category.id }]"
          >
            <i :class="category.icon"></i>
            <span>{{ category.name }}</span>
          </button>
        </div>

        <!-- 游戏列表 -->
        <div class="games-grid">
          <div
            v-for="game in filteredGames"
            :key="game.id"
            class="game-card"
            :class="{ 'completed': game.completed, 'played': game.playCount > 0 }"
          >
            <!-- 游戏头部：图标和状态 -->
            <div class="game-header">
              <div class="game-icon">
                <i :class="game.icon"></i>
              </div>
              <div class="game-status-badge">
                <span v-if="game.completed && game.playCount > 0" class="status-badge completed">
                  <i class="fas fa-check-circle"></i>
                </span>
                <span v-else-if="game.playCount > 0" class="status-badge played">
                  <i class="fas fa-play-circle"></i>
                </span>
                <span v-else class="status-badge unplayed">
                  <i class="fas fa-circle"></i>
                </span>
              </div>
            </div>

            <!-- 游戏内容 -->
            <div class="game-content">
              <h3 class="game-title">{{ game.name }}</h3>
              <p class="game-description">{{ game.description }}</p>

              <!-- 游戏属性网格 -->
              <div class="game-attributes">
                <div class="attribute-item">
                  <span class="attribute-label">难度</span>
                  <span class="difficulty-badge" :class="game.difficulty">
                    <i class="fas fa-star"></i>
                    {{ getDifficultyText(game.difficulty) }}
                  </span>
                </div>
                <div class="attribute-item">
                  <span class="attribute-label">时长</span>
                  <span class="duration-badge">
                    <i class="fas fa-clock"></i>
                    {{ game.duration }}分钟
                  </span>
                </div>
                <div class="attribute-item">
                  <span class="attribute-label">次数</span>
                  <span class="count-badge">
                    <i class="fas fa-gamepad"></i>
                    {{ game.playCount }}次
                  </span>
                </div>
                <div class="attribute-item">
                  <span class="attribute-label">最佳</span>
                  <span class="score-badge" :class="{ 'has-score': game.bestScore > 0 }">
                    <i class="fas fa-trophy"></i>
                    <span v-if="game.bestScore > 0">{{ game.bestScore }}分</span>
                    <span v-else>--</span>
                  </span>
                </div>
              </div>
            </div>

            <!-- 游戏操作 -->
            <div class="game-footer">
              <button class="play-button" @click="startGame(game)">
                <i class="fas fa-play"></i>
                <span v-if="game.playCount === 0">开始游戏</span>
                <span v-else>再次游戏</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 空状态提示 -->
        <div v-if="filteredGames.length === 0" class="empty-state">
          <div class="empty-icon">
            <i class="fas fa-search"></i>
          </div>
          <h3>没有找到匹配的游戏</h3>
          <p>请尝试调整搜索条件或选择其他分类</p>
          <button @click="searchQuery = ''; selectedCategory = 'all'" class="reset-filter-btn">
            <i class="fas fa-refresh"></i>
            重置筛选
          </button>
        </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
import AppHeader from '@/components/common/AppHeader.vue'
import AppSidebar from '@/components/common/AppSidebar.vue'

export default {
  name: 'AttentionGames',
  components: {
    AppHeader,
    AppSidebar
  },
  data() {
    return {
      // 用户信息
      currentUser: {
        name: '张同学',
        avatar: 'https://randomuser.me/api/portraits/men/44.jpg'
      },

      // 导航状态
      navExpanded: false,
      selectedCategory: 'all',
      searchQuery: '',

      // 游戏状态
      currentGame: null,
      gameStartTime: null,
      gameHistory: [],
      
      // 游戏分类
      gameCategories: [
        { id: 'all', name: '全部游戏', icon: 'fas fa-th' },
        { id: 'focus', name: '专注训练', icon: 'fas fa-eye' },
        { id: 'memory', name: '记忆训练', icon: 'fas fa-brain' },
        { id: 'reaction', name: '反应训练', icon: 'fas fa-bolt' },
        { id: 'logic', name: '逻辑训练', icon: 'fas fa-puzzle-piece' }
      ],
      
      // 游戏列表 - 基于示例文件设计
      games: [
        {
          id: 1,
          name: '数字字母倒顺序训练',
          description: '按照正确顺序排列数字和字母，提升序列记忆能力',
          icon: 'fas fa-sort-alpha-down',
          category: 'memory',
          difficulty: 'easy',
          duration: 8,
          completed: false,
          playCount: 0,
          bestScore: 0,
          gameType: 'iframe',
          gameUrl: '/game/sequence-training/index.html'
        },
        {
          id: 2,
          name: '倒序复述挑战',
          description: '听到数字或字母后，按倒序复述出来，训练工作记忆',
          icon: 'fas fa-undo',
          category: 'memory',
          difficulty: 'medium',
          duration: 10,
          completed: false,
          playCount: 0,
          bestScore: 0,
          gameType: 'iframe',
          gameUrl: '/game/reverse-recall/index.html'
        },
        {
          id: 3,
          name: '听词复述游戏',
          description: '仔细听取词语，然后准确复述，提升听觉注意力',
          icon: 'fas fa-volume-up',
          category: 'focus',
          difficulty: 'easy',
          duration: 6,
          completed: false,
          playCount: 0,
          bestScore: 0,
          gameType: 'iframe',
          gameUrl: '/game/audio-recall/index.html'
        },
        {
          id: 4,
          name: '词语归类训练',
          description: '将相关词语进行分类整理，训练逻辑思维和分类能力',
          icon: 'fas fa-layer-group',
          category: 'logic',
          difficulty: 'medium',
          duration: 12,
          completed: false,
          playCount: 0,
          bestScore: 0,
          gameType: 'iframe',
          gameUrl: '/game/word-classification/index.html'
        },
        {
          id: 5,
          name: '舒尔特方格5阶',
          description: '经典5×5舒尔特方格训练，提升视觉搜索和专注力',
          icon: 'fas fa-th',
          category: 'focus',
          difficulty: 'medium',
          duration: 5,
          completed: false,
          playCount: 0,
          bestScore: 0,
          gameType: 'iframe',
          gameUrl: '/game/schulte-grid/index.html'
        },
        {
          id: 6,
          name: '专注木鱼',
          description: '通过木鱼声音进行专注力训练，提升持续注意力',
          icon: 'fas fa-yin-yang',
          category: 'focus',
          difficulty: 'easy',
          duration: 15,
          completed: false,
          playCount: 0,
          bestScore: 0,
          gameType: 'iframe',
          gameUrl: '/game/muyu-focus-master/index.html'
        },
        {
          id: 7,
          name: 'VocaBird单词收集',
          description: '控制小鸟收集单词，训练反应速度和注意力',
          icon: 'fas fa-dove',
          category: 'reaction',
          difficulty: 'medium',
          duration: 10,
          completed: false,
          playCount: 0,
          bestScore: 0,
          gameType: 'iframe',
          gameUrl: '/game/vocabird-main/index.html'
        }
      ]
    }
  },
  
  computed: {
    filteredGames() {
      let filtered = this.games;

      // 按分类筛选
      if (this.selectedCategory !== 'all') {
        filtered = filtered.filter(game => game.category === this.selectedCategory);
      }

      // 按搜索关键词筛选
      if (this.searchQuery.trim()) {
        const query = this.searchQuery.toLowerCase();
        filtered = filtered.filter(game =>
          game.name.toLowerCase().includes(query) ||
          game.description.toLowerCase().includes(query)
        );
      }

      return filtered;
    },

    totalPlayCount() {
      return this.games.reduce((total, game) => total + game.playCount, 0);
    },

    completedGamesCount() {
      return this.games.filter(game => game.completed).length;
    }
  },
  
  mounted() {
    // 初始化导航状态
    if (localStorage.getItem('navExpanded') === 'true') {
      this.navExpanded = true;
    }

    // 加载游戏数据
    this.loadGameData();
    this.loadGameHistory();
  },
  
  methods: {
    toggleNav() {
      this.navExpanded = !this.navExpanded;
      localStorage.setItem('navExpanded', this.navExpanded.toString());
    },

    // 导航展开/收起
    expandNav() {
      this.navExpanded = true;
    },

    collapseNav() {
      this.navExpanded = false;
    },
    
    handleSearch(event) {
      this.searchQuery = event.target.value;
    },
    
    showNotifications() {
      console.log('显示通知');
    },

    showMessages() {
      console.log('显示消息');
    },

    showSettings() {
      console.log('显示设置');
    },

    showUserMenu() {
      console.log('显示用户菜单');
    },

    startConsultation() {
      // 跳转到在线咨询页面
      this.$router.push('/online-consultation');
    },

    selectCategory(categoryId) {
      this.selectedCategory = categoryId;
    },
    
    startGame(game) {
      // 记录游戏开始时间
      this.currentGame = game;
      this.gameStartTime = Date.now();

      // 增加游戏次数
      game.playCount++;

      // 根据游戏类型启动不同的游戏
      switch (game.gameType) {
        case 'iframe':
          this.startIframeGame(game);
          break;
        case 'builtin':
          this.startBuiltinGame(game);
          break;
        default:
          this.startBuiltinGame(game);
      }

      this.saveGameData();
    },

    // 启动iframe游戏
    startIframeGame(game) {
      // 获取屏幕尺寸
      const screenWidth = window.screen.width;
      const screenHeight = window.screen.height;

      // 全屏窗口配置
      const gameWindow = window.open(
        game.gameUrl,
        'AttentionGame',
        `width=${screenWidth},height=${screenHeight},left=0,top=0,scrollbars=no,resizable=yes,toolbar=no,menubar=no,location=no,status=no,fullscreen=yes`
      );

      if (gameWindow) {
        // 显示全屏提示
        this.showFullscreenTip(gameWindow);

        // 监听来自游戏窗口的消息
        const messageHandler = (event) => {
          if (event.source === gameWindow) {
            if (event.data.type === 'gameEnd') {
              // 游戏正常结束
              window.removeEventListener('message', messageHandler);
              clearInterval(checkClosed);
              this.endGame(game, event.data.score || Math.floor(Math.random() * 100) + 50);
            } else if (event.data.type === 'closeGame') {
              // 游戏请求关闭
              gameWindow.close();
            }
          }
        };

        window.addEventListener('message', messageHandler);

        // 监听游戏窗口关闭
        const checkClosed = setInterval(() => {
          if (gameWindow.closed) {
            clearInterval(checkClosed);
            window.removeEventListener('message', messageHandler);
            // 如果游戏没有发送结束消息就关闭了，给一个默认分数
            if (this.currentGame === game) {
              this.endGame(game, Math.floor(Math.random() * 100) + 50);
            }
          }
        }, 1000);

        // 设置超时自动结束
        setTimeout(() => {
          if (!gameWindow.closed) {
            gameWindow.close();
            clearInterval(checkClosed);
            window.removeEventListener('message', messageHandler);
            this.endGame(game, Math.floor(Math.random() * 100) + 50);
          }
        }, game.duration * 60 * 1000);
      } else {
        alert('无法打开游戏窗口，请检查浏览器弹窗设置');
        this.currentGame = null;
        this.gameStartTime = null;
        game.playCount--; // 回退游戏次数
      }
    },



    // 启动内置游戏
    startBuiltinGame(game) {
      const confirmed = confirm(`开始游戏: ${game.name}\n\n描述: ${game.description}\n预计时长: ${game.duration}分钟\n\n点击确定开始游戏`);

      if (confirmed) {
        // 模拟游戏结束（实际游戏中这里会是游戏逻辑）
        setTimeout(() => {
          this.endGame(game, Math.floor(Math.random() * 100) + 50);
        }, 2000);
      } else {
        this.currentGame = null;
        this.gameStartTime = null;
        game.playCount--; // 回退游戏次数
      }
    },

    endGame(game, score) {
      if (!this.currentGame || !this.gameStartTime) return;

      const duration = Math.floor((Date.now() - this.gameStartTime) / 1000); // 秒

      // 更新最佳分数
      if (score > game.bestScore) {
        game.bestScore = score;
      }

      // 标记为已完成
      if (score >= 70) {
        game.completed = true;
      }

      // 添加到游戏历史
      const gameRecord = {
        gameId: game.id,
        gameName: game.name,
        score: score,
        duration: duration,
        timestamp: Date.now(),
        date: new Date().toLocaleString()
      };

      this.gameHistory.unshift(gameRecord);

      // 只保留最近20条记录
      if (this.gameHistory.length > 20) {
        this.gameHistory = this.gameHistory.slice(0, 20);
      }

      // 保存数据
      this.saveGameData();
      this.saveGameHistory();

      // 显示结果
      alert(`游戏结束!\n\n得分: ${score}\n用时: ${duration}秒\n最佳分数: ${game.bestScore}`);

      // 重置当前游戏状态
      this.currentGame = null;
      this.gameStartTime = null;
    },
    


    getDifficultyText(difficulty) {
      const difficultyMap = {
        'easy': '简单',
        'medium': '中等',
        'hard': '困难'
      };
      return difficultyMap[difficulty] || '未知';
    },

    // 数据持久化方法
    saveGameData() {
      localStorage.setItem('attentionGamesData', JSON.stringify(this.games));
    },

    loadGameData() {
      const saved = localStorage.getItem('attentionGamesData');
      if (saved) {
        try {
          const savedGames = JSON.parse(saved);
          // 合并保存的数据到当前游戏列表
          savedGames.forEach(savedGame => {
            const game = this.games.find(g => g.id === savedGame.id);
            if (game) {
              game.playCount = savedGame.playCount || 0;
              game.bestScore = savedGame.bestScore || 0;
              game.completed = savedGame.completed || false;
            }
          });
        } catch (e) {
          console.error('加载游戏数据失败:', e);
        }
      }
    },

    saveGameHistory() {
      localStorage.setItem('attentionGamesHistory', JSON.stringify(this.gameHistory));
    },

    loadGameHistory() {
      const saved = localStorage.getItem('attentionGamesHistory');
      if (saved) {
        try {
          this.gameHistory = JSON.parse(saved);
        } catch (e) {
          console.error('加载游戏历史失败:', e);
          this.gameHistory = [];
        }
      }
    },

    clearGameData() {
      if (confirm('确定要清除所有游戏数据吗？此操作不可恢复。')) {
        // 重置游戏数据
        this.games.forEach(game => {
          game.playCount = 0;
          game.bestScore = 0;
          game.completed = false;
        });

        // 清空历史记录
        this.gameHistory = [];

        // 清除本地存储
        localStorage.removeItem('attentionGamesData');
        localStorage.removeItem('attentionGamesHistory');

        alert('游戏数据已清除');
      }
    },

    showGameHistory() {
      if (this.gameHistory.length === 0) {
        alert('暂无游戏记录');
        return;
      }

      const historyText = this.gameHistory.slice(0, 10).map(record =>
        `${record.gameName} - 得分:${record.score} - ${record.date}`
      ).join('\n');

      alert(`最近游戏记录:\n\n${historyText}`);
    },

    // 显示全屏提示
    showFullscreenTip(gameWindow) {
      // 创建一个友好的提示
      const tipMessage = `
🎮 游戏即将开始！

🌟 小贴士：
• 按 F11 键可以全屏游戏
• 全屏模式让游戏更有趣哦！
• 按 ESC 键可以退出全屏

🚀 准备好了吗？让我们开始冒险吧！
      `;

      // 显示提示，3秒后自动关闭
      const confirmed = confirm(tipMessage.trim());

      // 如果用户确认，尝试让游戏窗口全屏
      if (confirmed && gameWindow) {
        setTimeout(() => {
          try {
            // 尝试让游戏窗口获得焦点
            gameWindow.focus();

            // 发送全屏指令到游戏窗口
            gameWindow.postMessage({ type: 'requestFullscreen' }, '*');
          } catch (error) {
            console.log('无法自动全屏，用户可以手动按F11');
          }
        }, 500);
      }
    },


  }
}
</script>

<style scoped>
/* 基础布局样式 */
.attention-games {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 内容包装区 */
.content-wrapper {
  display: flex;
  height: calc(100vh - 60px);
}

/* 主内容区域 */
.main-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 游戏容器 */
.games-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.games-container:hover {
  box-shadow: 0 12px 28px rgba(71, 118, 230, 0.15);
}

/* 区域头部样式 */
.area-header {
  padding: 18px 25px;
  background: linear-gradient(to right, #e8efff, #f8f9fc);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.area-header h3 {
  font-size: 20px;
  color: #3a5bbd;
  display: flex;
  align-items: center;
  margin: 0;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.btn-secondary, .btn-danger {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 6px;
  transition: all 0.3s ease;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
}

.area-header h3 i {
  margin-right: 12px;
  font-size: 1.3rem;
  color: #4e73df;
  font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  display: inline-block;
  min-width: 22px;
  text-align: center;
  line-height: 1;
}

/* 游戏统计信息 */
.game-stats {
  padding: 15px 25px;
  background: #f8f9fc;
  border-bottom: 1px solid #e3e6f0;
  display: flex;
  gap: 30px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4e4e4e;
  font-size: 14px;
}

.stat-item i {
  color: #4e73df;
  width: 16px;
}

/* 搜索和筛选区域 */
.search-filter-section {
  padding: 20px 25px;
  border-bottom: 1px solid #e3e6f0;
}

.search-box {
  position: relative;
  max-width: 400px;
}

.search-box i.fa-search {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6c757d;
}

.search-input {
  width: 100%;
  padding: 10px 40px 10px 40px;
  border: 2px solid #e3e6f0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #4e73df;
}

.clear-search {
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
}

.clear-search:hover {
  background: #f8f9fc;
  color: #dc3545;
}

/* 主内容区域 */
.main-content {
  padding: 25px;
  height: 100%;
}



/* 华丽温和分类按钮样式 */
.game-categories {
  display: flex;
  gap: 12px;
  margin-bottom: 30px;
  justify-content: center;
  flex-wrap: wrap;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
}

.category-btn {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e9ecef;
  color: #495057;
  padding: 14px 24px;
  border-radius: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  position: relative;
  overflow: hidden;
}

.category-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(168, 181, 200, 0.1), transparent);
  transition: left 0.5s ease;
}

.category-btn:hover::before {
  left: 100%;
}

.category-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
  border-color: #a8b5c8;
}

.category-btn.active {
  background: linear-gradient(135deg, #a8b5c8 0%, #7b8fa3 100%);
  color: white;
  border-color: #7b8fa3;
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(123, 143, 163, 0.3);
}

.category-btn i {
  font-size: 1rem;
}

/* 游戏网格样式 */
.games-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(360px, 1fr));
  gap: 24px;
  margin-bottom: 30px;
  max-width: 100%;
}

.game-card {
  background: #fff;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.game-card:hover {
  transform: translateY(-6px);
  box-shadow: 0 12px 32px rgba(71, 118, 230, 0.15);
  border-color: #4e73df;
}

.game-card.completed {
  border-color: #28a745;
}

.game-card.completed:hover {
  border-color: #1e7e34;
  box-shadow: 0 12px 32px rgba(40, 167, 69, 0.15);
}

.game-card.played:not(.completed) {
  border-color: #ffc107;
}

.game-card.played:not(.completed):hover {
  border-color: #e0a800;
  box-shadow: 0 12px 32px rgba(255, 193, 7, 0.15);
}

/* 游戏卡片头部 */
.game-header {
  padding: 20px 20px 0 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.game-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.game-icon i {
  font-size: 28px;
  color: white;
}

.game-status-badge {
  display: flex;
  align-items: center;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.played {
  background: #fff3cd;
  color: #856404;
}

.status-badge.unplayed {
  background: #f8f9fa;
  color: #6c757d;
}

/* 游戏内容 */
.game-content {
  padding: 20px;
  flex: 1;
}

.game-title {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.game-description {
  color: #6c757d;
  font-size: 14px;
  line-height: 1.5;
  margin: 0 0 16px 0;
}

/* 游戏属性网格 */
.game-attributes {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.attribute-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.attribute-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.difficulty-badge, .duration-badge, .count-badge, .score-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  font-weight: 500;
  padding: 6px 10px;
  border-radius: 8px;
  background: #f8f9fa;
  color: #495057;
}

.difficulty-badge.easy {
  background: #d4edda;
  color: #155724;
}

.difficulty-badge.medium {
  background: #fff3cd;
  color: #856404;
}

.difficulty-badge.hard {
  background: #f8d7da;
  color: #721c24;
}

.score-badge.has-score {
  background: #e7f3ff;
  color: #0066cc;
}

.difficulty.easy { color: #27ae60; }
.difficulty.medium { color: #f39c12; }
.difficulty.hard { color: #e74c3c; }

.duration {
  color: #666;
}

.game-status {
  text-align: center;
}

.completed {
  color: #27ae60;
  font-weight: bold;
}

.played {
  color: #f39c12;
  font-weight: bold;
}

.play-btn {
  color: #667eea;
  font-weight: bold;
}

/* 游戏操作区域 */
.game-footer {
  padding: 0 20px 20px 20px;
  margin-top: auto;
}

.play-button {
  width: 100%;
  padding: 12px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.play-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.play-button:active {
  transform: translateY(0);
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-icon i {
  font-size: 32px;
  color: #adb5bd;
}

.empty-state h3 {
  font-size: 20px;
  color: #495057;
  margin: 0 0 8px 0;
}

.empty-state p {
  font-size: 14px;
  margin: 0 0 24px 0;
  line-height: 1.5;
}

.reset-filter-btn {
  padding: 10px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.reset-filter-btn:hover {
  background: #5a6268;
  transform: translateY(-2px);
}



/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    margin-left: 0;
    padding: 20px;
  }
  
  .main-content.expanded {
    margin-left: 0;
  }
  
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.expanded {
    transform: translateX(0);
    width: 250px;
  }
  
  .games-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .game-attributes {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .search-filter-section {
    padding: 15px 20px;
  }

  .game-stats {
    padding: 12px 20px;
    gap: 20px;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .area-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .game-stats {
    flex-direction: column;
    gap: 12px;
  }

  .game-categories {
    gap: 8px;
  }

  .category-btn {
    padding: 8px 12px;
    font-size: 12px;
  }
}

/* 华丽温和样式 */
.area-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 1px solid #dee2e6;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 30px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
}

.area-header::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
  animation: gentle-rotate 30s linear infinite;
}

.area-header h3 {
  color: #495057;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  position: relative;
  z-index: 2;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.area-header h3 i {
  margin-right: 12px;
  color: #6c757d;
}

.header-actions {
  position: relative;
  z-index: 2;
}

.btn-secondary, .btn-danger {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  border: none;
  color: white;
  padding: 12px 20px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(108, 117, 125, 0.2);
  margin-left: 10px;
}

.btn-secondary:hover, .btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
  box-shadow: 0 4px 16px rgba(220, 53, 69, 0.2);
}

.btn-danger:hover {
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
}

/* 华丽统计卡片样式 */
.game-stats {
  display: flex;
  gap: 20px;
  margin-bottom: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.stat-item {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e9ecef;
  border-radius: 16px;
  padding: 20px 24px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 0.95rem;
  color: #495057;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 160px;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #a8b5c8, #7b8fa3, #a8b5c8);
  background-size: 200% 100%;
  animation: gentle-shimmer 3s ease-in-out infinite;
}

.stat-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

.stat-item i {
  color: #7b8fa3;
  font-size: 1.2rem;
  width: 24px;
  text-align: center;
}

.stat-item span {
  font-weight: 500;
}

/* 华丽温和游戏卡片样式 */
.game-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border: 1px solid #e9ecef;
  border-radius: 20px;
  padding: 24px;
  transition: all 0.4s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.game-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #a8b5c8, #7b8fa3, #9bb5d1, #a8b5c8);
  background-size: 300% 100%;
  animation: gentle-flow 4s ease-in-out infinite;
}

.game-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.12);
  border-color: #a8b5c8;
}

.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.game-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #a8b5c8 0%, #7b8fa3 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(123, 143, 163, 0.2);
}

.game-icon i {
  font-size: 1.8rem;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.status-badge {
  padding: 6px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-badge.completed {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.played {
  background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.status-badge.unplayed {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  color: #6c757d;
  border: 1px solid #dee2e6;
}

/* 华丽温和按钮样式 */
.play-button {
  background: linear-gradient(135deg, #a8b5c8 0%, #7b8fa3 100%);
  border: none;
  border-radius: 16px;
  padding: 16px 32px;
  color: white;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 6px 20px rgba(123, 143, 163, 0.2);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.play-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.play-button:hover::before {
  left: 100%;
}

.play-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 28px rgba(123, 143, 163, 0.3);
  background: linear-gradient(135deg, #9bb5d1 0%, #7b8fa3 100%);
}

.play-button i {
  font-size: 1.1rem;
  margin-right: 4px;
}

.game-footer {
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #e9ecef;
}

/* 温和动画定义 */
@keyframes gentle-rotate {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes gentle-shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

@keyframes gentle-flow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes soft-pulse {
  0%, 100% { opacity: 0.8; transform: scale(1); }
  50% { opacity: 1; transform: scale(1.02); }
}

@keyframes gentle-float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-4px); }
}

@keyframes soft-glow {
  0%, 100% { box-shadow: 0 0 10px rgba(168, 181, 200, 0.3); }
  50% { box-shadow: 0 0 20px rgba(168, 181, 200, 0.5); }
}

/* 关卡筛选样式 */
.level-filter {
  margin-bottom: 25px;
  text-align: center;
}

.filter-title {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.2rem;
  font-weight: bold;
}

.level-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.level-btn {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #dee2e6;
  color: #495057;
  padding: 12px 20px;
  border-radius: 25px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
}

.level-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.level-btn.active {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0,0,0,0.2);
}

.level-1-btn.active {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  border-color: #4ecdc4;
  color: white;
}

.level-2-btn.active {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-color: #f093fb;
  color: white;
}

.level-3-btn.active {
  background: linear-gradient(135deg, #ffd700 0%, #ff8c00 100%);
  border-color: #ffd700;
  color: white;
}

.level-btn.active {
  animation: level-glow 2s infinite alternate;
}

@keyframes level-glow {
  0% { box-shadow: 0 6px 20px rgba(0,0,0,0.2); }
  100% { box-shadow: 0 8px 25px rgba(0,0,0,0.3), 0 0 20px rgba(255,255,255,0.2); }
}

/* 关卡系统样式 */
.stars-display {
  text-align: center;
  margin: 15px 0;
  padding: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.star {
  font-size: 1.5rem;
  margin: 0 2px;
}

.stars-text {
  color: white;
  font-weight: bold;
  margin-left: 10px;
  font-size: 0.9rem;
}

.level-badge {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 4px;
}

.level-badge.level-1 {
  background: linear-gradient(45deg, #4ecdc4, #44a08d);
}

.level-badge.level-2 {
  background: linear-gradient(45deg, #f093fb, #f5576c);
}

.level-badge.level-3 {
  background: linear-gradient(45deg, #ffd700, #ff8c00);
}

.unlock-hint {
  text-align: center;
  padding: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-top: 10px;
}

.unlock-hint i {
  margin-right: 5px;
  color: #ffd700;
}

.game-tips {
  text-align: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  color: rgba(255, 255, 255, 0.9);
  font-size: 0.85rem;
  margin-top: 10px;
  border-left: 3px solid #ffd700;
}

.game-tips i {
  margin-right: 5px;
  color: #ffd700;
}

/* 锁定状态的游戏卡片 */
.kids-game-card:not(.unlocked) {
  opacity: 0.6;
  filter: grayscale(50%);
}

.kids-game-card:not(.unlocked):hover {
  transform: none;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

/* 锁定状态的按钮 */
.kids-play-button.locked {
  background: linear-gradient(45deg, #95a5a6, #7f8c8d);
  cursor: not-allowed;
  opacity: 0.7;
}

.kids-play-button.locked:hover {
  transform: none;
  box-shadow: 0 8px 25px rgba(0,0,0,0.2);
}

.kids-play-button:disabled {
  cursor: not-allowed;
}

/* 关卡完成状态 */
.kids-game-card.completed {
  border: 2px solid #ffd700;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.kids-game-card.completed::after {
  content: '✅';
  position: absolute;
  top: 10px;
  right: 10px;
  font-size: 1.5rem;
  z-index: 10;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .stars-display {
    margin: 10px 0;
    padding: 8px;
  }

  .star {
    font-size: 1.2rem;
  }

  .stars-text {
    font-size: 0.8rem;
  }

  .game-tips, .unlock-hint {
    font-size: 0.8rem;
    padding: 6px;
  }
}


</style>
