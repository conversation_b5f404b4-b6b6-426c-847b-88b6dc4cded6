<template>
  <div 
    v-if="monitorState.isMonitoring" 
    class="monitor-indicator"
    :class="{ 'pulse': monitorState.isConnected }"
  >
    <div class="indicator-content">
      <div class="status-icon">
        <i class="fas fa-eye" :class="getStatusClass()"></i>
      </div>
      <div class="status-info">
        <div class="status-text">后台监控运行中</div>
        <div class="status-details">
          <span class="duration">{{ formattedDuration }}</span>
          <span class="separator">•</span>
          <span class="attention-level" :class="getAttentionClass()">
            {{ monitorState.currentEmotion.text }}
          </span>
        </div>
      </div>
      <div class="control-buttons">
        <button 
          @click="toggleDetails" 
          class="detail-btn"
          :title="showDetails ? '收起详情' : '展开详情'"
        >
          <i class="fas" :class="showDetails ? 'fa-chevron-up' : 'fa-chevron-down'"></i>
        </button>
        <button 
          @click="stopMonitoring" 
          class="stop-btn"
          title="停止监控"
        >
          <i class="fas fa-stop"></i>
        </button>
      </div>
    </div>
    
    <!-- 详细信息面板 -->
    <div v-if="showDetails" class="details-panel">
      <div class="detail-row">
        <span class="label">注意力分数:</span>
        <span class="value">{{ monitorState.attentionScore }}%</span>
        <div class="score-bar">
          <div 
            class="score-fill" 
            :style="{ width: monitorState.attentionScore + '%' }"
            :class="getScoreClass()"
          ></div>
        </div>
      </div>
      <div class="detail-row">
        <span class="label">服务器状态:</span>
        <span class="value" :class="getServerStatusClass()">
          {{ getServerStatusText() }}
        </span>
      </div>
      <div class="detail-row">
        <span class="label">最后检测:</span>
        <span class="value">{{ lastDetectionText }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import backgroundMonitorService from '@/services/backgroundMonitorService'

export default {
  name: 'BackgroundMonitorIndicator',
  data() {
    return {
      monitorState: backgroundMonitorService.getState(),
      showDetails: false,
      updateTimer: null
    }
  },
  computed: {
    formattedDuration() {
      return backgroundMonitorService.getFormattedDuration()
    },
    lastDetectionText() {
      if (!this.monitorState.lastDetectionTime) {
        return '暂无'
      }
      const now = new Date()
      const diff = Math.floor((now - this.monitorState.lastDetectionTime) / 1000)
      if (diff < 60) {
        return `${diff}秒前`
      } else if (diff < 3600) {
        return `${Math.floor(diff / 60)}分钟前`
      } else {
        return `${Math.floor(diff / 3600)}小时前`
      }
    }
  },
  mounted() {
    // 设置定时更新
    this.updateTimer = setInterval(() => {
      this.$forceUpdate()
    }, 1000)
    
    // 监听监控停止事件
    backgroundMonitorService.on('monitoringStopped', this.handleMonitoringStopped)
  },
  beforeUnmount() {
    if (this.updateTimer) {
      clearInterval(this.updateTimer)
    }
    backgroundMonitorService.off('monitoringStopped', this.handleMonitoringStopped)
  },
  methods: {
    getStatusClass() {
      if (!this.monitorState.isConnected) {
        return 'status-disconnected'
      }
      switch (this.monitorState.attentionLevel) {
        case 'high':
        case 'focused':
          return 'status-good'
        case 'normal':
          return 'status-normal'
        case 'distracted':
        case 'low':
          return 'status-poor'
        default:
          return 'status-normal'
      }
    },
    
    getAttentionClass() {
      switch (this.monitorState.attentionLevel) {
        case 'high':
        case 'focused':
          return 'attention-good'
        case 'normal':
          return 'attention-normal'
        case 'distracted':
        case 'low':
          return 'attention-poor'
        default:
          return 'attention-normal'
      }
    },
    
    getScoreClass() {
      const score = this.monitorState.attentionScore
      if (score >= 80) return 'score-excellent'
      if (score >= 60) return 'score-good'
      if (score >= 40) return 'score-normal'
      return 'score-poor'
    },
    
    getServerStatusClass() {
      switch (this.monitorState.pythonServerStatus) {
        case 'running':
          return 'server-running'
        case 'starting':
          return 'server-starting'
        case 'error':
          return 'server-error'
        default:
          return 'server-stopped'
      }
    },
    
    getServerStatusText() {
      switch (this.monitorState.pythonServerStatus) {
        case 'running':
          return '运行中'
        case 'starting':
          return '启动中'
        case 'error':
          return '错误'
        case 'stopped':
          return '已停止'
        default:
          return '未知'
      }
    },
    
    toggleDetails() {
      this.showDetails = !this.showDetails
    },
    
    async stopMonitoring() {
      if (confirm('确定要停止后台监控吗？')) {
        await backgroundMonitorService.stopMonitoring()
      }
    },
    
    handleMonitoringStopped() {
      this.showDetails = false
    }
  }
}
</script>

<style scoped>
.monitor-indicator {
  position: fixed;
  top: 80px;
  right: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.2);
  z-index: 1000;
  min-width: 280px;
  max-width: 350px;
}

.monitor-indicator.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); }
  50% { box-shadow: 0 4px 25px rgba(74, 144, 226, 0.3); }
  100% { box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15); }
}

.indicator-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
}

.status-icon {
  font-size: 20px;
}

.status-icon .status-good { color: #10b981; }
.status-icon .status-normal { color: #f59e0b; }
.status-icon .status-poor { color: #ef4444; }
.status-icon .status-disconnected { color: #6b7280; }

.status-info {
  flex: 1;
  min-width: 0;
}

.status-text {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 2px;
}

.status-details {
  font-size: 12px;
  color: #6b7280;
  display: flex;
  align-items: center;
  gap: 6px;
}

.separator {
  color: #d1d5db;
}

.attention-level.attention-good { color: #10b981; }
.attention-level.attention-normal { color: #f59e0b; }
.attention-level.attention-poor { color: #ef4444; }

.control-buttons {
  display: flex;
  gap: 4px;
}

.detail-btn, .stop-btn {
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 6px;
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s;
}

.detail-btn:hover {
  background: rgba(107, 114, 128, 0.2);
  color: #374151;
}

.stop-btn:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.details-panel {
  border-top: 1px solid rgba(229, 231, 235, 0.8);
  padding: 12px 16px;
  background: rgba(249, 250, 251, 0.5);
  border-radius: 0 0 12px 12px;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.label {
  font-size: 12px;
  color: #6b7280;
  min-width: 70px;
}

.value {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.server-running { color: #10b981; }
.server-starting { color: #f59e0b; }
.server-error { color: #ef4444; }
.server-stopped { color: #6b7280; }

.score-bar {
  flex: 1;
  height: 4px;
  background: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
  margin-left: 8px;
}

.score-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 2px;
}

.score-fill.score-excellent { background: #10b981; }
.score-fill.score-good { background: #3b82f6; }
.score-fill.score-normal { background: #f59e0b; }
.score-fill.score-poor { background: #ef4444; }

/* 响应式设计 */
@media (max-width: 768px) {
  .monitor-indicator {
    right: 10px;
    min-width: 260px;
    max-width: calc(100vw - 20px);
  }
  
  .indicator-content {
    padding: 10px 12px;
  }
  
  .details-panel {
    padding: 10px 12px;
  }
}
</style>
