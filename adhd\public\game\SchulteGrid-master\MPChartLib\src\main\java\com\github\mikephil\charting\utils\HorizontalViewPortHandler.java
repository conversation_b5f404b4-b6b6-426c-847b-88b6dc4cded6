
package com.github.mikephil.charting.utils;

/**
 * ViewPortHandler for HorizontalBarChart.
 */
public class HorizontalViewPortHandler extends ViewPortHandler {


//    @Override
//    public void setMinimumScaleX(float xScale) {
//        setMinimumScaleY(xScale);
//    }
//
//    @Override
//    public void setMinimumScaleY(float yScale) {
//        setMinimumScaleX(yScale);
//    }
//
//    @Override
//    public void setMinMaxScaleX(float minScaleX, float maxScaleX) {
//        setMinMaxScaleY(minScaleX, maxScaleX);
//    }
//
//    @Override
//    public void setMinMaxScaleY(float minScaleY, float maxScaleY) {
//        setMinMaxScaleX(minScaleY, maxScaleY);
//    }
}
