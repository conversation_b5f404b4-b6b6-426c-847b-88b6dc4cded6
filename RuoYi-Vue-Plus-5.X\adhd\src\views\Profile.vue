<template>
  <div class="profile-container">
    <div class="profile-header">
      <h1>个人中心</h1>
      <p>管理您的个人信息和头像</p>

      <!-- 数据完整性进度条 -->
      <div class="completion-progress">
        <div class="progress-info">
          <span>资料完整度: {{ dataCompleteness.completionRate }}%</span>
          <span v-if="!dataCompleteness.isComplete" class="missing-count">
            (还需补全 {{ dataCompleteness.missingCount }} 项)
          </span>
        </div>
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: dataCompleteness.completionRate + '%' }"
            :class="{
              'progress-low': dataCompleteness.completionRate < 50,
              'progress-medium': dataCompleteness.completionRate >= 50 && dataCompleteness.completionRate < 80,
              'progress-high': dataCompleteness.completionRate >= 80
            }"
          ></div>
        </div>
      </div>
    </div>

    <div class="profile-content">
      <!-- 左侧栏 -->
      <div class="profile-left-column">
        <!-- 头像区域 -->
        <div class="avatar-section">
          <h2>头像设置</h2>
          <div class="avatar-upload-area">
            <div class="current-avatar" @click="triggerFileInput">
              <img v-if="userInfo.avatar" :src="userInfo.avatar" alt="当前头像" class="avatar-large">
              <div v-else class="avatar-placeholder-large">
                <i class="fas fa-user"></i>
                <span>暂无头像</span>
              </div>
              <div class="avatar-overlay">
                <i class="fas fa-camera"></i>
                <span>点击更换</span>
              </div>
            </div>

            <input
              type="file"
              ref="avatarInput"
              @change="handleAvatarUpload"
              accept="image/*"
              style="display: none;"
            >

            <div class="avatar-actions">
              <button @click="triggerFileInput" class="btn btn-primary">
                <i class="fas fa-upload"></i>
                上传新头像
              </button>
              <button v-if="userInfo.avatar" @click="removeAvatar" class="btn btn-danger">
                <i class="fas fa-trash"></i>
                删除头像
              </button>
            </div>

            <div class="avatar-tips">
              <p><i class="fas fa-info-circle"></i> 支持 JPG、PNG、GIF 格式</p>
              <p><i class="fas fa-info-circle"></i> 文件大小不超过 2MB</p>
              <p><i class="fas fa-info-circle"></i> 建议尺寸：200x200 像素</p>
            </div>
          </div>
        </div>

        <!-- 数据完整性提示 -->
        <div v-if="!checkRequiredFields().isComplete" class="completion-reminder">
          <div class="reminder-header">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>完善个人信息</h3>
          </div>
          <p>为了获得更好的服务体验，请补全以下必填信息：</p>
          <ul class="missing-fields">
            <li v-for="field in getMissingFieldsDisplay()" :key="field.key">
              <i class="fas fa-circle"></i>
              {{ field.label }}
            </li>
          </ul>
          <button @click="startEdit" class="btn btn-primary">
            <i class="fas fa-edit"></i>
            立即完善
          </button>
        </div>

        <!-- 密码修改区域 -->
        <div class="password-section">
          <h2>修改密码</h2>
          <div class="password-form">
            <div class="input-group">
              <label for="currentPassword">当前密码</label>
              <div class="password-input">
                <input
                  type="password"
                  id="currentPassword"
                  v-model="passwordForm.currentPassword"
                  placeholder="请输入当前密码"
                  :class="{ 'error': passwordErrors.currentPassword }"
                >
                <button
                  type="button"
                  class="toggle-password"
                  @click="togglePasswordVisibility('current')"
                >
                  <i :class="showPasswords.current ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                </button>
              </div>
              <span v-if="passwordErrors.currentPassword" class="error-message">{{ passwordErrors.currentPassword }}</span>
            </div>

            <div class="input-group">
              <label for="newPassword">新密码</label>
              <div class="password-input">
                <input
                  type="password"
                  id="newPassword"
                  v-model="passwordForm.newPassword"
                  placeholder="请输入新密码（6-20位）"
                  :class="{ 'error': passwordErrors.newPassword }"
                >
                <button
                  type="button"
                  class="toggle-password"
                  @click="togglePasswordVisibility('new')"
                >
                  <i :class="showPasswords.new ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                </button>
              </div>
              <span v-if="passwordErrors.newPassword" class="error-message">{{ passwordErrors.newPassword }}</span>
            </div>

            <div class="input-group">
              <label for="confirmPassword">确认新密码</label>
              <div class="password-input">
                <input
                  type="password"
                  id="confirmPassword"
                  v-model="passwordForm.confirmPassword"
                  placeholder="请再次输入新密码"
                  :class="{ 'error': passwordErrors.confirmPassword }"
                >
                <button
                  type="button"
                  class="toggle-password"
                  @click="togglePasswordVisibility('confirm')"
                >
                  <i :class="showPasswords.confirm ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                </button>
              </div>
              <span v-if="passwordErrors.confirmPassword" class="error-message">{{ passwordErrors.confirmPassword }}</span>
            </div>

            <div class="password-actions">
              <button @click="changePassword" class="btn btn-primary" :disabled="isChangingPassword">
                <i v-if="isChangingPassword" class="fas fa-spinner fa-spin"></i>
                <i v-else class="fas fa-key"></i>
                {{ isChangingPassword ? '修改中...' : '修改密码' }}
              </button>
              <button @click="resetPasswordForm" class="btn btn-outline">
                <i class="fas fa-undo"></i>
                重置
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧栏 -->
      <div class="profile-right-column">
        <!-- 个人信息区域 -->
        <div class="info-section">
        <div class="section-header">
          <h2>个人信息</h2>
          <div class="header-actions">
            <button @click="refreshUserInfo" class="btn btn-outline" :disabled="isLoading">
              <i :class="isLoading ? 'fas fa-spinner fa-spin' : 'fas fa-sync-alt'"></i>
              {{ isLoading ? '同步中...' : '同步数据' }}
            </button>
            <button @click="toggleEditMode" class="btn btn-outline">
              <i :class="isEditing ? 'fas fa-times' : 'fas fa-edit'"></i>
              {{ isEditing ? '取消编辑' : '编辑信息' }}
            </button>
          </div>
        </div>

        <div v-if="!isEditing" class="info-grid">
          <div class="info-item">
            <label>姓名</label>
            <span>{{ userInfo.patientName || userInfo.username || '未填写' }}</span>
          </div>
          <div class="info-item">
            <label>年龄</label>
            <span>{{ userInfo.age ? userInfo.age + '岁' : '未填写' }}</span>
          </div>
          <div class="info-item">
            <label>性别</label>
            <span>{{ getGenderDisplay(userInfo.gender) }}</span>
          </div>
          <div class="info-item">
            <label>手机号</label>
            <span>{{ userInfo.phoneNumber || userInfo.phone || '未填写' }}</span>
          </div>
          <div class="info-item">
            <label>身份证号</label>
            <span>{{ userInfo.idCard || '未填写' }}</span>
          </div>
          <div class="info-item">
            <label>联系地址</label>
            <span>{{ userInfo.address || '未填写' }}</span>
          </div>
          <div class="info-item">
            <label>紧急联系人</label>
            <span>{{ userInfo.emergencyContact || '未填写' }}</span>
          </div>
          <div class="info-item">
            <label>紧急联系电话</label>
            <span>{{ userInfo.emergencyPhone || '未填写' }}</span>
          </div>
          <div class="info-item">
            <label>血型</label>
            <span>{{ userInfo.bloodType || '未填写' }}</span>
          </div>
          <div class="info-item">
            <label>过敏史</label>
            <span>{{ userInfo.allergies || '无' }}</span>
          </div>
          <div class="info-item">
            <label>病史</label>
            <span>{{ userInfo.medicalHistory || '无' }}</span>
          </div>
          <div class="info-item">
            <label>患者ID</label>
            <span>{{ userInfo.patientId || userInfo.userId }}</span>
          </div>
          <div class="info-item">
            <label>注册时间</label>
            <span>{{ formatDate(userInfo.createTime || userInfo.createdAt) }}</span>
          </div>
        </div>

        <!-- 编辑表单 -->
        <div v-else class="edit-form">
          <div class="form-grid">
            <div class="form-item">
              <label for="patientName">姓名 *</label>
              <input
                type="text"
                id="patientName"
                v-model="editForm.patientName"
                placeholder="请输入真实姓名"
                :class="{ 'error': editErrors.patientName }"
              >
              <span v-if="editErrors.patientName" class="error-message">{{ editErrors.patientName }}</span>
            </div>

            <div class="form-item">
              <label for="age">年龄 *</label>
              <input
                type="number"
                id="age"
                v-model="editForm.age"
                placeholder="请输入年龄"
                min="1"
                max="120"
                :class="{ 'error': editErrors.age }"
              >
              <span v-if="editErrors.age" class="error-message">{{ editErrors.age }}</span>
            </div>

            <div class="form-item">
              <label for="gender">性别 *</label>
              <select id="gender" v-model="editForm.gender" :class="{ 'error': editErrors.gender }">
                <option value="">请选择性别</option>
                <option value="0">男</option>
                <option value="1">女</option>
              </select>
              <span v-if="editErrors.gender" class="error-message">{{ editErrors.gender }}</span>
            </div>

            <div class="form-item">
              <label for="phoneNumber">手机号</label>
              <input
                type="tel"
                id="phoneNumber"
                v-model="editForm.phoneNumber"
                placeholder="请输入手机号"
              >
            </div>

            <div class="form-item">
              <label for="idCard">身份证号</label>
              <input
                type="text"
                id="idCard"
                v-model="editForm.idCard"
                placeholder="请输入身份证号"
              >
            </div>

            <div class="form-item full-width">
              <label for="address">联系地址</label>
              <input
                type="text"
                id="address"
                v-model="editForm.address"
                placeholder="请输入联系地址"
              >
            </div>

            <div class="form-item">
              <label for="emergencyContact">紧急联系人</label>
              <input
                type="text"
                id="emergencyContact"
                v-model="editForm.emergencyContact"
                placeholder="请输入紧急联系人姓名"
              >
            </div>

            <div class="form-item">
              <label for="emergencyPhone">紧急联系电话</label>
              <input
                type="tel"
                id="emergencyPhone"
                v-model="editForm.emergencyPhone"
                placeholder="请输入紧急联系电话"
              >
            </div>

            <div class="form-item">
              <label for="bloodType">血型</label>
              <select id="bloodType" v-model="editForm.bloodType">
                <option value="">请选择血型</option>
                <option value="A">A型</option>
                <option value="B">B型</option>
                <option value="AB">AB型</option>
                <option value="O">O型</option>
              </select>
            </div>

            <div class="form-item full-width">
              <label for="allergies">过敏史</label>
              <textarea
                id="allergies"
                v-model="editForm.allergies"
                placeholder="请输入过敏史，如无请填写'无'"
                rows="3"
              ></textarea>
            </div>

            <div class="form-item full-width">
              <label for="medicalHistory">病史</label>
              <textarea
                id="medicalHistory"
                v-model="editForm.medicalHistory"
                placeholder="请输入病史，如无请填写'无'"
                rows="3"
              ></textarea>
            </div>
          </div>

          <div class="form-actions">
            <button @click="saveUserInfo" class="btn btn-primary" :disabled="isSaving">
              <i v-if="isSaving" class="fas fa-spinner fa-spin"></i>
              <i v-else class="fas fa-save"></i>
              {{ isSaving ? '保存中...' : '保存信息' }}
            </button>
            <button @click="cancelEdit" class="btn btn-outline">
              <i class="fas fa-times"></i>
              取消
            </button>
          </div>
        </div>
        </div> <!-- 关闭右侧栏 -->
      </div> <!-- 关闭profile-content -->

      <!-- 操作按钮 -->
      <div class="action-section">
        <button @click="saveProfile" class="btn btn-success" :disabled="isLoading">
          <i v-if="isLoading" class="fas fa-spinner fa-spin"></i>
          <i v-else class="fas fa-save"></i>
          {{ isLoading ? '保存中...' : '保存更改' }}
        </button>
        <button @click="goBack" class="btn btn-secondary">
          <i class="fas fa-arrow-left"></i>
          返回
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { localStorageUtils, updateUserPassword } from '@/api/ruoyiAPI.js'
import { patientAPI, patientDataFormatter } from '@/api/patientAPI.js'

export default {
  name: 'ProfilePage',
  data() {
    return {
      userInfo: {
        patientId: '',
        userId: '',
        patientName: '',
        username: '',
        age: '',
        gender: '',
        phoneNumber: '',
        phone: '',
        idCard: '',
        address: '',
        emergencyContact: '',
        emergencyPhone: '',
        bloodType: '',
        allergies: '',
        medicalHistory: '',
        avatar: '',
        createTime: '',
        createdAt: ''
      },
      isEditing: false,
      isSaving: false,
      editForm: {},
      editErrors: {},
      isLoading: false,
      isChangingPassword: false,

      // 密码修改表单
      passwordForm: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      },

      // 密码显示控制
      showPasswords: {
        current: false,
        new: false,
        confirm: false
      },

      // 密码验证错误
      passwordErrors: {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
    }
  },
  
  mounted() {
    this.loadUserInfo()
  },

  computed: {
    // 计算数据完整性
    dataCompleteness() {
      const checkResult = this.checkRequiredFields();
      return {
        isComplete: checkResult.isComplete,
        completionRate: Math.round(((8 - checkResult.missingFields.length) / 8) * 100),
        missingCount: checkResult.missingFields.length
      };
    }
  },
  
  methods: {
    // 加载用户信息
    async loadUserInfo() {
      try {
        console.log('🔍 开始加载用户信息...');

        // 使用统一的用户信息获取方法
        const user = localStorageUtils.getUser();
        if (user) {
          this.userInfo = {
            userId: user.userId || '',
            username: user.username || '未知用户',
            age: user.age || 0,
            gender: user.gender || '',
            phone: user.phone || '',
            email: user.email || '',
            avatar: user.avatar || '',
            createdAt: user.createdAt || '',
            ...user
          };

          console.log('✅ 用户信息加载成功:', this.userInfo);

          // 自动填充默认值
          this.autoFillDefaults();

          // 尝试从服务器获取最新信息
          try {
            const serverResult = await this.fetchUserInfoFromServer();
            if (!serverResult) {
              // 如果服务器没有完整信息，检查数据完整性
              const checkResult = this.checkRequiredFields();
              if (!checkResult.isComplete) {
                console.log('⚠️ 发现缺失字段:', checkResult.missingFields);
                this.showInfoMessage(`发现 ${checkResult.missingFields.length} 个字段需要补全，请完善个人信息`);
              }
            }
          } catch (error) {
            console.log('⚠️ 服务器信息获取失败，使用本地信息:', error.message);
          }
        } else {
          console.log('❌ 未找到用户信息，跳转到登录页');
          this.$router.push('/login');
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
        this.$router.push('/login')
      }
    },

    // 从服务器获取完整的用户信息
    async fetchUserInfoFromServer() {
      this.isLoading = true;
      try {
        console.log('🔍 尝试从RuoYi后端获取患者信息...')
        console.log('用户ID:', this.userInfo.userId)

        // 使用新的API服务
        const result = await patientAPI.getPatientByUserId(this.userInfo.userId);

        if (result.success && result.data) {
          const patientData = result.data;

          // 合并用户信息，优先使用服务器数据
          this.userInfo = {
            ...this.userInfo,
            ...patientData,
            // 确保关键字段不被覆盖
            userId: this.userInfo.userId,
            username: this.userInfo.username || patientData.patientName,
            // 格式化性别显示
            gender: this.formatGenderFromServer(patientData.gender),
            // 确保数据类型正确
            age: patientData.age ? Number(patientData.age) : this.userInfo.age,
            patientId: patientData.patientId,
            // 保持原有登录状态
            isLoggedIn: true
          }

          // 更新localStorage
          localStorage.setItem('currentUser', JSON.stringify(this.userInfo))
          console.log('✅ 患者信息已更新:', this.userInfo)

          // 显示成功提示
          this.showSuccessMessage('个人信息已从服务器同步');
          return true;
        } else {
          console.log('⚠️ 未找到患者信息，可能是新用户')
          this.showInfoMessage('未找到完整的个人信息，请补全资料');
          return false;
        }
      } catch (error) {
        console.error('❌ 从服务器获取患者信息失败:', error)
        this.showErrorMessage('获取个人信息失败，请检查网络连接');
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    // 格式化服务器返回的性别数据
    formatGenderFromServer(gender) {
      if (gender === '0' || gender === 0) return 'male';
      if (gender === '1' || gender === 1) return 'female';
      return 'other';
    },

    // 触发文件选择
    triggerFileInput() {
      this.$refs.avatarInput.click()
    },

    // 处理头像上传
    handleAvatarUpload(event) {
      const file = event.target.files[0]
      if (!file) return

      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      if (!allowedTypes.includes(file.type)) {
        alert('请选择 JPG、PNG 或 GIF 格式的图片')
        return
      }

      // 验证文件大小 (2MB)
      const maxSize = 2 * 1024 * 1024
      if (file.size > maxSize) {
        alert('图片大小不能超过 2MB')
        return
      }

      // 读取文件并转换为Base64
      const reader = new FileReader()
      reader.onload = (e) => {
        this.userInfo.avatar = e.target.result
        console.log('头像上传成功')
      }
      reader.onerror = () => {
        console.error('头像读取失败')
        alert('头像读取失败，请重试')
      }
      reader.readAsDataURL(file)
    },

    // 删除头像
    removeAvatar() {
      if (confirm('确定要删除头像吗？')) {
        this.userInfo.avatar = ''
        // 清空文件输入框
        if (this.$refs.avatarInput) {
          this.$refs.avatarInput.value = ''
        }
        console.log('头像已删除')
      }
    },

    // 切换编辑模式
    toggleEditMode() {
      if (this.isEditing) {
        this.cancelEdit()
      } else {
        this.startEdit()
      }
    },

    // 开始编辑
    startEdit() {
      this.isEditing = true
      this.editForm = { ...this.userInfo }
      this.editErrors = {}
    },

    // 取消编辑
    cancelEdit() {
      this.isEditing = false
      this.editForm = {}
      this.editErrors = {}
    },

    // 验证表单
    validateForm() {
      this.editErrors = {}
      let isValid = true

      if (!this.editForm.patientName || this.editForm.patientName.trim() === '') {
        this.editErrors.patientName = '请输入姓名'
        isValid = false
      }

      if (!this.editForm.age || this.editForm.age < 1 || this.editForm.age > 120) {
        this.editErrors.age = '请输入有效的年龄（1-120岁）'
        isValid = false
      }

      if (!this.editForm.gender) {
        this.editErrors.gender = '请选择性别'
        isValid = false
      }

      // 为了满足后端验证，设置默认值
      if (!this.editForm.idCard) {
        this.editForm.idCard = '000000000000000000' // 默认身份证号
      }

      if (!this.editForm.phoneNumber) {
        this.editForm.phoneNumber = '00000000000' // 默认手机号
      }

      if (!this.editForm.address) {
        this.editForm.address = '未填写'
      }

      if (!this.editForm.emergencyContact) {
        this.editForm.emergencyContact = '未填写'
      }

      if (!this.editForm.emergencyPhone) {
        this.editForm.emergencyPhone = '00000000000'
      }

      if (!this.editForm.bloodType) {
        this.editForm.bloodType = 'O'
      }

      return isValid
    },

    // 保存用户信息
    async saveUserInfo() {
      if (!this.validateForm()) {
        return
      }

      this.isSaving = true
      try {
        console.log('💾 开始保存患者信息...')

        // 使用数据格式化工具准备数据
        const rawData = {
          ...this.userInfo,
          ...this.editForm
        };

        const saveData = patientDataFormatter.formatForSubmit(rawData);
        console.log('📤 格式化后的保存数据:', saveData)

        let result;

        if (this.userInfo.patientId) {
          // 更新现有患者信息
          console.log('🔄 更新现有患者信息')
          result = await patientAPI.updatePatient(saveData);
        } else {
          // 创建新的患者信息
          console.log('🔄 创建新患者信息')
          result = await patientAPI.registerPatient(saveData);
        }

        if (result.success) {
          // 更新本地用户信息
          this.userInfo = {
            ...this.userInfo,
            ...this.editForm,
            // 如果是新创建的患者，保存返回的patientId
            patientId: result.data || this.userInfo.patientId
          }

          // 更新localStorage
          localStorage.setItem('currentUser', JSON.stringify(this.userInfo))

          this.isEditing = false
          this.editForm = {}

          this.showSuccessMessage('个人信息保存成功！')
          console.log('✅ 患者信息保存成功')

          // 重新从服务器获取最新信息
          await this.fetchUserInfoFromServer()
        } else {
          throw new Error(result.message || '保存失败')
        }
      } catch (error) {
        console.error('❌ 保存患者信息失败:', error)
        this.showErrorMessage(`保存失败: ${error.message}`)
      } finally {
        this.isSaving = false
      }
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 获取性别显示文本
    getGenderDisplay(gender) {
      return patientDataFormatter.formatGenderDisplay(gender) || '未知'
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString || dateString === '${comment}') {
        return '未知'
      }

      try {
        // 处理多种日期格式
        let date

        // 如果是时间戳
        if (typeof dateString === 'number') {
          date = new Date(dateString)
        }
        // 如果是ISO字符串或其他格式
        else if (typeof dateString === 'string') {
          // 替换可能的占位符
          if (dateString.includes('${') || dateString === 'null' || dateString === 'undefined') {
            return '未知'
          }
          date = new Date(dateString)
        }
        // 如果已经是Date对象
        else if (dateString instanceof Date) {
          date = dateString
        }
        else {
          return '未知'
        }

        // 检查日期是否有效
        if (isNaN(date.getTime())) {
          console.warn('无效的日期格式:', dateString)
          return '未知'
        }

        // 格式化为中文日期
        return date.toLocaleDateString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        console.error('日期格式化错误:', error, '原始数据:', dateString)
        return '未知'
      }
    },

    // 切换密码显示/隐藏
    togglePasswordVisibility(type) {
      this.showPasswords[type] = !this.showPasswords[type]

      // 切换input类型
      const inputId = type === 'current' ? 'currentPassword' :
                     type === 'new' ? 'newPassword' : 'confirmPassword'
      const input = document.getElementById(inputId)
      if (input) {
        input.type = this.showPasswords[type] ? 'text' : 'password'
      }
    },

    // 验证密码表单
    validatePasswordForm() {
      this.passwordErrors = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }

      let isValid = true

      // 验证当前密码
      if (!this.passwordForm.currentPassword) {
        this.passwordErrors.currentPassword = '请输入当前密码'
        isValid = false
      }

      // 验证新密码
      if (!this.passwordForm.newPassword) {
        this.passwordErrors.newPassword = '请输入新密码'
        isValid = false
      } else if (this.passwordForm.newPassword.length < 6 || this.passwordForm.newPassword.length > 20) {
        this.passwordErrors.newPassword = '密码长度应为6-20位'
        isValid = false
      }

      // 验证确认密码
      if (!this.passwordForm.confirmPassword) {
        this.passwordErrors.confirmPassword = '请确认新密码'
        isValid = false
      } else if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
        this.passwordErrors.confirmPassword = '两次输入的密码不一致'
        isValid = false
      }

      // 检查新密码是否与当前密码相同
      if (this.passwordForm.currentPassword === this.passwordForm.newPassword) {
        this.passwordErrors.newPassword = '新密码不能与当前密码相同'
        isValid = false
      }

      return isValid
    },

    // 修改密码
    async changePassword() {
      if (!this.validatePasswordForm()) {
        return
      }

      this.isChangingPassword = true
      try {
        console.log('🔐 开始修改密码...');

        // 调用API修改密码
        const result = await updateUserPassword(this.userInfo.userId, {
          currentPassword: this.passwordForm.currentPassword,
          newPassword: this.passwordForm.newPassword
        });

        if (result.success) {
          console.log('✅ 密码修改成功');
          alert('密码修改成功！');
          this.resetPasswordForm();

          // 更新本地存储的用户信息
          const updatedUser = { ...this.userInfo, password: this.passwordForm.newPassword };
          localStorageUtils.saveUser(updatedUser, true);
        } else {
          console.log('❌ 密码修改失败:', result.message);
          alert(result.message || '密码修改失败，请检查当前密码是否正确');
        }
      } catch (error) {
        console.error('❌ 密码修改异常:', error);
        alert('密码修改失败，请稍后重试');
      } finally {
        this.isChangingPassword = false;
      }
    },

    // 重置密码表单
    resetPasswordForm() {
      this.passwordForm = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.passwordErrors = {
        currentPassword: '',
        newPassword: '',
        confirmPassword: ''
      }
      this.showPasswords = {
        current: false,
        new: false,
        confirm: false
      }

      // 重置input类型为password
      const inputs = ['currentPassword', 'newPassword', 'confirmPassword']
      inputs.forEach(id => {
        const input = document.getElementById(id)
        if (input) {
          input.type = 'password'
        }
      })
    },

    // 消息提示方法
    showSuccessMessage(message) {
      // 可以使用更好的UI组件库，这里先用alert
      console.log('✅ 成功:', message);
      alert(message);
    },

    showErrorMessage(message) {
      console.error('❌ 错误:', message);
      alert(message);
    },

    showInfoMessage(message) {
      console.log('ℹ️ 信息:', message);
      alert(message);
    },

    // 刷新用户信息
    async refreshUserInfo() {
      console.log('🔄 刷新用户信息...');
      await this.fetchUserInfoFromServer();
    },

    // 检查必填字段是否完整
    checkRequiredFields() {
      const requiredFields = ['patientName', 'age', 'gender', 'phoneNumber'];
      const recommendedFields = ['idCard', 'address', 'emergencyContact', 'emergencyPhone'];
      const missingFields = [];

      // 检查必填字段
      requiredFields.forEach(field => {
        const value = this.userInfo[field];
        if (!value || value === '' || value === '未填写' || value === 'other' || value === '2') {
          missingFields.push(field);
        }
      });

      // 检查推荐字段
      recommendedFields.forEach(field => {
        const value = this.userInfo[field];
        if (!value || value === '' || value === '未填写' || value === '00000000000' || value === '000000000000000000') {
          missingFields.push(field);
        }
      });

      return {
        isComplete: missingFields.length === 0,
        missingFields,
        requiredMissing: missingFields.filter(field => requiredFields.includes(field)),
        recommendedMissing: missingFields.filter(field => recommendedFields.includes(field))
      };
    },

    // 自动填充默认值
    autoFillDefaults() {
      if (!this.userInfo.patientName && this.userInfo.username) {
        this.userInfo.patientName = this.userInfo.username;
      }

      // 其他默认值填充逻辑
      if (!this.userInfo.allergies) {
        this.userInfo.allergies = '无';
      }

      if (!this.userInfo.medicalHistory) {
        this.userInfo.medicalHistory = '无';
      }
    },

    // 获取缺失字段的显示信息
    getMissingFieldsDisplay() {
      const fieldLabels = {
        patientName: '真实姓名',
        age: '年龄',
        gender: '性别',
        phoneNumber: '手机号码',
        idCard: '身份证号',
        address: '联系地址',
        emergencyContact: '紧急联系人',
        emergencyPhone: '紧急联系电话'
      };

      const checkResult = this.checkRequiredFields();
      return checkResult.missingFields.map(field => ({
        key: field,
        label: fieldLabels[field] || field
      }));
    }
  }
}
</script>

<style scoped>
.profile-container {
  max-width: 1400px; /* 增加最大宽度，适合电脑和平板 */
  margin: 0 auto;
  padding: 30px 40px; /* 增加内边距 */
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  min-height: 100vh;
  font-family: 'Microsoft YaHei', '微软雅黑', sans-serif;
  color: #e8eaed;
}

.profile-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 30px 0;
  background: linear-gradient(135deg, #16213e 0%, #1e3c72 100%);
  color: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.3);
}

.profile-header h1 {
  margin: 0 0 10px 0;
  font-size: 32px;
  font-weight: 600;
}

.profile-header p {
  margin: 0 0 20px 0;
  opacity: 0.9;
  font-size: 16px;
}

/* 数据完整性进度条 */
.completion-progress {
  margin-top: 20px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
}

.missing-count {
  color: #ffeaa7;
  font-weight: 500;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 4px;
}

.progress-low {
  background: linear-gradient(90deg, #e74c3c, #c0392b);
}

.progress-medium {
  background: linear-gradient(90deg, #f39c12, #e67e22);
}

.progress-high {
  background: linear-gradient(90deg, #27ae60, #2ecc71);
}

.profile-content {
  display: grid;
  grid-template-columns: 1fr 2fr; /* 左侧1份，右侧2份 */
  gap: 40px;
  align-items: start;
}

/* 左侧栏 - 头像和快捷操作 */
.profile-left-column {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* 右侧栏 - 主要信息 */
.profile-right-column {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

/* 头像区域 */
.avatar-section {
  background: rgba(255, 255, 255, 0.95);
  padding: 35px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  min-height: 450px; /* 增加最小高度 */
}

.avatar-section h2 {
  margin: 0 0 25px 0;
  color: #333;
  font-size: 24px;
}

.avatar-upload-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.current-avatar {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  cursor: pointer;
  border: 4px solid #e9ecef;
  transition: all 0.3s ease;
}

.current-avatar:hover {
  border-color: #007bff;
  transform: scale(1.05);
}

.avatar-large {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder-large {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  color: #6c757d;
}

.avatar-placeholder-large i {
  font-size: 40px;
  margin-bottom: 8px;
}

.avatar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.current-avatar:hover .avatar-overlay {
  opacity: 1;
}

.avatar-overlay i {
  font-size: 24px;
  margin-bottom: 5px;
}

.avatar-actions {
  display: flex;
  gap: 15px;
}

.avatar-tips {
  text-align: center;
  color: #6c757d;
  font-size: 14px;
}

.avatar-tips p {
  margin: 5px 0;
}

/* 数据完整性提示 */
.completion-reminder {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 2px solid #ffc107;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.2);
}

.reminder-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}

.reminder-header i {
  color: #f39c12;
  font-size: 20px;
}

.reminder-header h3 {
  margin: 0;
  color: #d68910;
  font-size: 18px;
}

.completion-reminder p {
  margin: 10px 0;
  color: #856404;
  font-size: 14px;
}

.missing-fields {
  list-style: none;
  padding: 0;
  margin: 15px 0;
}

.missing-fields li {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 5px 0;
  color: #856404;
  font-size: 14px;
}

.missing-fields i {
  color: #f39c12;
  font-size: 8px;
}

/* 个人信息区域 */
.info-section {
  background: rgba(255, 255, 255, 0.95);
  padding: 35px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 30px;
  min-height: 600px; /* 增加最小高度 */
  color: #333;
}

.password-section {
  background: rgba(255, 255, 255, 0.95);
  padding: 35px;
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 30px;
  min-height: 400px; /* 增加最小高度 */
  color: #333;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
}

.header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.info-section h2,
.password-section h2 {
  margin: 0;
  color: #333;
  font-size: 24px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 固定3列布局，更适合宽屏 */
  gap: 25px;
  margin-top: 20px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.info-item span {
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  font-size: 16px;
}

/* 编辑表单样式 */
.edit-form {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr); /* 改为3列布局，更好利用宽屏空间 */
  gap: 25px;
  margin-bottom: 25px;
}

.form-item {
  display: flex;
  flex-direction: column;
}

.form-item.full-width {
  grid-column: 1 / -1;
}

.form-item label {
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
  font-size: 14px;
}

.form-item input,
.form-item select,
.form-item textarea {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-item input:focus,
.form-item select:focus,
.form-item textarea:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-item input.error,
.form-item select.error {
  border-color: #dc3545;
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 5px;
}

.form-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: linear-gradient(135deg, #2a5298, #1e3c72);
  color: white;
  border: none;
  box-shadow: 0 4px 15px rgba(42, 82, 152, 0.3);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, #1e3c72, #16213e);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(42, 82, 152, 0.4);
}

.btn-outline {
  background: transparent;
  color: #2a5298;
  border: 2px solid #2a5298;
  box-shadow: 0 4px 15px rgba(42, 82, 152, 0.2);
}

.btn-outline:hover {
  background: linear-gradient(135deg, #2a5298, #1e3c72);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(42, 82, 152, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .form-actions {
    flex-direction: column;
  }
}

/* 密码修改区域 */
.password-form {
  max-width: 400px;
}

.password-form .input-group {
  margin-bottom: 20px;
}

.password-form label {
  display: block;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
  margin-bottom: 8px;
}

.password-input {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input input {
  flex: 1;
  padding: 12px 45px 12px 12px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.password-input input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.password-input input.error {
  border-color: #dc3545;
}

.toggle-password {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #6c757d;
  cursor: pointer;
  padding: 5px;
  transition: color 0.3s ease;
}

.toggle-password:hover {
  color: #007bff;
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.password-actions {
  display: flex;
  gap: 15px;
  margin-top: 25px;
}

.btn-outline {
  background: transparent;
  border: 1px solid #6c757d;
  color: #6c757d;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn-outline:hover {
  background: #6c757d;
  color: white;
  transform: translateY(-2px);
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  gap: 15px;
  justify-content: center;
  padding: 20px 0;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-2px);
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #1e7e34;
  transform: translateY(-2px);
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover {
  background: #c82333;
  transform: translateY(-2px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #545b62;
  transform: translateY(-2px);
}

/* 响应式设计 */

/* 大屏幕优化 (1400px+) */
@media (min-width: 1400px) {
  .profile-container {
    max-width: 1600px;
    padding: 40px 50px;
  }

  .profile-content {
    gap: 50px;
  }

  .info-grid {
    grid-template-columns: repeat(4, 1fr); /* 大屏幕显示4列 */
  }

  .form-grid {
    grid-template-columns: repeat(4, 1fr); /* 大屏幕显示4列 */
  }
}

/* 平板端优化 (768px - 1200px) */
@media (min-width: 768px) and (max-width: 1200px) {
  .profile-container {
    max-width: 1200px;
    padding: 25px 30px;
  }

  .profile-content {
    grid-template-columns: 1fr 1.5fr; /* 调整比例 */
    gap: 35px;
  }

  .info-grid {
    grid-template-columns: repeat(2, 1fr); /* 平板显示2列 */
  }

  .form-grid {
    grid-template-columns: repeat(2, 1fr); /* 平板显示2列 */
  }
}

/* 手机端 */
@media (max-width: 768px) {
  .profile-container {
    padding: 15px;
    max-width: 100%;
  }

  .profile-content {
    grid-template-columns: 1fr; /* 单列布局 */
    gap: 25px;
  }

  .avatar-section,
  .info-section,
  .password-section {
    padding: 20px;
    min-height: auto; /* 移除最小高度限制 */
  }

  .info-grid {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .action-section {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 300px;
  }
}
</style>
