<template>
  <span>{{ displayValue }}</span>
</template>

<script setup>
/* eslint-disable no-undef */
import { ref, watch, onMounted } from 'vue'

const props = defineProps({
  value: {
    type: Number,
    required: true
  },
  duration: {
    type: Number,
    default: 1000
  },
  format: {
    type: Function,
    default: (val) => val.toString()
  }
})

const displayValue = ref('0')

const animateNumber = (start, end, duration) => {
  const startTime = Date.now()
  const difference = end - start
  
  const updateValue = () => {
    const elapsed = Date.now() - startTime
    const progress = Math.min(elapsed / duration, 1)
    
    // 使用缓动函数
    const easeProgress = 1 - Math.pow(1 - progress, 3)
    const currentValue = start + (difference * easeProgress)
    
    displayValue.value = props.format(Math.floor(currentValue))
    
    if (progress < 1) {
      requestAnimationFrame(updateValue)
    } else {
      displayValue.value = props.format(end)
    }
  }
  
  requestAnimationFrame(updateValue)
}

watch(() => props.value, (newValue, oldValue) => {
  const startValue = oldValue || 0
  animateNumber(startValue, newValue, props.duration)
}, { immediate: true })

onMounted(() => {
  animateNumber(0, props.value, props.duration)
})
</script>
