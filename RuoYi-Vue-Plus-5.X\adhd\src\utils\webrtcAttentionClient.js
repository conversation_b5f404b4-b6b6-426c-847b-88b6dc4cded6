/**
 * WebRTC注意力分析客户端
 * 连接Python注意力分析服务器，实现实时注意力检测
 */

class WebRTCAttentionClient {
  constructor() {
    this.pc = null;
    this.localStream = null;
    this.remoteStream = null;
    this.ws = null;
    this.serverUrl = 'http://localhost:8765';
    this.wsUrl = 'ws://localhost:8765/ws';
    this.isConnected = false;
    this.onResultCallback = null;
    this.onStatusCallback = null;
    this.onLandmarksCallback = null;
    
    console.log('🚀 WebRTC注意力分析客户端初始化');
  }

  /**
   * 设置结果回调函数
   * @param {Function} callback - 接收分析结果的回调函数
   */
  setResultCallback(callback) {
    this.onResultCallback = callback;
  }

  /**
   * 设置状态回调函数
   * @param {Function} callback - 接收连接状态的回调函数
   */
  setStatusCallback(callback) {
    this.onStatusCallback = callback;
  }

  /**
   * 设置远程流回调函数
   * @param {Function} callback - 接收远程视频流的回调函数
   */
  setRemoteStreamCallback(callback) {
    this.onRemoteStreamCallback = callback;
  }

  /**
   * 设置特征点回调函数
   * @param {Function} callback - 接收468个特征点数据的回调函数
   */
  setLandmarksCallback(callback) {
    this.onLandmarksCallback = callback;
  }

  /**
   * 启动WebRTC连接和注意力分析
   */
  async startAnalysis() {
    try {
      this.updateStatus('正在启动注意力分析...', 'info');
      
      // 1. 获取用户媒体
      await this.getUserMedia();
      
      // 2. 建立WebRTC连接
      await this.setupWebRTC();
      
      // 3. 建立WebSocket连接接收分析结果
      await this.setupWebSocket();
      
      this.isConnected = true;
      this.updateStatus('注意力分析已启动', 'success');
      
    } catch (error) {
      console.error('启动注意力分析失败:', error);
      this.updateStatus(`启动失败: ${error.message}`, 'error');
      throw error;
    }
  }

  /**
   * 停止注意力分析
   */
  async stopAnalysis() {
    try {
      this.updateStatus('正在停止注意力分析...', 'info');
      
      // 关闭WebSocket
      if (this.ws) {
        this.ws.close();
        this.ws = null;
      }
      
      // 关闭WebRTC连接
      if (this.pc) {
        this.pc.close();
        this.pc = null;
      }
      
      // 停止本地媒体流
      if (this.localStream) {
        this.localStream.getTracks().forEach(track => track.stop());
        this.localStream = null;
      }
      
      this.isConnected = false;
      this.updateStatus('注意力分析已停止', 'info');
      
    } catch (error) {
      console.error('停止注意力分析失败:', error);
      this.updateStatus(`停止失败: ${error.message}`, 'error');
    }
  }

  /**
   * 获取用户媒体（摄像头）
   */
  async getUserMedia() {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1920, min: 1280 },
          height: { ideal: 1080, min: 720 },
          frameRate: { ideal: 30, min: 20 },
          facingMode: 'user',  // 前置摄像头
          aspectRatio: { ideal: 16/9 }
        },
        audio: false
      });
      
      console.log('✅ 摄像头访问成功');
      
    } catch (error) {
      console.error('❌ 摄像头访问失败:', error);
      throw new Error('无法访问摄像头，请检查权限设置');
    }
  }

  /**
   * 设置WebRTC连接
   */
  async setupWebRTC() {
    try {
      // 创建RTCPeerConnection，配置高质量传输
      this.pc = new RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' }
        ],
        // 配置高质量传输
        bundlePolicy: 'max-bundle',
        rtcpMuxPolicy: 'require'
      });

      // 监听连接状态变化
      this.pc.onconnectionstatechange = () => {
        console.log('WebRTC连接状态:', this.pc.connectionState);
        this.updateStatus(`WebRTC状态: ${this.pc.connectionState}`, 'info');
      };

      // 监听远程流
      this.pc.ontrack = (event) => {
        console.log('接收到远程视频流（处理后的画面）');
        this.remoteStream = event.streams[0];

        // 触发远程流回调
        if (this.onRemoteStreamCallback) {
          this.onRemoteStreamCallback(this.remoteStream);
        }

        // 自动设置远程视频流到视频元素
        this.setRemoteStreamToVideo();
      };

      // 添加本地流，配置高质量传输
      this.localStream.getTracks().forEach(track => {
        const sender = this.pc.addTrack(track, this.localStream);

        // 如果是视频轨道，设置高质量编码参数
        if (track.kind === 'video') {
          setTimeout(async () => {
            try {
              const params = sender.getParameters();
              if (params.encodings && params.encodings.length > 0) {
                params.encodings[0].maxBitrate = 2000000; // 2Mbps
                params.encodings[0].maxFramerate = 30;
                await sender.setParameters(params);
                console.log('✅ 视频质量参数设置成功');
              }
            } catch (error) {
              console.warn('设置视频质量参数失败:', error);
            }
          }, 1000);
        }
      });

      // 创建offer
      const offer = await this.pc.createOffer();
      await this.pc.setLocalDescription(offer);

      // 发送offer到服务器
      const response = await fetch(`${this.serverUrl}/offer`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sdp: offer.sdp,
          type: offer.type
        })
      });

      if (!response.ok) {
        throw new Error(`服务器响应错误: ${response.status}`);
      }

      const answer = await response.json();
      
      // 设置远程描述
      await this.pc.setRemoteDescription(new RTCSessionDescription(answer));
      
      console.log('✅ WebRTC连接建立成功');
      
    } catch (error) {
      console.error('❌ WebRTC连接失败:', error);
      throw new Error(`WebRTC连接失败: ${error.message}`);
    }
  }

  /**
   * 设置WebSocket连接接收分析结果
   */
  async setupWebSocket() {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.wsUrl);
        
        this.ws.onopen = () => {
          console.log('✅ WebSocket连接建立');
          resolve();
        };
        
        this.ws.onmessage = (event) => {
          try {
            const result = JSON.parse(event.data);
            console.log('📊 收到分析结果:', result);

            // 处理注意力分析结果
            if (result.attention_level !== undefined) {
              if (this.onResultCallback) {
                this.onResultCallback(result);
              }
            }

            // 处理468个特征点数据
            if (result.landmarks && Array.isArray(result.landmarks)) {
              console.log(`🎯 收到 ${result.landmarks.length} 个特征点`);
              if (this.onLandmarksCallback) {
                this.onLandmarksCallback(result.landmarks);
              }
            }

            // 处理包含特征点的完整结果
            if (result.face_landmarks && Array.isArray(result.face_landmarks)) {
              console.log(`🎯 收到面部特征点: ${result.face_landmarks.length} 个`);
              if (this.onLandmarksCallback) {
                this.onLandmarksCallback(result.face_landmarks);
              }
            }

          } catch (error) {
            console.error('解析分析结果失败:', error);
          }
        };
        
        this.ws.onerror = (error) => {
          console.error('❌ WebSocket错误:', error);
          reject(new Error('WebSocket连接失败'));
        };
        
        this.ws.onclose = () => {
          console.log('WebSocket连接关闭');
        };
        
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 获取本地视频流（用于显示）
   */
  getLocalStream() {
    return this.localStream;
  }

  /**
   * 获取远程视频流（带分析结果的视频）
   */
  getRemoteStream() {
    return this.remoteStream;
  }

  /**
   * 检查连接状态
   */
  isAnalysisRunning() {
    return this.isConnected && this.pc && this.pc.connectionState === 'connected';
  }

  /**
   * 更新状态
   */
  updateStatus(message, type = 'info') {
    console.log(`[${type.toUpperCase()}] ${message}`);
    
    if (this.onStatusCallback) {
      this.onStatusCallback(message, type);
    }
  }

  /**
   * 设置远程视频流到视频元素
   */
  setRemoteStreamToVideo() {
    if (this.remoteStream && this.videoElement) {
      console.log('设置远程视频流到视频元素');
      this.videoElement.srcObject = this.remoteStream;
    }
  }

  /**
   * 设置视频元素引用
   * @param {HTMLVideoElement} videoElement - 视频元素
   */
  setVideoElement(videoElement) {
    this.videoElement = videoElement;
    console.log('设置视频元素引用');
  }

  /**
   * 检查服务器健康状态
   */
  async checkServerHealth() {
    try {
      const response = await fetch(`${this.serverUrl}/health`);
      const data = await response.json();
      return data.status === 'healthy';
    } catch (error) {
      console.error('服务器健康检查失败:', error);
      return false;
    }
  }
}

export default WebRTCAttentionClient;
