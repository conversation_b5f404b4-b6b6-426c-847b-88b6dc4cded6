package org.dromara.patient.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.patient.domain.ScaleTestRecords;
import org.dromara.patient.mapper.ScaleTestRecordsMapper;
import org.dromara.patient.service.IScaleTestRecordsService;
// import org.dromara.patient.service.IAIAnalysisService;
// import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ADHD量表测试记录Service业务层处理
 *
 * <AUTHOR> System
 * @date 2025-07-19
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ScaleTestRecordsServiceImpl implements IScaleTestRecordsService {

    private final ScaleTestRecordsMapper baseMapper;

    // @Qualifier("ollamaAIAnalysisService")
    // private final IAIAnalysisService aiAnalysisService;

    /**
     * 查询ADHD量表测试记录
     */
    @Override
    public ScaleTestRecords queryById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 查询ADHD量表测试记录列表
     */
    @Override
    public TableDataInfo<ScaleTestRecords> queryPageList(ScaleTestRecords scaleTestRecords, PageQuery pageQuery) {
        LambdaQueryWrapper<ScaleTestRecords> lqw = buildQueryWrapper(scaleTestRecords);
        Page<ScaleTestRecords> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询ADHD量表测试记录列表
     */
    @Override
    public List<ScaleTestRecords> queryList(ScaleTestRecords scaleTestRecords) {
        LambdaQueryWrapper<ScaleTestRecords> lqw = buildQueryWrapper(scaleTestRecords);
        return baseMapper.selectList(lqw);
    }

    private LambdaQueryWrapper<ScaleTestRecords> buildQueryWrapper(ScaleTestRecords scaleTestRecords) {
        LambdaQueryWrapper<ScaleTestRecords> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(scaleTestRecords.getUserId()), ScaleTestRecords::getUserId, scaleTestRecords.getUserId());
        lqw.like(StringUtils.isNotBlank(scaleTestRecords.getUsername()), ScaleTestRecords::getUsername, scaleTestRecords.getUsername());
        lqw.eq(scaleTestRecords.getAge() != null, ScaleTestRecords::getAge, scaleTestRecords.getAge());
        lqw.eq(StringUtils.isNotBlank(scaleTestRecords.getGender()), ScaleTestRecords::getGender, scaleTestRecords.getGender());
        lqw.eq(StringUtils.isNotBlank(scaleTestRecords.getScaleType()), ScaleTestRecords::getScaleType, scaleTestRecords.getScaleType());
        lqw.eq(StringUtils.isNotBlank(scaleTestRecords.getRiskLevel()), ScaleTestRecords::getRiskLevel, scaleTestRecords.getRiskLevel());
        lqw.eq(StringUtils.isNotBlank(scaleTestRecords.getSeverityLevel()), ScaleTestRecords::getSeverityLevel, scaleTestRecords.getSeverityLevel());
        lqw.eq(StringUtils.isNotBlank(scaleTestRecords.getAnalysisMethod()), ScaleTestRecords::getAnalysisMethod, scaleTestRecords.getAnalysisMethod());
        lqw.orderByDesc(ScaleTestRecords::getCreateTime);
        return lqw;
    }

    /**
     * 新增ADHD量表测试记录
     */
    @Override
    public Boolean insertByBo(ScaleTestRecords scaleTestRecords) {
        try {
            // 暂时移除AI服务，使用传统方法
            scaleTestRecords.setAnalysisMethod("traditional");
            scaleTestRecords.setProfessionalAdvice("基于量表得分的基础分析，建议专业医生进一步评估");
            scaleTestRecords.setRiskAssessment("请根据量表得分进行风险评估");
            scaleTestRecords.setRehabilitationPlan("建议制定个性化康复计划");

            log.info("✅ 使用传统方法生成分析 - 患者: {}", scaleTestRecords.getUsername());

            return baseMapper.insert(scaleTestRecords) > 0;
        } catch (Exception e) {
            log.error("新增量表测试记录失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 修改ADHD量表测试记录
     */
    @Override
    public Boolean updateByBo(ScaleTestRecords scaleTestRecords) {
        return baseMapper.updateById(scaleTestRecords) > 0;
    }

    /**
     * 批量删除ADHD量表测试记录
     */
    @Override
    public Boolean deleteWithValidByIds(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
