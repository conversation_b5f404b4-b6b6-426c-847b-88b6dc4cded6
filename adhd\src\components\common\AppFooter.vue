<template>
  <footer class="app-footer">
    <p>
      © 2025 智能心理健康检测与筛查系统 | 
      <a href="#" @click.prevent="showPrivacyPolicy">隐私政策</a> | 
      <a href="#" @click.prevent="showTermsOfService">使用条款</a> |
      <a href="#" @click.prevent="showContactUs">联系我们</a>
    </p>
    <div class="footer-links">
      <a href="#" @click.prevent="showHelp">帮助中心</a>
      <a href="#" @click.prevent="showFeedback">意见反馈</a>
      <a href="#" @click.prevent="showAbout">关于我们</a>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'AppFooter',
  methods: {
    showPrivacyPolicy() {
      this.$emit('show-privacy-policy');
      console.log('显示隐私政策');
    },
    showTermsOfService() {
      this.$emit('show-terms');
      console.log('显示使用条款');
    },
    showContactUs() {
      this.$emit('show-contact');
      console.log('显示联系我们');
    },
    showHelp() {
      this.$emit('show-help');
      console.log('显示帮助中心');
    },
    showFeedback() {
      this.$emit('show-feedback');
      console.log('显示意见反馈');
    },
    showAbout() {
      this.$emit('show-about');
      console.log('显示关于我们');
    }
  }
}
</script>

<style scoped>
.app-footer {
  text-align: center;
  padding: 20px 15px;
  color: #777;
  background-color: #fff;
  border-top: 1px solid #eee;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.app-footer a {
  color: var(--primary-color, #4e73df);
  text-decoration: none;
  transition: color var(--transition-speed, 0.3s);
}

.app-footer a:hover {
  text-decoration: underline;
  color: var(--primary-dark, #3a5bbd);
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
}

@media (max-width: 768px) {
  .app-footer {
    padding: 15px 10px;
    font-size: 13px;
  }
  
  .footer-links {
    gap: 15px;
  }
}
</style>
