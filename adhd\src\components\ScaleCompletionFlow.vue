<template>
  <div class="scale-completion-flow">
    <!-- 量表完成后的数据收集和分析流程 -->
    <ScaleSubmissionDialog
      :visible="showSubmissionDialog"
      :patient-info="patientInfo"
      :scale-result="scaleResult"
      @view-result="handleViewResult"
      @send-to-doctor="handleSendToDoctor"
    />

    <!-- 详细结果展示 -->
    <el-dialog
      title="AI综合分析结果"
      :visible.sync="showDetailedResult"
      width="90%"
      top="5vh"
      custom-class="result-dialog"
    >
      <div class="detailed-result-content">
        <!-- 结果概览 -->
        <div class="result-overview">
          <el-card>
            <div slot="header" class="result-header">
              <div class="patient-info">
                <h3>{{ patientInfo.name || '患者' }} 的综合分析报告</h3>
                <p>评测时间: {{ formatTime(analysisResult.submitTime) }}</p>
              </div>
              <div class="risk-badge">
                <el-tag :type="getRiskTagType(analysisResult.riskLevel)" size="large">
                  {{ getRiskLevelText(analysisResult.riskLevel) }}
                </el-tag>
              </div>
            </div>
            
            <el-row :gutter="30">
              <el-col :span="8">
                <div class="score-section">
                  <h4>综合评分</h4>
                  <div class="score-circle">
                    <el-progress
                      type="circle"
                      :percentage="analysisResult.totalScore"
                      :width="120"
                      :stroke-width="8"
                      :color="getScoreColor(analysisResult.totalScore)">
                      <span class="score-text">{{ analysisResult.totalScore }}</span>
                    </el-progress>
                  </div>
                </div>
              </el-col>
              <el-col :span="16">
                <div class="key-findings">
                  <h4>关键发现</h4>
                  <div class="findings-list">
                    <div v-for="finding in analysisResult.keyFindings" :key="finding" class="finding-item">
                      <i class="el-icon-right"></i>
                      <span>{{ finding }}</span>
                    </div>
                  </div>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </div>

        <!-- 量表详细分析 -->
        <div class="scale-detailed-analysis">
          <el-card>
            <div slot="header">
              <i class="el-icon-document"></i> 量表详细分析
            </div>
            
            <div class="scale-info">
              <h4>{{ scaleResult.scaleName || '量表' }} 分析结果</h4>
              <p class="scale-description">{{ scaleResult.description || '基于标准化量表的专业评估' }}</p>
            </div>

            <!-- 题目分析 -->
            <div class="question-analysis" v-if="analysisResult.questionAnalysis">
              <h5>题目分析</h5>
              <div class="question-grid">
                <div 
                  v-for="(question, index) in analysisResult.questionAnalysis" 
                  :key="index"
                  class="question-item"
                  :class="getQuestionRiskClass(question.riskLevel)"
                >
                  <div class="question-header">
                    <span class="question-number">Q{{ index + 1 }}</span>
                    <el-tag :type="getQuestionTagType(question.riskLevel)" size="mini">
                      {{ question.riskLevel }}
                    </el-tag>
                  </div>
                  <div class="question-content">
                    <p class="question-text">{{ question.questionText }}</p>
                    <p class="answer-text">
                      <strong>回答:</strong> {{ question.answerText }}
                      <span class="score-info">({{ question.score }}分)</span>
                    </p>
                    <p class="ai-interpretation" v-if="question.aiInterpretation">
                      <strong>AI解读:</strong> {{ question.aiInterpretation }}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 维度分析 -->
            <div class="dimension-scores" v-if="analysisResult.dimensionScores">
              <h5>维度评分</h5>
              <el-row :gutter="20">
                <el-col :span="8" v-for="dimension in analysisResult.dimensionScores" :key="dimension.name">
                  <div class="dimension-card">
                    <h6>{{ dimension.name }}</h6>
                    <el-progress
                      :percentage="dimension.percentage"
                      :stroke-width="6"
                      :color="getDimensionColor(dimension.level)">
                    </el-progress>
                    <div class="dimension-info">
                      <span class="dimension-score">{{ dimension.score }}/{{ dimension.maxScore }}</span>
                      <el-tag :type="getDimensionTagType(dimension.level)" size="mini">
                        {{ dimension.level }}
                      </el-tag>
                    </div>
                    <p class="dimension-desc">{{ dimension.description }}</p>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>

        <!-- AI诊断建议 -->
        <div class="ai-recommendations" v-if="analysisResult.recommendations">
          <el-card>
            <div slot="header">
              <i class="el-icon-lightbulb"></i> AI诊断建议
            </div>
            
            <div class="recommendations-content">
              <!-- 初步诊断 -->
              <div class="primary-diagnosis" v-if="analysisResult.recommendations.diagnosis">
                <h5>初步诊断倾向</h5>
                <div class="diagnosis-list">
                  <div 
                    v-for="diagnosis in analysisResult.recommendations.diagnosis" 
                    :key="diagnosis.code"
                    class="diagnosis-item"
                  >
                    <div class="diagnosis-header">
                      <span class="diagnosis-name">{{ diagnosis.name }}</span>
                      <div class="confidence-bar">
                        <el-progress 
                          :percentage="diagnosis.confidence" 
                          :stroke-width="4"
                          :show-text="false"
                          color="#67C23A">
                        </el-progress>
                        <span class="confidence-text">{{ diagnosis.confidence }}%</span>
                      </div>
                    </div>
                    <p class="diagnosis-desc">{{ diagnosis.description }}</p>
                  </div>
                </div>
              </div>

              <!-- 干预建议 -->
              <div class="intervention-suggestions" v-if="analysisResult.recommendations.interventions">
                <h5>干预建议</h5>
                <div class="suggestions-grid">
                  <div 
                    v-for="suggestion in analysisResult.recommendations.interventions" 
                    :key="suggestion.type"
                    class="suggestion-card"
                  >
                    <div class="suggestion-icon">
                      <i :class="getSuggestionIcon(suggestion.type)"></i>
                    </div>
                    <div class="suggestion-content">
                      <h6>{{ suggestion.title }}</h6>
                      <p>{{ suggestion.description }}</p>
                      <el-tag :type="getPriorityTagType(suggestion.priority)" size="mini">
                        {{ suggestion.priority }}
                      </el-tag>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 随访计划 -->
              <div class="follow-up-plan" v-if="analysisResult.recommendations.followUp">
                <h5>建议随访计划</h5>
                <el-timeline>
                  <el-timeline-item 
                    v-for="plan in analysisResult.recommendations.followUp" 
                    :key="plan.time"
                    :timestamp="plan.time"
                    :color="getTimelineColor(plan.type)"
                  >
                    <h6>{{ plan.title }}</h6>
                    <p>{{ plan.description }}</p>
                  </el-timeline-item>
                </el-timeline>
              </div>
            </div>
          </el-card>
        </div>

        <!-- 多维度数据分析 -->
        <div class="multidimensional-analysis" v-if="analysisResult.multidimensionalAnalysis">
          <el-card>
            <div slot="header">
              <i class="el-icon-pie-chart"></i> 多维度综合分析
            </div>
            
            <div class="dimension-analysis">
              <el-row :gutter="20">
                <el-col :span="8" v-for="dimension in analysisResult.multidimensionalAnalysis" :key="dimension.type">
                  <div class="multi-dimension-card">
                    <h6>{{ dimension.name }}</h6>
                    <el-rate 
                      v-model="dimension.rating" 
                      :max="5" 
                      disabled 
                      show-score
                      text-color="#ff9900">
                    </el-rate>
                    <div class="dimension-tags">
                      <el-tag 
                        v-for="tag in dimension.tags" 
                        :key="tag"
                        size="mini"
                        type="info"
                        style="margin: 2px;">
                        {{ tag }}
                      </el-tag>
                    </div>
                    <p class="dimension-summary">{{ dimension.summary }}</p>
                  </div>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="showDetailedResult = false">关闭</el-button>
        <el-button type="success" @click="exportReport">
          <i class="el-icon-download"></i> 导出报告
        </el-button>
        <el-button type="primary" @click="sendToDoctor">
          <i class="el-icon-s-promotion"></i> 推送给医生
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import ScaleSubmissionDialog from './ScaleSubmissionDialog.vue'

export default {
  name: 'ScaleCompletionFlow',
  components: {
    ScaleSubmissionDialog
  },
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    patientInfo: {
      type: Object,
      default: () => ({
        id: 1001,
        name: '张小明',
        age: 12,
        gender: '男'
      })
    },
    scaleResult: {
      type: Object,
      default: () => ({
        scaleType: 'adhd',
        scaleName: 'ADHD评估量表',
        totalScore: 75,
        questions: [],
        answers: [],
        questionDetails: []
      })
    }
  },
  
  data() {
    return {
      showSubmissionDialog: false,
      showDetailedResult: false,
      analysisResult: null
    }
  },
  
  watch: {
    visible(val) {
      if (val) {
        this.showSubmissionDialog = true
      }
    }
  },
  
  methods: {
    // 查看详细结果
    handleViewResult(result) {
      this.analysisResult = result
      this.showDetailedResult = true
    },
    
    // 推送给医生
    handleSendToDoctor(result) {
      this.$emit('send-to-doctor', result)
    },
    
    // 导出报告
    exportReport() {
      // TODO: 实现报告导出
      this.$message.success('报告导出功能开发中...')
    },
    
    // 推送给医生
    sendToDoctor() {
      this.$confirm('确认将分析结果推送给医生？', '推送确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }).then(() => {
        this.handleSendToDoctor(this.analysisResult)
        this.$message.success('已推送给医生')
      })
    },
    
    // 格式化时间
    formatTime(time) {
      if (!time) return ''
      return new Date(time).toLocaleString()
    },
    
    // 获取风险标签类型
    getRiskTagType(level) {
      const types = {
        'URGENT': 'danger',
        'HIGH': 'warning',
        'MEDIUM': 'info',
        'LOW': 'success'
      }
      return types[level] || 'info'
    },
    
    // 获取风险等级文本
    getRiskLevelText(level) {
      const texts = {
        'URGENT': '紧急风险',
        'HIGH': '高风险',
        'MEDIUM': '中等风险',
        'LOW': '低风险'
      }
      return texts[level] || level
    },
    
    // 获取分数颜色
    getScoreColor(score) {
      if (score >= 80) return '#F56C6C'
      if (score >= 60) return '#E6A23C'
      if (score >= 40) return '#409EFF'
      return '#67C23A'
    },
    
    // 获取题目风险样式类
    getQuestionRiskClass(level) {
      return `risk-${level?.toLowerCase() || 'normal'}`
    },
    
    // 获取题目标签类型
    getQuestionTagType(level) {
      const types = {
        'HIGH': 'danger',
        'MEDIUM': 'warning',
        'LOW': 'success',
        'NORMAL': 'info'
      }
      return types[level] || 'info'
    },
    
    // 获取维度颜色
    getDimensionColor(level) {
      const colors = {
        '重度': '#F56C6C',
        '中重度': '#E6A23C',
        '中度': '#409EFF',
        '轻度': '#67C23A',
        '正常': '#67C23A'
      }
      return colors[level] || '#409EFF'
    },
    
    // 获取维度标签类型
    getDimensionTagType(level) {
      const types = {
        '重度': 'danger',
        '中重度': 'warning',
        '中度': 'info',
        '轻度': 'success',
        '正常': 'success'
      }
      return types[level] || 'info'
    },
    
    // 获取建议图标
    getSuggestionIcon(type) {
      const icons = {
        'medical': 'el-icon-first-aid-kit',
        'therapy': 'el-icon-chat-line-round',
        'lifestyle': 'el-icon-time',
        'family': 'el-icon-house',
        'education': 'el-icon-reading'
      }
      return icons[type] || 'el-icon-info'
    },
    
    // 获取优先级标签类型
    getPriorityTagType(priority) {
      const types = {
        '紧急': 'danger',
        '重要': 'warning',
        '一般': 'info'
      }
      return types[priority] || 'info'
    },
    
    // 获取时间线颜色
    getTimelineColor(type) {
      const colors = {
        'immediate': '#F56C6C',
        'short_term': '#E6A23C',
        'long_term': '#409EFF'
      }
      return colors[type] || '#409EFF'
    }
  }
}
</script>
