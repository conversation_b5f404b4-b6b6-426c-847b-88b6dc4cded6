/**
 * 数据收集服务
 * 负责收集、存储和管理检测数据
 */

import dataSync from '@/utils/dataSync.js'

class DataCollectionService {
  constructor() {
    this.storageKey = 'adhd_detection_data';
    this.sessionKey = 'adhd_current_session';
    this.initStorage();
  }

  /**
   * 初始化本地存储
   */
  initStorage() {
    if (!localStorage.getItem(this.storageKey)) {
      const initialData = {
        sessions: [],
        statistics: {
          totalSessions: 0,
          totalDuration: 0,
          totalDetections: 0,
          lastSessionDate: null
        },
        dailyData: {},
        weeklyData: {},
        monthlyData: {}
      };
      localStorage.setItem(this.storageKey, JSON.stringify(initialData));
    }
  }

  /**
   * 开始新的检测会话
   */
  startSession() {
    const session = {
      id: this.generateSessionId(),
      startTime: new Date().toISOString(),
      endTime: null,
      duration: 0,
      detections: [],
      statistics: {
        totalDetections: 0,
        attentionLevels: { 0: 0, 1: 0, 2: 0, 3: 0 },
        avgAttentionLevel: 0,
        avgAttentionScore: 0,
        maxAttentionScore: 0,
        minAttentionScore: 100,
        avgEyeScore: 0,
        avgFaceScore: 0,
        avgMotionScore: 0,
        emotions: {}
      }
    };

    localStorage.setItem(this.sessionKey, JSON.stringify(session));
    console.log('🎯 开始新的检测会话:', session.id);
    return session;
  }

  /**
   * 记录检测结果
   */
  recordDetection(result) {
    try {
      console.log('📥 数据收集服务收到检测结果:', result);

      // 验证输入数据
      if (!result || typeof result !== 'object') {
        console.error('❌ 无效的检测结果数据:', result);
        return null;
      }

      const currentSession = this.getCurrentSession();
      if (!currentSession) {
        console.warn('⚠️ 没有活动的检测会话，创建新会话');
        const newSession = this.startSession();
        if (!newSession) {
          console.error('❌ 无法创建新会话');
          return null;
        }
        return this.recordDetection(result);
      }

      console.log('📋 当前会话信息:', {
        id: currentSession.id,
        startTime: currentSession.startTime,
        detectionsCount: currentSession.detections.length
      });

      // 添加检测数据，确保数据完整性
      const detection = {
        timestamp: new Date().toISOString(),
        attention_level: result.attention_level !== undefined && result.attention_level !== null ? Number(result.attention_level) : 1,
        level_name: result.level_name || this.getLevelName(result.attention_level) || '正常',
        attention_score: Number(result.attention_score) || 0,
        confidence: Number(result.confidence) || 0,
        eye_score: Number(result.details?.eye_score || result.eye_score) || 0,
        face_score: Number(result.details?.face_score || result.face_score) || 0,
        motion_score: Number(result.details?.motion_score || result.motion_score) || 0,
        emotion: result.emotion || 'neutral'
      };

      console.log('📊 准备添加的检测数据:', detection);

      currentSession.detections.push(detection);
      currentSession.statistics.totalDetections++;

      // 更新统计数据
      this.updateSessionStatistics(currentSession, detection);

      // 保存会话到localStorage
      try {
        localStorage.setItem(this.sessionKey, JSON.stringify(currentSession));
        console.log('💾 会话数据已保存到localStorage');
      } catch (error) {
        console.error('❌ 保存会话数据失败:', error);
        return null;
      }

      // 触发数据更新事件
      const eventData = {
        sessionId: currentSession.id,
        detection: detection,
        totalDetections: currentSession.statistics.totalDetections,
        timestamp: detection.timestamp
      };

      dataSync.publish('detection-updated', eventData);

      console.log('✅ 检测数据记录成功:', detection);
      console.log('📊 当前会话检测总数:', currentSession.statistics.totalDetections);
      console.log('📡 数据更新事件已触发:', eventData);

      return detection;
    } catch (error) {
      console.error('记录检测数据失败:', error);
    }
  }

  /**
   * 更新会话统计数据
   */
  updateSessionStatistics(session, detection) {
    const stats = session.statistics;

    console.log('📊 更新会话统计 - 检测数据:', detection);
    console.log('📊 更新前的等级统计:', stats.attentionLevels);

    // 🔥 修复注意力等级统计更新
    let level = detection.attention_level;
    console.log('📊 检测到的注意力等级:', level, '类型:', typeof level);

    // 如果没有attention_level，从attention_score推算
    if (level === undefined || level === null) {
      const score = Number(detection.attention_score) || 0;
      if (score >= 80) level = 0;      // 高度专注
      else if (score >= 60) level = 1; // 专注
      else if (score >= 40) level = 2; // 分心
      else level = 3;                  // 发呆

      console.log('🔧 从分数推算等级:', score, '→', level);
    }

    // 确保level是数字类型
    const numLevel = Number(level);
    if (!isNaN(numLevel) && stats.attentionLevels[numLevel] !== undefined) {
      stats.attentionLevels[numLevel]++;
      console.log('📊 等级', numLevel, '计数已更新，新值:', stats.attentionLevels[numLevel]);
    } else {
      console.warn('⚠️ 无效的注意力等级:', level, '转换后:', numLevel, '可用等级:', Object.keys(stats.attentionLevels));
    }

    console.log('📊 更新后的等级统计:', stats.attentionLevels);

    // 更新情绪统计
    const emotion = detection.emotion;
    stats.emotions[emotion] = (stats.emotions[emotion] || 0) + 1;

    // 计算平均分数
    const detections = session.detections;
    // const count = detections.length;

    stats.avgAttentionScore = this.calculateAverage(detections, 'attention_score');
    stats.avgAttentionLevel = this.calculateAverage(detections, 'attention_level');
    stats.avgEyeScore = this.calculateAverage(detections, 'eye_score');
    stats.avgFaceScore = this.calculateAverage(detections, 'face_score');
    stats.avgMotionScore = this.calculateAverage(detections, 'motion_score');

    // 计算最大最小注意力分数
    if (detections.length > 0) {
      const scores = detections.map(d => d.attention_score || 0);
      stats.maxAttentionScore = Math.max(...scores);
      stats.minAttentionScore = Math.min(...scores);
    }
  }

  /**
   * 计算平均值
   */
  calculateAverage(array, field) {
    if (array.length === 0) return 0;
    const sum = array.reduce((acc, item) => acc + (item[field] || 0), 0);
    return Math.round((sum / array.length) * 100) / 100;
  }

  /**
   * 重新计算会话统计数据（在会话结束时调用）
   */
  recalculateSessionStatistics(session) {
    console.log('🔄 重新计算会话统计数据...');
    console.log('📊 检测数据数量:', session.detections.length);

    const detections = session.detections;
    const stats = session.statistics;

    // 重置统计数据
    stats.totalDetections = detections.length;
    stats.attentionLevels = { 0: 0, 1: 0, 2: 0, 3: 0 };
    stats.emotions = {};

    if (detections.length === 0) {
      console.warn('⚠️ 没有检测数据，统计结果将为空');
      return;
    }

    // 重新计算所有统计数据
    detections.forEach((detection, index) => {
      // 只在前5个检测中显示详细日志，避免日志过多
      if (index < 5) {
        console.log(`📊 处理检测 ${index + 1}:`, detection);
        console.log(`  attention_level: ${detection.attention_level} (类型: ${typeof detection.attention_level})`);
        console.log(`  attention_score: ${detection.attention_score}`);
      }

      // 🔥 修复注意力等级识别问题
      let level = detection.attention_level;

      // 如果没有attention_level，尝试从attention_score推算
      if (level === undefined || level === null) {
        const score = Number(detection.attention_score) || 0;
        if (score >= 80) level = 0;      // 高度专注
        else if (score >= 60) level = 1; // 专注
        else if (score >= 40) level = 2; // 分心
        else level = 3;                  // 发呆

        if (index < 5) {
          console.log(`  🔧 从分数推算等级: ${score} → ${level}`);
        }
      }

      // 确保level是数字
      level = Number(level);

      if (!isNaN(level) && stats.attentionLevels[level] !== undefined) {
        stats.attentionLevels[level]++;
        if (index < 5) {
          console.log(`  ✅ 等级 ${level} 计数更新: ${stats.attentionLevels[level]}`);
        }
      } else {
        if (index < 5) {
          console.warn(`  ⚠️ 无效的注意力等级: ${level}`);
        }
      }

      // 情绪统计
      const emotion = detection.emotion;
      if (emotion) {
        stats.emotions[emotion] = (stats.emotions[emotion] || 0) + 1;
      }
    });

    // 计算平均分数
    stats.avgAttentionScore = this.calculateAverage(detections, 'attention_score');
    stats.avgAttentionLevel = this.calculateAverage(detections, 'attention_level');
    stats.avgEyeScore = this.calculateAverage(detections, 'eye_score');
    stats.avgFaceScore = this.calculateAverage(detections, 'face_score');
    stats.avgMotionScore = this.calculateAverage(detections, 'motion_score');

    // 计算最大最小注意力分数
    const scores = detections.map(d => d.attention_score || 0);
    stats.maxAttentionScore = Math.max(...scores);
    stats.minAttentionScore = Math.min(...scores);

    console.log('✅ 重新计算完成，最终统计数据:', stats);
  }

  /**
   * 根据注意力等级获取等级名称
   */
  getLevelName(level) {
    const levelNames = {
      0: '专注',
      1: '正常',
      2: '分心',
      3: '发呆'
    };
    return levelNames[level] || '正常';
  }

  /**
   * 结束当前会话
   */
  endSession() {
    try {
      const currentSession = this.getCurrentSession();
      if (!currentSession) {
        console.warn('没有活动的检测会话');
        return null;
      }

      // 设置结束时间和持续时间
      currentSession.endTime = new Date().toISOString();
      const startTime = new Date(currentSession.startTime);
      const endTime = new Date(currentSession.endTime);
      const durationMs = endTime - startTime;
      const durationMinutes = durationMs / 1000 / 60;

      // 🔍 调试时间计算
      console.log('🔍 时间计算调试:');
      console.log('  startTime:', currentSession.startTime);
      console.log('  endTime:', currentSession.endTime);
      console.log('  durationMs:', durationMs);
      console.log('  durationMinutes:', durationMinutes);

      // 🔥 修复持续时间计算：如果少于1分钟，按秒计算但至少1分钟
      if (durationMinutes < 1) {
        const durationSeconds = Math.round(durationMs / 1000);
        console.log('  durationSeconds:', durationSeconds);
        // 如果检测时间很短，但有足够的检测数据，按检测数据量估算时间
        if (currentSession.detections.length > 30) {
          // 假设每秒1-2次检测，估算实际时间
          const estimatedMinutes = Math.max(1, Math.round(currentSession.detections.length / 60));
          currentSession.duration = estimatedMinutes;
          console.log('  🔧 根据检测数据量估算时间:', estimatedMinutes, '分钟');
        } else {
          currentSession.duration = 1; // 最少1分钟
        }
      } else {
        currentSession.duration = Math.round(durationMinutes);
      }

      // 🔥 重新计算最终统计数据，确保数据准确
      this.recalculateSessionStatistics(currentSession);

      // 🔍 最终数据验证
      console.log('🔍 最终会话数据验证:');
      console.log('  会话ID:', currentSession.id);
      console.log('  检测数据数量:', currentSession.detections.length);
      console.log('  统计数据:', currentSession.statistics);
      console.log('  注意力等级分布:', currentSession.statistics.attentionLevels);

      // 保存到历史数据
      this.saveSessionToHistory(currentSession);

      // 更新全局统计
      this.updateGlobalStatistics(currentSession);

      // 清除当前会话
      localStorage.removeItem(this.sessionKey);

      // 触发会话结束事件
      dataSync.publish('session-ended', {
        sessionId: currentSession.id,
        duration: currentSession.duration,
        totalDetections: currentSession.statistics.totalDetections,
        avgAttentionScore: currentSession.statistics.avgAttentionScore
      });

      console.log('✅ 检测会话结束:', currentSession.id, '持续时间:', currentSession.duration, '分钟');
      return currentSession;
    } catch (error) {
      console.error('结束会话失败:', error);
      return null;
    }
  }

  /**
   * 保存会话到历史记录
   */
  saveSessionToHistory(session) {
    const data = this.getAllData();
    data.sessions.push(session);
    
    // 只保留最近100个会话
    if (data.sessions.length > 100) {
      data.sessions = data.sessions.slice(-100);
    }
    
    localStorage.setItem(this.storageKey, JSON.stringify(data));
  }

  /**
   * 更新全局统计数据
   */
  updateGlobalStatistics(session) {
    const data = this.getAllData();
    const stats = data.statistics;
    
    stats.totalSessions++;
    stats.totalDuration += session.duration;
    stats.totalDetections += session.statistics.totalDetections;
    stats.lastSessionDate = session.endTime;
    
    // 更新日期统计
    this.updateDateStatistics(data, session);
    
    localStorage.setItem(this.storageKey, JSON.stringify(data));
  }

  /**
   * 更新日期统计数据
   */
  updateDateStatistics(data, session) {
    const date = new Date(session.startTime);
    const dateKey = date.toISOString().split('T')[0]; // YYYY-MM-DD
    const weekKey = this.getWeekKey(date);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

    // 日统计
    if (!data.dailyData[dateKey]) {
      data.dailyData[dateKey] = {
        sessions: 0,
        duration: 0,
        detections: 0,
        avgAttention: 0
      };
    }
    data.dailyData[dateKey].sessions++;
    data.dailyData[dateKey].duration += session.duration;
    data.dailyData[dateKey].detections += session.statistics.totalDetections;
    data.dailyData[dateKey].avgAttention = session.statistics.avgAttentionScore;

    // 周统计
    if (!data.weeklyData[weekKey]) {
      data.weeklyData[weekKey] = {
        sessions: 0,
        duration: 0,
        detections: 0,
        avgAttention: 0
      };
    }
    data.weeklyData[weekKey].sessions++;
    data.weeklyData[weekKey].duration += session.duration;
    data.weeklyData[weekKey].detections += session.statistics.totalDetections;

    // 月统计
    if (!data.monthlyData[monthKey]) {
      data.monthlyData[monthKey] = {
        sessions: 0,
        duration: 0,
        detections: 0,
        avgAttention: 0
      };
    }
    data.monthlyData[monthKey].sessions++;
    data.monthlyData[monthKey].duration += session.duration;
    data.monthlyData[monthKey].detections += session.statistics.totalDetections;
  }

  /**
   * 获取当前会话
   */
  getCurrentSession() {
    const sessionData = localStorage.getItem(this.sessionKey);
    return sessionData ? JSON.parse(sessionData) : null;
  }

  /**
   * 获取所有数据
   */
  getAllData() {
    const data = localStorage.getItem(this.storageKey);
    return data ? JSON.parse(data) : null;
  }

  /**
   * 获取统计数据
   */
  getStatistics() {
    const data = this.getAllData();
    return data ? data.statistics : null;
  }

  /**
   * 获取最近的会话数据
   */
  getRecentSessions(limit = 10) {
    const data = this.getAllData();
    if (!data || !data.sessions) return [];
    
    return data.sessions
      .sort((a, b) => new Date(b.startTime) - new Date(a.startTime))
      .slice(0, limit);
  }

  /**
   * 获取指定时间范围的数据
   */
  getDataByRange(range = 'week') {
    const data = this.getAllData();
    if (!data) return null;

    const now = new Date();
    let startDate;

    switch (range) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      case 'year':
        startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
        break;
      default:
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    }

    return data.sessions.filter(session => 
      new Date(session.startTime) >= startDate
    );
  }

  /**
   * 生成会话ID
   */
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * 获取周键值
   */
  getWeekKey(date) {
    const year = date.getFullYear();
    const week = this.getWeekNumber(date);
    return `${year}-W${String(week).padStart(2, '0')}`;
  }

  /**
   * 获取周数
   */
  getWeekNumber(date) {
    const d = new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate()));
    const dayNum = d.getUTCDay() || 7;
    d.setUTCDate(d.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
    return Math.ceil((((d - yearStart) / 86400000) + 1) / 7);
  }

  /**
   * 添加历史会话（用于测试）
   */
  addHistoricalSession(sessionData) {
    if (!sessionData || !sessionData.id) {
      console.error('无效的会话数据');
      return;
    }

    const data = this.getAllData() || this.getDefaultData();

    // 添加会话
    data.sessions.push(sessionData);

    // 更新全局统计
    data.statistics.totalSessions = data.sessions.length;
    data.statistics.totalDuration += sessionData.duration;
    data.statistics.totalDetections += sessionData.statistics.totalDetections;

    // 更新日期统计
    this.updateDateStatistics(data, sessionData);

    // 保存数据
    localStorage.setItem(this.storageKey, JSON.stringify(data));

    console.log('📝 历史会话已添加:', sessionData.id);
  }

  /**
   * 清除所有数据
   */
  clearAllData() {
    localStorage.removeItem(this.storageKey);
    localStorage.removeItem(this.sessionKey);
    this.initStorage();
    console.log('🗑️ 已清除所有检测数据');
  }
}

// 创建单例实例
const dataCollectionService = new DataCollectionService();

export default dataCollectionService;
