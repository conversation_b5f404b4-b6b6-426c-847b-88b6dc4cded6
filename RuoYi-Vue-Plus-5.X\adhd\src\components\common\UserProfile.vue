<template>
  <div class="user-profile">
    <!-- 用户头像和下拉菜单 -->
    <div class="profile-dropdown" @click="toggleDropdown" v-click-outside="closeDropdown">
      <div class="avatar-container">
        <img 
          :src="currentAvatar" 
          :alt="userInfo.username" 
          class="user-avatar"
        >
        <i class="fas fa-chevron-down dropdown-icon" :class="{ 'rotated': showDropdown }"></i>
      </div>
      
      <!-- 下拉菜单 -->
      <div class="dropdown-menu" v-show="showDropdown">
        <div class="user-info">
          <div class="user-name">{{ userInfo.username }}</div>
          <div class="user-id">ID: {{ userInfo.userId }}</div>
        </div>
        
        <div class="menu-divider"></div>
        
        <div class="menu-item" @click.stop="openProfileCenter">
          <i class="fas fa-user"></i>
          <span>个人中心</span>
        </div>

        <div class="menu-divider"></div>
        
        <div class="menu-item logout" @click="handleLogout">
          <i class="fas fa-sign-out-alt"></i>
          <span>退出登录</span>
        </div>
      </div>
    </div>

    <!-- 个人中心模态框 -->
    <div v-if="showProfileModal" class="modal-overlay" @click="closeProfileModal">
      <div class="modal-content profile-modal" @click.stop>
        <div class="modal-header">
          <h3>个人中心</h3>
          <button @click="closeProfileModal" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <div class="profile-info">
            <div class="info-row">
              <label>用户ID：</label>
              <span>{{ userInfo.userId }}</span>
            </div>
            <div class="info-row">
              <label>姓名：</label>
              <span>{{ userInfo.username }}</span>
            </div>
            <div class="info-row">
              <label>年龄：</label>
              <span>{{ userInfo.age || '未设置' }}</span>
            </div>
            <div class="info-row">
              <label>性别：</label>
              <span>{{ userInfo.gender || '未设置' }}</span>
            </div>
            <div class="info-row">
              <label>手机号：</label>
              <span>{{ userInfo.phone || '未设置' }}</span>
            </div>
            <div class="info-row">
              <label>身份证号：</label>
              <span>{{ maskIdCard(userInfo.idCard) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 头像选择模态框 -->
    <div v-if="showAvatarModal" class="modal-overlay" @click="closeAvatarModal">
      <div class="modal-content avatar-modal" @click.stop>
        <div class="modal-header">
          <h3>选择头像</h3>
          <button @click="closeAvatarModal" class="close-btn">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <!-- 本地上传选项 -->
          <div class="upload-section">
            <h4>从本地上传</h4>
            <div class="upload-area" @click="triggerFileInput">
              <div class="upload-placeholder">
                <i class="fas fa-cloud-upload-alt"></i>
                <span>点击选择图片文件</span>
                <p>支持 JPG、PNG、GIF 格式，大小不超过 2MB</p>
              </div>
            </div>
            <input
              type="file"
              ref="avatarFileInput"
              @change="handleFileUpload"
              accept="image/*"
              style="display: none;"
            >
          </div>

          <div class="section-divider">
            <span>或选择预设头像</span>
          </div>

          <div class="avatar-grid">
            <div
              v-for="(avatar, index) in avatarOptions"
              :key="index"
              class="avatar-option"
              :class="{ 'selected': currentAvatar === avatar }"
              @click="selectAvatar(avatar)"
            >
              <img :src="avatar" :alt="`头像 ${index + 1}`">
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="closeAvatarModal" class="btn btn-secondary">取消</button>
          <button @click="saveAvatar" class="btn btn-primary">确定</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { localStorageUtils } from '@/api/ruoyiAPI.js'

export default {
  name: 'UserProfile',
  data() {
    return {
      showDropdown: false,
      showProfileModal: false,
      showAvatarModal: false,
      selectedAvatar: null,
      
      // 预设头像选项（使用在线头像服务）
      avatarOptions: [
        'https://randomuser.me/api/portraits/men/1.jpg',
        'https://randomuser.me/api/portraits/women/1.jpg',
        'https://randomuser.me/api/portraits/men/2.jpg',
        'https://randomuser.me/api/portraits/women/2.jpg',
        'https://randomuser.me/api/portraits/men/3.jpg',
        'https://randomuser.me/api/portraits/women/3.jpg',
        'https://randomuser.me/api/portraits/men/4.jpg',
        'https://randomuser.me/api/portraits/women/4.jpg',
        'https://randomuser.me/api/portraits/men/5.jpg',
        'https://randomuser.me/api/portraits/women/5.jpg',
        'https://randomuser.me/api/portraits/men/6.jpg',
        'https://randomuser.me/api/portraits/women/6.jpg'
      ]
    }
  },
  
  computed: {
    userInfo() {
      // 使用本地存储获取用户信息
      const userStr = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
      const user = userStr ? JSON.parse(userStr) : {};

      // 确保必要字段存在
      return {
        userId: user.userId || '',
        username: user.username || '未知用户',
        age: user.age || 0,
        gender: user.gender || '',
        phone: user.phone || '',
        email: user.email || '',
        avatar: user.avatar || '',
        createdAt: user.createdAt || '',
        ...user
      };
    },
    
    currentAvatar() {
      // 从本地存储获取用户选择的头像，如果没有则使用默认头像
      const savedAvatar = localStorage.getItem(`avatar_${this.userInfo.userId}`)
      return savedAvatar || this.avatarOptions[0] || 'https://randomuser.me/api/portraits/lego/1.jpg'
    }
  },
  
  methods: {
    toggleDropdown() {
      this.showDropdown = !this.showDropdown
    },
    
    closeDropdown() {
      this.showDropdown = false
    },
    
    openProfileCenter() {
      console.log('🔗 点击个人中心，准备跳转到 /profile');

      // 跳转到个人中心页面
      this.$router.push('/profile').then(() => {
        console.log('✅ 成功跳转到个人中心页面');
      }).catch(error => {
        console.error('❌ 跳转失败:', error);
      });

      this.closeDropdown();
    },
    
    closeProfileModal() {
      this.showProfileModal = false
    },
    
    openAvatarSelector() {
      this.showAvatarModal = true
      this.selectedAvatar = this.currentAvatar
      this.closeDropdown()
    },
    
    closeAvatarModal() {
      this.showAvatarModal = false
      this.selectedAvatar = null
    },
    
    selectAvatar(avatar) {
      this.selectedAvatar = avatar
    },

    // 触发文件选择
    triggerFileInput() {
      this.$refs.avatarFileInput.click()
    },

    // 处理文件上传
    handleFileUpload(event) {
      const file = event.target.files[0]
      if (!file) return

      // 验证文件类型
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      if (!allowedTypes.includes(file.type)) {
        alert('请选择 JPG、PNG 或 GIF 格式的图片')
        return
      }

      // 验证文件大小 (2MB)
      const maxSize = 2 * 1024 * 1024
      if (file.size > maxSize) {
        alert('图片大小不能超过 2MB')
        return
      }

      // 读取文件并转换为Base64
      const reader = new FileReader()
      reader.onload = (e) => {
        this.selectedAvatar = e.target.result
        console.log('头像文件读取成功')
      }
      reader.onerror = () => {
        console.error('头像文件读取失败')
        alert('头像文件读取失败，请重试')
      }
      reader.readAsDataURL(file)
    },

    saveAvatar() {
      if (this.selectedAvatar) {
        // 保存用户选择的头像到本地存储
        localStorage.setItem(`avatar_${this.userInfo.userId}`, this.selectedAvatar)
        this.closeAvatarModal()

        // 清空文件输入框
        if (this.$refs.avatarFileInput) {
          this.$refs.avatarFileInput.value = ''
        }
      }
    },
    
    maskIdCard(idCard) {
      if (!idCard) return '未设置'
      // 身份证号脱敏显示
      return idCard.replace(/^(.{6}).*(.{4})$/, '$1********$2')
    },
    
    async handleLogout() {
      if (confirm('确定要退出登录吗？')) {
        try {
          // 使用统一的清除方法
          localStorageUtils.clearUser();
          
          // 跳转到登录页面
          this.$router.push('/login')
          
          alert('已成功退出登录')
        } catch (error) {
          console.error('退出登录失败:', error)
          alert('退出登录失败，请稍后重试')
        }
      }
      this.closeDropdown()
    }
  },
  
  // 点击外部关闭下拉菜单的指令
  directives: {
    'click-outside': {
      bind(el, binding) {
        el.clickOutsideEvent = function(event) {
          if (!(el === event.target || el.contains(event.target))) {
            binding.value()
          }
        }
        document.addEventListener('click', el.clickOutsideEvent)
      },
      unbind(el) {
        document.removeEventListener('click', el.clickOutsideEvent)
      }
    }
  }
}
</script>

<style scoped>
.user-profile {
  position: relative;
}

.profile-dropdown {
  cursor: pointer;
  position: relative;
}

.avatar-container {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border-radius: 20px;
  transition: background-color 0.3s ease;
}

.avatar-container:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.dropdown-icon {
  color: #fff;
  font-size: 12px;
  transition: transform 0.3s ease;
}

.dropdown-icon.rotated {
  transform: rotate(180deg);
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  min-width: 200px;
  z-index: 1000;
  overflow: hidden;
  margin-top: 8px;
}

.user-info {
  padding: 16px;
  background: #f8f9fa;
}

.user-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.user-id {
  font-size: 12px;
  color: #666;
}

.menu-divider {
  height: 1px;
  background: #eee;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.menu-item:hover {
  background-color: #f8f9fa;
}

.menu-item.logout {
  color: #dc3545;
}

.menu-item.logout:hover {
  background-color: #fff5f5;
}

.menu-item i {
  width: 16px;
  text-align: center;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #999;
  padding: 5px;
}

.close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #eee;
}

/* 个人信息样式 */
.profile-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: flex;
  align-items: center;
}

.info-row label {
  font-weight: 600;
  color: #333;
  min-width: 80px;
}

.info-row span {
  color: #666;
}

/* 上传区域样式 */
.upload-section {
  margin-bottom: 20px;
}

.upload-section h4 {
  margin: 0 0 15px 0;
  color: #333;
  font-size: 16px;
}

.upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: #007bff;
  background-color: #f8f9ff;
}

.upload-placeholder i {
  font-size: 32px;
  color: #007bff;
  margin-bottom: 10px;
  display: block;
}

.upload-placeholder span {
  display: block;
  font-size: 16px;
  color: #333;
  margin-bottom: 5px;
}

.upload-placeholder p {
  margin: 0;
  font-size: 12px;
  color: #666;
}

.section-divider {
  text-align: center;
  margin: 20px 0;
  position: relative;
}

.section-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #eee;
}

.section-divider span {
  background: white;
  padding: 0 15px;
  color: #666;
  font-size: 14px;
  position: relative;
}

/* 头像选择样式 */
.avatar-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 16px;
  padding: 10px 0;
}

.avatar-option {
  cursor: pointer;
  border-radius: 8px;
  overflow: hidden;
  border: 3px solid transparent;
  transition: all 0.3s ease;
}

.avatar-option:hover {
  border-color: #007bff;
  transform: scale(1.05);
}

.avatar-option.selected {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.avatar-option img {
  width: 100%;
  height: 80px;
  object-fit: cover;
  display: block;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62;
}
</style>
