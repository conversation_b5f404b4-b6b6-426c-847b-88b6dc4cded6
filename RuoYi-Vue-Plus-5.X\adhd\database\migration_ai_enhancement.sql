-- =====================================================
-- ADHD系统AI增强功能数据库迁移脚本
-- 版本: v2.0
-- 日期: 2025-07-14
-- 说明: 为现有表添加AI分析相关字段，保持向后兼容
-- =====================================================

-- 1. 扩展量表测试记录表 (scale_test_records)
-- 添加AI分析相关字段
ALTER TABLE scale_test_records 
ADD COLUMN IF NOT EXISTS analysis_method VARCHAR(50) DEFAULT 'traditional' COMMENT '分析方法: ai_analysis, traditional',
ADD COLUMN IF NOT EXISTS ai_confidence DECIMAL(3,2) NULL COMMENT 'AI分析置信度 (0.00-1.00)',
ADD COLUMN IF NOT EXISTS severity_level VARCHAR(20) NULL COMMENT '严重程度: 正常, 轻度, 中度, 重度',
ADD COLUMN IF NOT EXISTS attention_score INT NULL COMMENT '注意力缺陷分数',
ADD COLUMN IF NOT EXISTS hyperactivity_score INT NULL COMMENT '多动/冲动分数',
ADD COLUMN IF NOT EXISTS opposition_score INT NULL COMMENT '对立违抗分数',
ADD COLUMN IF NOT EXISTS analysis_details JSON NULL COMMENT 'AI分析详细结果(JSON格式)',
ADD COLUMN IF NOT EXISTS model_used VARCHAR(100) NULL COMMENT '使用的AI模型名称',
ADD COLUMN IF NOT EXISTS analysis_timestamp TIMESTAMP NULL COMMENT 'AI分析时间戳';

-- 添加索引优化查询性能
CREATE INDEX IF NOT EXISTS idx_scale_analysis_method ON scale_test_records(analysis_method);
CREATE INDEX IF NOT EXISTS idx_scale_severity_level ON scale_test_records(severity_level);
CREATE INDEX IF NOT EXISTS idx_scale_ai_confidence ON scale_test_records(ai_confidence);

-- 2. 创建AI模型配置表
CREATE TABLE IF NOT EXISTS ai_model_configs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    model_name VARCHAR(100) NOT NULL COMMENT '模型名称',
    model_version VARCHAR(50) NOT NULL COMMENT '模型版本',
    model_type ENUM('llm', 'cv', 'ml') NOT NULL COMMENT '模型类型',
    endpoint_url VARCHAR(255) NOT NULL COMMENT '模型服务端点',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    config_json JSON NULL COMMENT '模型配置参数',
    performance_metrics JSON NULL COMMENT '性能指标',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_model_name_version (model_name, model_version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI模型配置表';

-- 插入默认AI模型配置
INSERT INTO ai_model_configs (model_name, model_version, model_type, endpoint_url, config_json) VALUES
('qwen2.5:14b', '14b', 'llm', 'http://localhost:11434', '{"temperature": 0.1, "top_p": 0.9, "max_tokens": 2048}'),
('qwen2.5:7b', '7b', 'llm', 'http://localhost:11434', '{"temperature": 0.1, "top_p": 0.9, "max_tokens": 1024}'),
('llama3.1:8b', '8b', 'llm', 'http://localhost:11434', '{"temperature": 0.1, "top_p": 0.9, "max_tokens": 2048}')
ON DUPLICATE KEY UPDATE 
    endpoint_url = VALUES(endpoint_url),
    config_json = VALUES(config_json),
    updated_at = CURRENT_TIMESTAMP;

-- 3. 创建AI分析日志表
CREATE TABLE IF NOT EXISTS ai_analysis_logs (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
    analysis_type ENUM('scale', 'attention', 'emotion', 'rehabilitation') NOT NULL COMMENT '分析类型',
    model_name VARCHAR(100) NOT NULL COMMENT '使用的模型',
    input_data JSON NOT NULL COMMENT '输入数据',
    output_data JSON NULL COMMENT '输出结果',
    processing_time INT NULL COMMENT '处理时间(毫秒)',
    success TINYINT(1) DEFAULT 1 COMMENT '是否成功',
    error_message TEXT NULL COMMENT '错误信息',
    confidence_score DECIMAL(3,2) NULL COMMENT '置信度分数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_user_analysis (user_id, analysis_type),
    INDEX idx_model_performance (model_name, success, processing_time),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分析日志表';

-- 4. 扩展用户信息表 (user_info)
-- 添加AI偏好设置
ALTER TABLE user_info 
ADD COLUMN IF NOT EXISTS ai_preferences JSON NULL COMMENT 'AI功能偏好设置',
ADD COLUMN IF NOT EXISTS consent_ai_analysis TINYINT(1) DEFAULT 1 COMMENT '是否同意AI分析',
ADD COLUMN IF NOT EXISTS preferred_analysis_method VARCHAR(50) DEFAULT 'ai_analysis' COMMENT '首选分析方法';

-- 5. 创建量表模板增强表
CREATE TABLE IF NOT EXISTS scale_templates_enhanced (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    scale_type VARCHAR(50) NOT NULL COMMENT '量表类型',
    scale_name VARCHAR(200) NOT NULL COMMENT '量表名称',
    version VARCHAR(20) NOT NULL COMMENT '量表版本',
    questions_json JSON NOT NULL COMMENT '问题配置(JSON)',
    scoring_rules JSON NOT NULL COMMENT '评分规则(JSON)',
    ai_prompt_template TEXT NULL COMMENT 'AI分析提示词模板',
    interpretation_rules JSON NULL COMMENT '结果解释规则',
    target_age_min INT NULL COMMENT '适用最小年龄',
    target_age_max INT NULL COMMENT '适用最大年龄',
    is_active TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_scale_type_version (scale_type, version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='增强版量表模板表';

-- 6. 创建AI分析质量评估表
CREATE TABLE IF NOT EXISTS ai_analysis_quality (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    analysis_log_id BIGINT NOT NULL COMMENT '分析日志ID',
    user_feedback_score INT NULL COMMENT '用户反馈评分(1-5)',
    expert_review_score INT NULL COMMENT '专家评审评分(1-5)',
    accuracy_metrics JSON NULL COMMENT '准确性指标',
    feedback_comments TEXT NULL COMMENT '反馈意见',
    reviewed_by VARCHAR(100) NULL COMMENT '评审人',
    reviewed_at TIMESTAMP NULL COMMENT '评审时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    FOREIGN KEY (analysis_log_id) REFERENCES ai_analysis_logs(id) ON DELETE CASCADE,
    INDEX idx_quality_scores (user_feedback_score, expert_review_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='AI分析质量评估表';

-- 7. 更新现有数据的默认值
UPDATE scale_test_records 
SET analysis_method = 'traditional',
    severity_level = CASE 
        WHEN risk_level = '高风险' THEN '重度'
        WHEN risk_level = '中等风险' THEN '中度'
        WHEN risk_level = '低风险' THEN '轻度'
        ELSE '正常'
    END
WHERE analysis_method IS NULL;

-- 8. 创建视图简化查询
CREATE OR REPLACE VIEW v_enhanced_scale_results AS
SELECT 
    sr.id,
    sr.user_id,
    sr.username,
    sr.scale_name,
    sr.total_score,
    sr.attention_score,
    sr.hyperactivity_score,
    sr.analysis_method,
    sr.ai_confidence,
    sr.severity_level,
    sr.risk_level,
    sr.model_used,
    sr.created_at,
    ui.age,
    ui.gender,
    CASE 
        WHEN sr.analysis_method = 'ai_analysis' THEN '🤖 AI智能分析'
        ELSE '📊 传统分析'
    END as analysis_method_display
FROM scale_test_records sr
LEFT JOIN user_info ui ON sr.user_id = ui.user_id
ORDER BY sr.created_at DESC;

-- 9. 添加触发器记录分析历史
DELIMITER //
CREATE TRIGGER IF NOT EXISTS tr_scale_analysis_history 
AFTER INSERT ON scale_test_records
FOR EACH ROW
BEGIN
    IF NEW.analysis_method = 'ai_analysis' THEN
        INSERT INTO ai_analysis_logs (
            user_id, 
            analysis_type, 
            model_name, 
            confidence_score,
            success
        ) VALUES (
            NEW.user_id,
            'scale',
            COALESCE(NEW.model_used, 'unknown'),
            NEW.ai_confidence,
            1
        );
    END IF;
END//
DELIMITER ;

-- 10. 创建性能监控视图
CREATE OR REPLACE VIEW v_ai_performance_stats AS
SELECT 
    model_name,
    analysis_type,
    COUNT(*) as total_analyses,
    AVG(confidence_score) as avg_confidence,
    AVG(processing_time) as avg_processing_time,
    SUM(CASE WHEN success = 1 THEN 1 ELSE 0 END) / COUNT(*) * 100 as success_rate,
    DATE(created_at) as analysis_date
FROM ai_analysis_logs
GROUP BY model_name, analysis_type, DATE(created_at)
ORDER BY analysis_date DESC, model_name;

-- =====================================================
-- 迁移完成提示
-- =====================================================
SELECT 
    '✅ AI增强功能数据库迁移完成!' as status,
    '新增表: ai_model_configs, ai_analysis_logs, scale_templates_enhanced, ai_analysis_quality' as new_tables,
    '扩展表: scale_test_records, user_info' as enhanced_tables,
    '新增视图: v_enhanced_scale_results, v_ai_performance_stats' as new_views;
