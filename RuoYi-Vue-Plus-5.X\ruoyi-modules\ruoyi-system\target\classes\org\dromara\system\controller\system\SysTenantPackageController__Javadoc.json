{"doc": " 租户套餐管理\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询租户套餐列表\n"}, {"name": "selectList", "paramTypes": [], "doc": " 查询租户套餐下拉选列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出租户套餐列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取租户套餐详细信息\n\n @param packageId 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 新增租户套餐\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 修改租户套餐\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 状态修改\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除租户套餐\n\n @param packageIds 主键串\n"}], "constructors": []}