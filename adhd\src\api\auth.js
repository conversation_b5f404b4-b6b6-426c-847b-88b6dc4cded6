import request from '@/utils/request'

// 用户登录
export function login(data) {
  return request({
    url: '/auth/login',
    method: 'post',
    data: {
      username: data.username,
      password: data.password
    }
  })
}

// 用户注册
export function register(data) {
  return request({
    url: '/auth/register',
    method: 'post',
    data: {
      username: data.username,
      email: data.email,
      password: data.password,
      age: data.age,
      gender: data.gender
    }
  })
}

// 忘记密码
export function forgotPassword(email) {
  return request({
    url: '/auth/forgot-password',
    method: 'post',
    data: { email }
  })
}

// 重置密码
export function resetPassword(data) {
  return request({
    url: '/auth/reset-password',
    method: 'post',
    data: {
      token: data.token,
      password: data.password
    }
  })
}

// 验证邮箱
export function verifyEmail(token) {
  return request({
    url: '/auth/verify-email',
    method: 'post',
    data: { token }
  })
}

// 获取用户信息
export function getUserInfo() {
  return request({
    url: '/auth/user-info',
    method: 'get'
  })
}

// 更新用户信息
export function updateUserInfo(data) {
  return request({
    url: '/auth/update-profile',
    method: 'put',
    data
  })
}

// 修改密码
export function changePassword(data) {
  return request({
    url: '/auth/change-password',
    method: 'put',
    data: {
      oldPassword: data.oldPassword,
      newPassword: data.newPassword
    }
  })
}

// 退出登录
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'post'
  })
}

// 刷新token
export function refreshToken() {
  return request({
    url: '/auth/refresh-token',
    method: 'post'
  })
}

// 检查用户名是否可用
export function checkUsername(username) {
  return request({
    url: '/auth/check-username',
    method: 'get',
    params: { username }
  })
}

// 检查邮箱是否可用
export function checkEmail(email) {
  return request({
    url: '/auth/check-email',
    method: 'get',
    params: { email }
  })
}

// 社交登录 - 微信
export function wechatLogin(code) {
  return request({
    url: '/auth/wechat-login',
    method: 'post',
    data: { code }
  })
}

// 社交登录 - QQ
export function qqLogin(code) {
  return request({
    url: '/auth/qq-login',
    method: 'post',
    data: { code }
  })
}

// 默认导出所有API
export default {
  login,
  register,
  forgotPassword,
  resetPassword,
  verifyEmail,
  getUserInfo,
  updateUserInfo,
  changePassword,
  logout,
  refreshToken,
  checkUsername,
  checkEmail,
  wechatLogin,
  qqLogin
}
