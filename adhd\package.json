{"name": "adhd", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.7.2", "@stomp/stompjs": "^7.0.0", "axios": "^1.10.0", "chart.js": "^3.9.1", "core-js": "^3.8.3", "cors": "^2.8.5", "element-plus": "^2.10.4", "express": "^5.1.0", "sockjs-client": "^1.6.1", "vue": "^3.2.13", "vue-router": "^4.5.1"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}