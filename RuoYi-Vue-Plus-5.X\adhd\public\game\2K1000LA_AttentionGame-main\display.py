# -*- coding: utf-8 -*-
from pyqtgraph.Qt import QtGui, QtCore
import threading
import pyqtgraph as pg
import serial
import time
from collections import deque

# 使用deque作为线程安全的缓冲区，设置最大长度防止内存溢出
data_buffer = deque(maxlen=500)
data2_buffer = deque(maxlen=20)
data3_buffer = deque(maxlen=20)
lock = threading.Lock()  # 添加线程锁

class EEGThread(threading.Thread):
    def __init__(self):
        super().__init__()
        self.com = "/dev/ttyUSB0"  #此处修改端口号
        self.bps = 57600    #波特率
        self._running = True

    def stop(self):
        self._running = False

    def check_threshold(self, buffer, threshold, count_threshold):
        return sum(1 for x in buffer if x > threshold) >= count_threshold

    def run(self):
        global data_buffer, data2_buffer, data3_buffer
        try:
            ser = serial.Serial(self.com, self.bps)
            print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} 脑电波设备配对中...")
            
            # 等待有效数据头
            while self._running:
                header = ser.read(3)
                if header == b'\xaa\xaa\x04':
                    break

            print(f"{time.strftime('%Y-%m-%d %H:%M:%S')} 配对成功")
            ser.read(5)  # 跳过初始数据

            while self._running:
                packet = ser.read(8)
                # 处理原始波形数据
                if packet[0:3] == b'\xaa\xaa\x20':
                    # 解析32字节长包
                    packet += ser.read(28)
                    with lock:
                        # 更新专注值和放松值
                        if packet[4] == 0:  # 信号质量正常
                            data2_buffer.append(packet[32])  # 专注值
                            data3_buffer.append(packet[34])  # 放松值
                # 处理4字节短包
                elif packet[0:3] == b'\xaa\xaa\x04' and packet[3] == 0x80:
                    raw_data = (packet[5] << 8) | packet[6]
                    if raw_data > 32768:
                        raw_data -= 65536
                    with lock:
                        data_buffer.append(raw_data)

        except Exception as e:
            print(f"发生错误: {str(e)}")
        finally:
            if ser.is_open:
                ser.close()

class MainWindow(QtGui.QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('脑电波实时监测')
        self.resize(1000, 600)
        
        # 创建绘图部件
        self.plot_widget = pg.GraphicsLayoutWidget()
        self.setCentralWidget(self.plot_widget)
        
        # 原始波形图
        self.raw_plot = self.plot_widget.addPlot(title="原始脑电信号")
        self.raw_curve = self.raw_plot.plot(pen='y')
        self.raw_plot.setYRange(-1000, 1000)
        
        # 专注/放松值图
        self.attention_plot = self.plot_widget.addPlot(title="专注值（蓝） vs 放松值（绿）")
        self.attention_curve = self.attention_plot.plot(pen='b')
        self.meditation_curve = self.attention_plot.plot(pen='g')
        self.attention_plot.setYRange(0, 100)
        
        # 设置定时器刷新界面
        self.timer = QtCore.QTimer()
        self.timer.timeout.connect(self.update_plots)
        self.timer.start(50)  # 20Hz刷新率

    def update_plots(self):
        with lock:  # 线程安全访问数据
            # 更新原始信号波形
            if data_buffer:
                self.raw_curve.setData(list(data_buffer))
            
            # 更新专注值和放松值
            if data2_buffer and data3_buffer:
                x = list(range(len(data2_buffer)))
                self.attention_curve.setData(x, list(data2_buffer))
                self.meditation_curve.setData(x, list(data3_buffer))

if __name__ == '__main__':
    import sys
    app = QtGui.QApplication(sys.argv)
    
    # 创建并启动线程
    eeg_thread = EEGThread()
    eeg_thread.daemon = True  # 设置为守护线程
    eeg_thread.start()
    
    # 显示主窗口
    main_win = MainWindow()
    main_win.show()
    
    # 安全退出处理
    def shutdown():
        eeg_thread.stop()
        eeg_thread.join()
        app.quit()
    app.aboutToQuit.connect(shutdown)
    
    sys.exit(app.exec_())