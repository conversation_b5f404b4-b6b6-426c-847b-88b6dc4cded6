/**
 * 数据同步工具
 * 用于在不同组件之间同步检测数据
 */

import dataCollectionService from '@/services/dataCollectionService.js'
import { localStorageUtils } from '@/api/ruoyiAPI.js'
import { request, formatters } from '@/api/axios.js'

class DataSync {
  constructor() {
    this.listeners = new Map();
    this.eventBus = new EventTarget();
  }

  /**
   * 订阅数据更新事件
   */
  subscribe(eventType, callback) {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set());
    }
    this.listeners.get(eventType).add(callback);

    // 添加事件监听器
    this.eventBus.addEventListener(eventType, callback);
  }

  /**
   * 取消订阅
   */
  unsubscribe(eventType, callback) {
    if (this.listeners.has(eventType)) {
      this.listeners.get(eventType).delete(callback);
    }
    this.eventBus.removeEventListener(eventType, callback);
  }

  /**
   * 发布数据更新事件
   */
  publish(eventType, data) {
    const event = new CustomEvent(eventType, { detail: data });
    this.eventBus.dispatchEvent(event);
  }

  /**
   * 通知检测数据更新
   */
  notifyDetectionUpdate(detectionData) {
    this.publish('detection-updated', detectionData);
  }

  /**
   * 通知会话开始
   */
  notifySessionStart(sessionData) {
    this.publish('session-started', sessionData);
  }

  /**
   * 通知会话结束
   */
  async notifySessionEnd(sessionData) {
    this.publish('session-ended', sessionData);

    // 发送会话数据到RuoYi后端
    await this.sendSessionDataToRuoYi(sessionData);
  }

  /**
   * 通知统计数据更新
   */
  notifyStatisticsUpdate() {
    const statistics = dataCollectionService.getStatistics();
    this.publish('statistics-updated', statistics);
  }

  /**
   * 获取实时统计数据
   */
  getRealTimeStatistics() {
    const allData = dataCollectionService.getAllData();
    if (!allData) return null;

    const currentSession = dataCollectionService.getCurrentSession();
    
    return {
      // 全局统计
      global: allData.statistics,
      
      // 当前会话统计
      currentSession: currentSession ? {
        id: currentSession.id,
        duration: this.calculateSessionDuration(currentSession.startTime),
        detections: currentSession.detections.length,
        avgAttention: currentSession.statistics.avgAttentionScore
      } : null,
      
      // 今日统计
      today: this.getTodayStatistics(allData),
      
      // 本周统计
      thisWeek: this.getThisWeekStatistics(allData)
    };
  }

  /**
   * 计算会话持续时间
   */
  calculateSessionDuration(startTime) {
    const start = new Date(startTime);
    const now = new Date();
    return Math.floor((now - start) / 1000 / 60); // 分钟
  }

  /**
   * 获取今日统计
   */
  getTodayStatistics(allData) {
    const today = new Date().toISOString().split('T')[0];
    const todayData = allData.dailyData[today];
    
    return todayData || {
      sessions: 0,
      duration: 0,
      detections: 0,
      avgAttention: 0
    };
  }

  /**
   * 获取本周统计
   */
  getThisWeekStatistics(allData) {
    const now = new Date();
    const weekStart = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000);
    
    let weekStats = {
      sessions: 0,
      duration: 0,
      detections: 0,
      avgAttention: 0
    };

    // 统计本周数据
    Object.keys(allData.dailyData).forEach(dateKey => {
      const date = new Date(dateKey);
      if (date >= weekStart) {
        const dayData = allData.dailyData[dateKey];
        weekStats.sessions += dayData.sessions;
        weekStats.duration += dayData.duration;
        weekStats.detections += dayData.detections;
      }
    });

    // 计算平均注意力
    if (weekStats.sessions > 0) {
      const recentSessions = allData.sessions
        .filter(session => new Date(session.startTime) >= weekStart)
        .slice(-10);
      
      if (recentSessions.length > 0) {
        const totalAttention = recentSessions.reduce((sum, session) => 
          sum + (session.statistics.avgAttentionScore || 0), 0);
        weekStats.avgAttention = Math.round(totalAttention / recentSessions.length);
      }
    }

    return weekStats;
  }

  /**
   * 获取趋势数据
   */
  getTrendData(range = 'week') {
    const allData = dataCollectionService.getAllData();
    if (!allData || !allData.sessions) return null;

    switch (range) {
      case 'week':
        return this.getWeeklyTrend(allData);
      case 'month':
        return this.getMonthlyTrend(allData);
      case 'year':
        return this.getYearlyTrend(allData);
      default:
        return this.getWeeklyTrend(allData);
    }
  }

  /**
   * 获取周趋势
   */
  getWeeklyTrend(allData) {
    const trend = [];
    const now = new Date();
    
    for (let i = 6; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateKey = date.toISOString().split('T')[0];
      const dayData = allData.dailyData[dateKey];
      
      trend.push({
        date: dateKey,
        label: this.formatDateLabel(date, 'day'),
        sessions: dayData ? dayData.sessions : 0,
        duration: dayData ? dayData.duration : 0,
        avgAttention: dayData ? dayData.avgAttention : 0
      });
    }
    
    return trend;
  }

  /**
   * 获取月趋势
   */
  getMonthlyTrend(allData) {
    const trend = [];
    const now = new Date();
    
    for (let i = 3; i >= 0; i--) {
      const weekStart = new Date(now.getTime() - (i * 7 + 6) * 24 * 60 * 60 * 1000);
      const weekEnd = new Date(now.getTime() - i * 7 * 24 * 60 * 60 * 1000);
      
      let weekStats = { sessions: 0, duration: 0, avgAttention: 0 };
      let attentionSum = 0, attentionCount = 0;
      
      Object.keys(allData.dailyData).forEach(dateKey => {
        const date = new Date(dateKey);
        if (date >= weekStart && date <= weekEnd) {
          const dayData = allData.dailyData[dateKey];
          weekStats.sessions += dayData.sessions;
          weekStats.duration += dayData.duration;
          if (dayData.avgAttention > 0) {
            attentionSum += dayData.avgAttention;
            attentionCount++;
          }
        }
      });
      
      weekStats.avgAttention = attentionCount > 0 ? Math.round(attentionSum / attentionCount) : 0;
      
      trend.push({
        period: `第${4-i}周`,
        ...weekStats
      });
    }
    
    return trend;
  }

  /**
   * 获取年趋势
   */
  getYearlyTrend(allData) {
    const trend = [];
    const now = new Date();
    const currentYear = now.getFullYear();
    
    for (let month = 0; month < 12; month++) {
      const monthKey = `${currentYear}-${String(month + 1).padStart(2, '0')}`;
      const monthData = allData.monthlyData[monthKey];
      
      trend.push({
        month: month + 1,
        label: `${month + 1}月`,
        sessions: monthData ? monthData.sessions : 0,
        duration: monthData ? monthData.duration : 0,
        avgAttention: monthData ? monthData.avgAttention : 0
      });
    }
    
    return trend;
  }

  /**
   * 格式化日期标签
   */
  formatDateLabel(date, type) {
    switch (type) {
      case 'day': {
        const days = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        return days[date.getDay()];
      }
      case 'month':
        return `${date.getMonth() + 1}月`;
      default:
        return date.toLocaleDateString();
    }
  }

  /**
   * 发送会话数据到RuoYi后端
   */
  async sendSessionDataToRuoYi(sessionData) {
    try {
      console.log('📤 准备发送智能筛查会话数据到RuoYi后端...');
      console.log('📊 会话数据:', sessionData);

      // 🔍 详细调试：检查数据结构
      console.log('🔍 调试 - sessionData.statistics:', sessionData.statistics);
      console.log('🔍 调试 - sessionData.statistics?.attentionLevels:', sessionData.statistics?.attentionLevels);
      console.log('🔍 调试 - attentionLevels类型:', typeof sessionData.statistics?.attentionLevels);
      console.log('🔍 调试 - attentionLevels键名:', Object.keys(sessionData.statistics?.attentionLevels || {}));
      console.log('🔍 调试 - sessionData.startTime:', sessionData.startTime);
      console.log('🔍 调试 - sessionData.endTime:', sessionData.endTime);
      console.log('🔍 调试 - sessionData.duration:', sessionData.duration);

      // 获取当前用户信息
      const currentUser = localStorageUtils.getUser();
      if (!currentUser) {
        console.warn('⚠️ 未找到当前用户信息，跳过数据发送');
        return;
      }

      // 🔥 数据修复：如果统计数据不正确，从检测数据重新计算
      let finalStatistics = sessionData.statistics;
      if (!finalStatistics || finalStatistics.totalDetections === 0 ||
          Object.values(finalStatistics.attentionLevels || {}).every(count => count === 0)) {
        console.log('⚠️ 检测到统计数据异常，从检测数据重新计算...');
        finalStatistics = this.recalculateStatisticsFromDetections(sessionData);
      }

      // 使用formatters构建发送到RuoYi的检测会话数据，完全匹配detection_sessions表结构
      const detectionSessionData = {
        userId: formatters.ensureString(currentUser.userId || currentUser.username || 'anonymous'),
        username: formatters.ensureString(currentUser.username || 'anonymous'),
        sessionId: formatters.ensureString(sessionData.id || `session_${Date.now()}`),

        // 会话时间信息
        startTime: formatters.formatDateForRuoYi(sessionData.startTime),
        endTime: formatters.formatDateForRuoYi(sessionData.endTime),
        duration: Math.max(1, formatters.ensureNumber(sessionData.duration, 1)), // 确保至少1分钟

        // 必须包含avg_attention_level字段
        avgAttentionLevel: formatters.ensureNumber(finalStatistics?.avgAttentionLevel || finalStatistics?.avgAttentionScore, 0),
        avgAttentionScore: formatters.ensureNumber(finalStatistics?.avgAttentionScore, 0),

        // 注意力等级计数 - 使用修复后的统计数据
        level0Count: formatters.ensureNumber(finalStatistics?.attentionLevels?.[0] || finalStatistics?.attentionLevels?.['0'], 0),
        level1Count: formatters.ensureNumber(finalStatistics?.attentionLevels?.[1] || finalStatistics?.attentionLevels?.['1'], 0),
        level2Count: formatters.ensureNumber(finalStatistics?.attentionLevels?.[2] || finalStatistics?.attentionLevels?.['2'], 0),
        level3Count: formatters.ensureNumber(finalStatistics?.attentionLevels?.[3] || finalStatistics?.attentionLevels?.['3'], 0),

        totalDetections: formatters.ensureNumber(finalStatistics?.totalDetections, 0),
        maxAttentionScore: formatters.ensureNumber(finalStatistics?.maxAttentionScore, 0),
        minAttentionScore: formatters.ensureNumber(finalStatistics?.minAttentionScore, 0)
        // createdAt由后端自动生成
      }

      console.log('📤 格式化后的检测会话数据:', detectionSessionData)

      // 🔍 详细调试：检查格式化后的关键字段
      console.log('🔍 调试 - 格式化后的时间:')
      console.log('  startTime:', detectionSessionData.startTime)
      console.log('  endTime:', detectionSessionData.endTime)
      console.log('  duration:', detectionSessionData.duration)
      console.log('🔍 调试 - 格式化后的等级计数:')
      console.log('  level0Count:', detectionSessionData.level0Count)
      console.log('  level1Count:', detectionSessionData.level1Count)
      console.log('  level2Count:', detectionSessionData.level2Count)
      console.log('  level3Count:', detectionSessionData.level3Count)
      console.log('  totalDetections:', detectionSessionData.totalDetections)

      // 使用Axios发送到RuoYi后端
      const result = await request.post('/system/sessions/sync', detectionSessionData)

      if (result.success) {
        console.log('✅ 智能筛查会话数据发送成功:', result)
      } else {
        console.error('❌ 发送智能筛查会话数据失败:', result.message)
      }

    } catch (error) {
      console.error('❌ 发送智能筛查会话数据异常:', error)
    }
  }

  /**
   * 清除所有监听器
   */
  clearAllListeners() {
    this.listeners.clear();
  }
}

// 创建单例实例
const dataSync = new DataSync();

export default dataSync;
