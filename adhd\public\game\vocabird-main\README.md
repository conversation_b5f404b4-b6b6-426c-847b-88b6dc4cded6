# VocaBird - 单词学习小游戏

VocaBird 是一个专门为 ADHD（注意力缺陷多动障碍）潜在患者设计的单词学习游戏。研究表明，ADHD 患者在一心二用时往往能达到更好的学习效果。通过将趣味性的小鸟飞行游戏与单词学习相结合，玩家可以在轻松愉快的游戏过程中更有效地记忆单词。

## 设计理念

- **一心二用学习法**：基于研究发现，ADHD 患者在进行多任务时，大脑的注意力分配反而会提高学习效率。
- **游戏化学习**：将枯燥的单词记忆转化为有趣的游戏体验。
- **即时反馈**：通过金币收集、单词朗读等机制提供及时的学习反馈。
- **自由节奏**：玩家可以自由控制游戏速度，找到最适合自己的学习节奏。

## 游戏特点

### 基本玩法
- 使用上下方向键控制小鸟飞行
- 使用左右方向键控制游戏速度（50-500范围内）
- 按空格键可以暂停/继续游戏
- 撞击云朵可以学习单词
- 收集金币可以购买不同颜色的小鸟

### 游戏界面
- 左侧：已收集的单词列表
  - 可以删除不想记住的单词
  - 可以一键复制所有收集的单词
- 中间：游戏主界面
  - 显示金币数量、行进距离、游戏速度和出现的单词数量
  - 进度条显示已学习的单词数量（每30个单词一个周期）
- 右侧：小鸟商店
  - 提供多种颜色的小鸟供购买
  - 需要在游戏暂停时才能购买或切换小鸟

### 特殊功能
- 单词朗读：撞击云朵时会自动朗读英文单词
- 学习里程碑：每学习30个单词会显示一次祝贺
- 进度保存：收集的单词会保存在列表中
- 云朵智能：被撞击过的单词云朵不会立即再次出现
- 触感反馈：收集金币时会有振动反馈（需设备支持）

## 操作说明

- ↑：向上飞行
- ↓：向下飞行
- ←：减速
- →：加速
- Space：暂停/继续游戏
- 鼠标点击：
  - 删除收集的单词
  - 购买/切换小鸟（需要在游戏暂停时）
  - 复制所有收集的单词

## 游戏目标

1. 收集金币购买不同颜色的小鸟
2. 撞击云朵学习新的单词
3. 每学习30个单词达成一个里程碑
4. 尽可能收集更多的单词来提高词汇量

## 技术实现

- HTML5 Canvas 用于游戏渲染
- Web Speech API 用于单词朗读
- Clipboard API 用于复制单词
- Vibration API 用于触感反馈
- 响应式设计确保良好的游戏体验

## 开始游戏

1. 点击 "START GAME" 开始游戏
2. 使用方向键控制小鸟
3. 收集金币并学习单词
4. 按空格键暂停游戏，可以购买新的小鸟

祝您玩得开心，学习进步！
