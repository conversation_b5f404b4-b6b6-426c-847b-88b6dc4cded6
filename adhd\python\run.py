#!/usr/bin/env python3
"""
4级注意力检测系统 - 统一启动脚本（规则模型）
简化版本，只包含核心功能
"""

import sys
import subprocess
import argparse
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def run_system_check():
    """运行系统检查"""
    logger.info("🔍 系统检查")
    logger.info("=" * 50)
    
    # 检查Python版本
    logger.info(f"Python版本: {sys.version}")
    
    # 检查核心文件
    core_files = [
        "daydream_detector.py",
        "demo.py", 
        "config.py",
        "requirements.txt"
    ]
    
    missing_files = []
    for file in core_files:
        if not Path(file).exists():
            missing_files.append(file)
        else:
            logger.info(f"✅ {file}")
    
    if missing_files:
        logger.error(f"❌ 缺少文件: {missing_files}")
        return 1
    
    # 检查依赖包
    try:
        import cv2
        import mediapipe
        import numpy
        import PIL
        logger.info("✅ 所有依赖包已安装")
    except ImportError as e:
        logger.error(f"❌ 缺少依赖包: {e}")
        logger.info("请运行: pip install -r requirements.txt")
        return 1
    
    logger.info("🎉 系统检查完成，一切正常！")
    return 0

def run_demo(args):
    """运行演示"""
    import subprocess
    
    cmd = [sys.executable, "demo.py"]
    
    if args.video:
        cmd.extend(["--video", args.video])
    if args.save:
        cmd.append("--save")
    if hasattr(args, 'output') and args.output:
        cmd.extend(["--output", args.output])
    
    return subprocess.call(cmd)

def run_detection(args):
    """运行实时检测 - 使用demo.py"""
    import subprocess
    
    cmd = [sys.executable, "demo.py"]
    
    if args.camera_id is not None:
        cmd.extend(["--camera_id", str(args.camera_id)])
    
    return subprocess.call(cmd)

def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="4级注意力检测系统 - 基于规则的实时检测",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
注意力等级说明:
  0: 高度专注 - 眼神稳定，头部静止
  1: 专注     - 轻微眼动，偶尔调整
  2: 分心     - 频繁眼动，头部转动
  3: 发呆     - 眼神涣散，长时间凝视

使用示例:
  python run.py check                    # 系统检查
  python run.py demo                     # 摄像头演示
  python run.py demo --video test.mp4    # 视频文件演示
  python run.py demo --save              # 保存检测结果
  python run.py detect                   # 实时检测
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 系统检查命令
    check_parser = subparsers.add_parser('check', help='检查系统环境和依赖')
    
    # 演示命令
    demo_parser = subparsers.add_parser('demo', help='运行4级注意力检测演示')
    demo_parser.add_argument('--video', help='视频文件路径')
    demo_parser.add_argument('--save', action='store_true', help='保存检测结果')
    demo_parser.add_argument('--output', help='输出文件路径')
    demo_parser.add_argument('--camera_id', type=int, default=0, help='摄像头ID')
    
    # 实时检测命令
    detect_parser = subparsers.add_parser('detect', help='运行4级注意力实时检测')
    detect_parser.add_argument('--camera_id', type=int, default=0, help='摄像头ID')
    
    return parser

def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    try:
        if args.command == 'check':
            return run_system_check()
        
        elif args.command == 'demo':
            return run_demo(args)
        
        elif args.command == 'detect':
            return run_detection(args)
        
        else:
            logger.error(f"未知命令: {args.command}")
            parser.print_help()
            return 1
            
    except KeyboardInterrupt:
        logger.info("用户中断操作")
        return 0
    except Exception as e:
        logger.error(f"执行错误: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
