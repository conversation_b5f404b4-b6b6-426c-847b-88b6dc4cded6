<template>
  <div class="page-container">
    <!-- 公共头部组件 -->
    <AppHeader
      :user="currentUser"
      current-route="home"
      @search="handleSearch"
      @show-notifications="showNotifications"
      @show-messages="showMessages"
      @show-settings="showSettings"
      @show-user-menu="showUserMenu"
    />

    <!-- 内容包装区 -->
    <div class="content-wrapper">
      <!-- 公共侧边栏组件 -->
      <AppSidebar
        current-route="home"
        :expanded="navExpanded"
        @expand="expandNav"
        @collapse="collapseNav"
      />

      <!-- 主内容区 -->
      <div class="main-area">
        <div class="page-main-container">
          <div class="area-header">
            <h3><i class="fas fa-smile"></i> AI情绪识别分析</h3>
          </div>

          <div class="emotion-content">
            <!-- 左侧：摄像头和控制区域 -->
            <div class="camera-section">
              <div class="camera-container">
                <video ref="emotionVideo" autoplay muted v-show="cameraActive"></video>
                <div class="video-placeholder" v-show="!cameraActive">
                  <i class="fas fa-video-slash"></i>
                  <p>{{ placeholderText }}</p>
                </div>

                <!-- 468特征点画布 -->
                <canvas
                  ref="emotionOverlay"
                  class="emotion-overlay"
                  v-show="cameraActive"
                ></canvas>

                <!-- 特征点控制面板 -->
                <div v-if="cameraActive" class="landmarks-control-panel">
                  <div class="control-item">
                    <span>468点位:</span>
                    <button
                      @click="toggleLandmarksDisplay"
                      :class="{ active: showLandmarks }"
                    >
                      {{ showLandmarks ? '隐藏' : '显示' }}
                    </button>
                  </div>
                  <div v-if="showLandmarks" class="control-item">
                    <span>类型:</span>
                    <button
                      @click="setLandmarkType('all')"
                      :class="{ active: landmarkType === 'all' }"
                    >
                      全部
                    </button>
                    <button
                      @click="setLandmarkType('eyes')"
                      :class="{ active: landmarkType === 'eyes' }"
                    >
                      眼部
                    </button>
                    <button
                      @click="setLandmarkType('face')"
                      :class="{ active: landmarkType === 'face' }"
                    >
                      轮廓
                    </button>
                  </div>
                  <div v-if="showLandmarks && currentLandmarks.length > 0" class="control-item">
                    <span>点数: {{ currentLandmarks.length }}/468</span>
                  </div>

                </div>

                <!-- 连接状态指示器 -->
                <div class="connection-status" v-if="cameraActive">
                  <div class="status-indicator" :class="{ 'connected': webrtcConnected, 'connecting': !webrtcConnected }">
                    <i :class="webrtcConnected ? 'fas fa-wifi' : 'fas fa-spinner fa-spin'"></i>
                    <span>{{ webrtcConnected ? 'AI分析中' : '连接中...' }}</span>
                  </div>
                  <!-- 会话信息 -->
                  <div class="session-info" v-if="sessionId" style="font-size: 12px; color: #666; margin-top: 5px;">
                    会话ID: {{ sessionId }}
                  </div>
                </div>
              </div>

              <div class="camera-controls">
                <button
                  @click="startCamera"
                  class="primary-btn"
                  :disabled="cameraActive || isLoading"
                >
                  <i class="fas fa-play"></i>
                  {{ isLoading ? '启动中...' : (cameraActive ? 'AI检测运行中' : '开始AI检测') }}
                </button>
                <button
                  @click="stopCamera"
                  class="secondary-btn"
                  :disabled="!cameraActive"
                >
                  <i class="fas fa-stop"></i> 停止检测
                </button>
                <button
                  @click="captureSnapshot"
                  class="capture-btn"
                  :disabled="!cameraActive"
                >
                  <i class="fas fa-camera"></i> 立即分析
                </button>
              </div>
            </div>

            <!-- 右侧：结果显示区域 -->
            <div class="results-section">
              <div class="result-card">
                <h4><i class="fas fa-brain"></i> AI注意力分析</h4>
                <div class="emotion-status">
                  <div class="status-item">
                    <div class="status-label">当前状态:</div>
                    <div class="status-value">
                      <span class="status-text">{{ currentEmotion.text }}</span>
                      <span class="status-level" v-if="currentAttentionLevel !== null">
                        (等级: {{ currentAttentionLevel }}/3)
                      </span>
                      <div class="emotion-icon">
                        <i :class="currentEmotion.icon"></i>
                      </div>
                    </div>
                  </div>

                  <div class="status-item">
                    <div class="status-label">注意力评分:</div>
                    <div class="status-value">
                      <div class="progress-container">
                        <div class="progress">
                          <div
                            class="progress-bar"
                            :style="{ width: attentionScore + '%' }"
                          ></div>
                        </div>
                        <span class="progress-text">{{ attentionScore }}%</span>
                      </div>
                    </div>
                  </div>

                  <!-- AI详细评分 -->
                  <div class="detailed-scores">
                    <div class="score-item">
                      <span class="score-name">眼动评分:</span>
                      <div class="score-bar">
                        <div class="bar-fill" :style="{ width: eyeScore + '%' }"></div>
                      </div>
                      <span class="score-num">{{ eyeScore }}%</span>
                    </div>
                    <div class="score-item">
                      <span class="score-name">面部评分:</span>
                      <div class="score-bar">
                        <div class="bar-fill" :style="{ width: faceScore + '%' }"></div>
                      </div>
                      <span class="score-num">{{ faceScore }}%</span>
                    </div>
                    <div class="score-item">
                      <span class="score-name">动作评分:</span>
                      <div class="score-bar">
                        <div class="bar-fill" :style="{ width: motionScore + '%' }"></div>
                      </div>
                      <span class="score-num">{{ motionScore }}%</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="result-card">
                <h4><i class="fas fa-chart-line"></i> 实时趋势</h4>
                <div class="trend-chart">
                  <div class="trend-item" v-for="(point, index) in attentionTrend" :key="index">
                    <div class="trend-bar" :style="{ height: (point.score || point) + '%' }"></div>
                  </div>
                  <div v-if="attentionTrend.length === 0" class="no-trend">
                    开始检测后显示注意力趋势
                  </div>
                </div>
              </div>

              <div class="result-card" v-if="currentSuggestions.length > 0">
                <h4><i class="fas fa-lightbulb"></i> AI建议</h4>
                <div class="suggestions">
                  <div
                    v-for="suggestion in currentSuggestions.slice(0, 3)"
                    :key="suggestion.id"
                    class="suggestion-item"
                  >
                    <i :class="suggestion.icon"></i>
                    <span>{{ suggestion.text }}</span>
                  </div>
                </div>
              </div>

              <!-- 会话统计 -->
              <div class="result-card" v-if="sessionStats.totalDetections > 0">
                <h4><i class="fas fa-chart-bar"></i> 会话统计</h4>
                <div class="session-stats">
                  <div class="stat-item">
                    <div class="stat-label">检测次数:</div>
                    <div class="stat-value">{{ sessionStats.totalDetections }}</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">平均注意力:</div>
                    <div class="stat-value">{{ sessionStats.avgAttentionScore }}%</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">会话时长:</div>
                    <div class="stat-value">{{ sessionStats.sessionDuration }}分钟</div>
                  </div>
                </div>
              </div>

              <!-- AI设置 -->
              <div class="result-card">
                <h4><i class="fas fa-cog"></i> AI设置</h4>
                <div class="ai-settings">
                  <div class="setting-item">
                    <span class="setting-label">声音提醒:</span>
                    <button @click="detectionSettings.soundAlert = !detectionSettings.soundAlert" class="toggle-btn" :class="{ active: detectionSettings.soundAlert }">
                      <i :class="detectionSettings.soundAlert ? 'fas fa-volume-up' : 'fas fa-volume-mute'"></i>
                      {{ detectionSettings.soundAlert ? '已开启' : '已关闭' }}
                    </button>
                  </div>
                  <div class="setting-item">
                    <span class="setting-label">WebRTC状态:</span>
                    <div class="status-badge" :class="{ connected: webrtcConnected }">
                      <i :class="webrtcConnected ? 'fas fa-check-circle' : 'fas fa-times-circle'"></i>
                      {{ webrtcConnected ? '已连接' : '未连接' }}
                    </div>
                  </div>
                  <div class="setting-item">
                    <span class="setting-label">468点位显示:</span>
                    <button @click="toggleLandmarksDisplay" class="toggle-btn" :class="{ active: showLandmarks }">
                      <i :class="showLandmarks ? 'fas fa-eye' : 'fas fa-eye-slash'"></i>
                      {{ showLandmarks ? '已开启' : '已关闭' }}
                    </button>
                  </div>
                  <div class="setting-item">
                    <span class="setting-label">点位类型:</span>
                    <div class="landmark-type-selector">
                      <button
                        @click="setLandmarkType('all')"
                        class="type-btn"
                        :class="{ active: landmarkType === 'all' }"
                      >
                        全部(468)
                      </button>
                      <button
                        @click="setLandmarkType('eyes')"
                        class="type-btn"
                        :class="{ active: landmarkType === 'eyes' }"
                      >
                        眼部
                      </button>
                      <button
                        @click="setLandmarkType('face')"
                        class="type-btn"
                        :class="{ active: landmarkType === 'face' }"
                      >
                        面部轮廓
                      </button>
                    </div>
                  </div>
                  <div class="setting-item">
                    <span class="setting-label">模拟测试:</span>
                    <div style="display: flex; gap: 10px;">
                      <button @click="startMockDetection" class="debug-btn">
                        <i class="fas fa-robot"></i>
                        模拟检测
                      </button>
                    </div>
                  </div>
                </div>
              </div>


            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 公共页脚组件 -->
    <AppFooter />
  </div>
</template>

<script>
import AppHeader from '@/components/common/AppHeader.vue'
import AppSidebar from '@/components/common/AppSidebar.vue'
import AppFooter from '@/components/common/AppFooter.vue'
import { emotionAPI } from '@/api'
import WebRTCAttentionClient from '@/utils/webrtcAttentionClient'
import dataCollectionService from '@/services/dataCollectionService.js'


import dataSync from '@/utils/dataSync.js'

export default {
  name: 'HomePage',
  components: {
    AppHeader,
    AppSidebar,
    AppFooter
  },
  data() {
    return {
      // 用户信息
      currentUser: {
        name: '张同学',
        avatar: 'https://randomuser.me/api/portraits/men/44.jpg'
      },
      
      // 导航状态
      navExpanded: false,
      


      // 摄像头状态（保留兼容性）
      cameraActive: false,
      isLoading: false,
      placeholderText: '点击"开始AI检测"启动AI分析',

      // AI检测结果（从后台服务同步）
      currentEmotion: {
        text: '未检测',
        icon: 'fas fa-meh'
      },
      attentionScore: 0,
      currentAttentionLevel: null,

      // 检测历史
      detectionHistory: [],

      // AI建议列表
      currentSuggestions: [],

      // 注意力趋势数据
      attentionTrend: [],

      // 会话数据
      sessionStartTime: null,
      sessionId: null,

      // WebRTC注意力分析客户端
      webrtcClient: null,
      webrtcConnected: false,

      // WebRTC直连模式
      isWebRTCMode: true,
      webrtcSocket: null,
      attentionLevel: 0.5,
      attentionStatus: '准备中',

      // AI详细评分
      detailedScores: null,
      eyeScore: 0,
      faceScore: 0,
      motionScore: 0,

      // 统计数据
      sessionStats: {
        totalDetections: 0,
        avgAttentionScore: 0,
        sessionDuration: 0,
        startTime: null
      },

      // 数据收集会话
      currentSession: null,
      sessionActive: false,

      // 会话时长更新定时器
      sessionTimer: null,

      // 数据采样相关
      lastSampleTime: 0,
      sampleInterval: 1000, // 每秒采样一次
      tempDetectionData: [], // 临时存储检测数据

      // 连接状态检查定时器
      connectionCheckTimer: null,

      // 模拟检测定时器
      mockDetectionInterval: null,

      // AI检测设置
      detectionSettings: {
        soundAlert: false, // 声音提醒
        interval: 1000 // 检测间隔
      },

      // 468特征点相关
      showLandmarks: false,
      landmarkType: 'all', // 'all', 'eyes', 'face'
      currentLandmarks: [],
      landmarkIndices: {
        // MediaPipe 468点模型的关键索引
        all: Array.from({length: 468}, (_, i) => i), // 0-467所有点
        eyes: [
          // 左眼轮廓
          33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246,
          // 右眼轮廓
          362, 382, 381, 380, 374, 373, 390, 249, 263, 466, 388, 387, 386, 385, 384, 398,
          // 左眼内部关键点（上眼睑）
          27, 28, 29, 30,
          // 右眼内部关键点（上眼睑）
          257, 258, 259, 260
        ],
        face: [
          // 面部轮廓
          10, 338, 297, 332, 284, 251, 389, 356, 454, 323, 361, 288, 397, 365, 379, 378, 400, 377, 152, 148, 176, 149, 150, 136, 172, 58, 132, 93, 234, 127, 162, 21, 54, 103, 67, 109,
          // 眉毛
          70, 63, 105, 66, 107, 55, 65, 52, 53, 46, 285, 295, 282, 283, 276, 293, 334, 296, 336, 285,
          // 鼻子
          1, 2, 5, 4, 6, 19, 94, 125, 141, 235, 31, 228, 229, 230, 231, 232, 233, 244, 245, 122, 6, 202, 214, 234, 93, 132, 58, 172, 136, 150, 149, 176, 148, 152, 377, 400, 378, 379, 365, 397, 288, 361, 323,
          // 嘴部
          61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318, 12, 15, 16, 17, 18, 200, 199, 175, 0, 13, 82, 81, 80, 78
        ]
      },

      // 分析状态
      analysisStatus: {
        message: '',
        type: 'info' // info, success, warning, error
      }
    }
  },
  
  async mounted() {
    // 验证特征点索引配置
    this.validateLandmarkIndices();

    // 初始化导航状态
    if (localStorage.getItem('navExpanded') === 'true') {
      this.navExpanded = true;
    }

    // 加载历史记录
    this.loadDetectionHistory();

    // 初始化WebRTC客户端（保留兼容性）
    try {
      this.initWebRTCClient();
    } catch (error) {
      console.warn('WebRTC客户端初始化失败:', error);
      // 继续运行，不阻塞界面
    }

    // 等待DOM渲染完成
    await this.$nextTick();
  },
  
  beforeUnmount() {
    // 清理会话定时器（如果有的话）
    this.stopSessionTimer();

    // 停止WebRTC连接
    if (this.webrtcClient && this.webrtcClient.stopAnalysis) {
      this.webrtcClient.stopAnalysis();
    }
  },
  
  methods: {
    // 搜索处理
    handleSearch(query) {
      console.log('搜索:', query);
      // 实现搜索逻辑
    },
    
    // 通知相关
    showNotifications() {
      console.log('显示通知');
    },
    
    showMessages() {
      console.log('显示消息');
    },
    
    showSettings() {
      console.log('显示设置');
    },
    
    showUserMenu() {
      console.log('显示用户菜单');
    },
    
    // 导航控制
    expandNav() {
      this.navExpanded = true;
    },
    
    collapseNav() {
      this.navExpanded = false;
    },
    
    // 初始化WebRTC客户端
    initWebRTCClient() {
      this.webrtcClient = new WebRTCAttentionClient();

      // 设置分析结果回调
      this.webrtcClient.setResultCallback((result) => {
        this.handleWebRTCResult(result);
      });

      // 设置状态回调
      this.webrtcClient.setStatusCallback((message, type) => {
        this.showAnalysisStatus(message, type);

        // 如果是成功启动的消息，更新连接状态
        if (message.includes('注意力分析已启动') || message.includes('启动成功')) {
          this.webrtcConnected = true;
        }
      });

      // 设置远程流回调（处理后的视频）
      this.webrtcClient.setRemoteStreamCallback((remoteStream) => {
        this.handleRemoteStream(remoteStream);
      });

      // 设置468特征点回调
      this.webrtcClient.setLandmarksCallback((landmarks) => {
        this.handleLandmarksData(landmarks);
      });

      console.log('✅ WebRTC客户端初始化完成');
    },

    // 处理WebRTC分析结果
    handleWebRTCResult(result) {
      console.log('📊 WebRTC分析结果:', result);

      // 确保数据收集会话处于活动状态
      if (!this.sessionActive || !this.currentSession) {
        console.log('🎯 自动启动数据收集会话');
        this.startDataCollection();
      }

      // 更新详细评分
      this.eyeScore = Math.round((result.eye_score || 0) * 100);
      this.faceScore = Math.round((result.face_score || 0) * 100);
      this.motionScore = Math.round((result.motion_score || 0) * 100);
      this.currentAttentionLevel = result.attention_level;

      // 转换为前端格式，确保数据完整性
      const attentionLevel = this.mapStatusToLevel(result.attention_status);
      const attentionScore = Math.round((result.confidence || 0) * 100);

      const emotionResult = {
        emotion: result.attention_status || '正常',
        level_name: result.attention_status || '正常',
        attention_score: attentionScore,
        confidence: result.confidence || 0,
        attention_level: attentionLevel,
        eye_score: result.eye_score || 0,
        face_score: result.face_score || 0,
        motion_score: result.motion_score || 0,
        details: {
          attention_level: attentionLevel,
          level_name: result.attention_status || '正常',
          eye_score: result.eye_score || 0,
          face_score: result.face_score || 0,
          motion_score: result.motion_score || 0
        },
        timestamp: new Date().toISOString()
      };

      // 🔥 数据采样：每秒只记录一次数据
      const currentTime = Date.now();
      if (currentTime - this.lastSampleTime >= this.sampleInterval) {
        this.tempDetectionData.push({
          score: attentionScore,
          level: attentionLevel,
          status: result.attention_status || '正常',
          timestamp: new Date().toISOString()
        });
        this.lastSampleTime = currentTime;
        console.log('📊 采样数据已记录:', {
          score: attentionScore,
          level: attentionLevel,
          status: result.attention_status,
          totalSamples: this.tempDetectionData.length
        });
      }

      // 更新界面（实时更新）
      this.updateEmotionResults(emotionResult);
    },

    // 处理远程视频流（Python处理后的画面）
    handleRemoteStream(remoteStream) {
      console.log('📺 接收到处理后的视频流');

      // 将处理后的视频流设置到视频元素
      const video = this.$refs.emotionVideo;
      if (video) {
        video.srcObject = remoteStream;
        console.log('✅ 处理后的视频流已设置到视频元素');

        // 等待视频加载完成后设置Canvas尺寸
        video.addEventListener('loadedmetadata', () => {
          this.$nextTick(() => {
            this.setupCanvas();
          });
        });

        // 设置WebRTC连接状态为已连接
        this.webrtcConnected = true;
        this.showAnalysisStatus('WebRTC连接成功，AI分析已启动', 'success');
      }
    },

    // 将注意力等级映射为情绪
    mapAttentionToEmotion(level) {
      const mapping = {
        0: '专注',     // 专注 (80-100分)
        1: '正常',     // 正常 (60-79分)
        2: '分心',     // 分心 (40-59分)
        3: '发呆'      // 发呆 (0-39分)
      };
      return mapping[level] || '未知';
    },

    // 将注意力状态转换为数字等级
    mapStatusToLevel(status) {
      const mapping = {
        '专注': 0,     // 专注 (80-100分)
        '正常': 1,     // 正常 (60-79分)
        '分心': 2,     // 分心 (40-59分)
        '发呆': 3      // 发呆 (0-39分)
      };
      return mapping[status] !== undefined ? mapping[status] : 1; // 默认为正常
    },

    

    // 显示一键启动指导
    showOneClickStartGuide() {
      const guide = `🚀 ADHD智能检测系统

需要启动后端服务器才能进行AI检测。

📋 一键启动方法：

方式1（最简单）：
• 双击项目根目录的 "start_all.bat" 文件

方式2（命令行）：
• 打开命令行，进入项目目录
• 运行：node start_all.js

⏱️ 启动通常需要10-30秒
🔄 启动后会自动继续AI检测，无需刷新页面

正在监控后端状态，启动后将自动继续...`;

      alert(guide);
      console.log('🚀 一键启动指导:', guide);
      this.showAnalysisStatus('等待后端服务器启动...', 'info');
    },

    // 显示一键启动对话框
    async showOneClickStartDialog() {
      return new Promise((resolve) => {
        const message = `🚀 ADHD智能检测系统

需要启动后端服务器才能进行AI检测。

💡 最简单的启动方式：
   双击项目根目录的 "start_all.bat" 文件

⚡ 或者运行命令：node start_all.js

启动后会自动开始AI检测，无需刷新页面！`;

        if (confirm(message + '\n\n点击"确定"查看详细启动指南，点击"取消"稍后启动')) {
          resolve('start');
        } else {
          resolve('cancel');
        }
      });
    },

    // 显示快速启动指南
    showQuickStartGuide() {
      const guide = `🚀 ADHD系统一键启动指南

📁 最简单方式：
   1. 找到项目根目录的 "start_all.bat" 文件
   2. 双击运行即可！

💻 命令行方式：
   1. 打开命令行/终端
   2. 进入项目目录：cd adhd
   3. 运行：node start_all.js

🔧 VS Code方式：
   1. 在VS Code中打开项目
   2. 按 Ctrl+Shift+P
   3. 选择 "Tasks: Run Task" → "Start All Services"

⏱️ 启动时间：通常需要10-30秒
🔄 自动检测：启动成功后会自动继续AI检测
📱 访问地址：http://localhost:8080

正在监控后端状态，启动后将自动继续...`;

      // 显示启动指南
      alert(guide);

      // 同时在控制台显示
      console.log('🚀 快速启动指南:', guide);

      // 更新状态显示
      this.showAnalysisStatus('等待后端服务器启动...', 'info');
    },

    // 等待用户启动后端服务器
    async waitForUserStartBackend() {
      console.log('⏳ 开始监控后端启动状态...');

      const maxWaitTime = 120000; // 最大等待2分钟
      const checkInterval = 2000; // 每2秒检查一次
      const startTime = Date.now();

      return new Promise((resolve) => {
        const checkBackend = async () => {
          const elapsed = Date.now() - startTime;
          const remainingTime = Math.max(0, maxWaitTime - elapsed);
          const remainingSeconds = Math.ceil(remainingTime / 1000);

          // 更新等待状态
          this.placeholderText = `等待后端启动... (${remainingSeconds}秒)`;

          // 检查后端是否启动
          const isConnected = await this.testBackendConnection();

          if (isConnected) {
            console.log('✅ 检测到后端服务器已启动！');
            this.showAnalysisStatus('后端服务器启动成功！正在继续AI检测...', 'success');
            resolve(true);
            return;
          }

          // 检查是否超时
          if (elapsed >= maxWaitTime) {
            console.log('⏰ 等待后端启动超时');
            this.showAnalysisStatus('等待后端启动超时，请检查启动状态', 'error');
            resolve(false);
            return;
          }

          // 继续等待
          setTimeout(checkBackend, checkInterval);
        };

        // 开始检查
        checkBackend();
      });
    },

    // 启动所有服务（最简单版本）
    async startAllServices() {
      try {
        console.log('🚀 正在启动所有服务...');
        this.showAnalysisStatus('正在启动后端和AI服务...', 'info');

        // 方法1: 尝试WebRTC直连
        const webrtcSuccess = await this.tryWebRTCDirect();
        if (webrtcSuccess) {
          console.log('✅ WebRTC直连成功，使用快速检测模式');
          this.showAnalysisStatus('WebRTC直连成功！', 'success');
          return true;
        }

        // 方法2: 传统后端启动
        const response = await fetch('http://localhost:3000/start-all', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            source: 'frontend',
            timestamp: Date.now()
          })
        });

        if (response.ok) {
          const result = await response.json();
          console.log('✅ 所有服务启动成功:', result);
          this.showAnalysisStatus('所有服务启动成功！', 'success');
          return true;
        } else {
          console.log('⚠️ 服务启动请求失败:', response.status);
          this.showAnalysisStatus('服务启动失败，尝试WebRTC直连模式', 'warning');
          return false;
        }

      } catch (error) {
        console.log('⚠️ 无法连接到自动启动服务器，尝试WebRTC直连');
        console.error('启动错误详情:', error);

        // 最后尝试WebRTC直连
        const webrtcSuccess = await this.tryWebRTCDirect();
        if (webrtcSuccess) {
          this.showAnalysisStatus('已切换到WebRTC直连模式', 'success');
          return true;
        }

        this.showAnalysisStatus('请先运行: python python/webrtc_direct.py', 'error');
        return false;
      }
    },

    // 尝试WebRTC直连
    async tryWebRTCDirect() {
      try {
        console.log('🔗 尝试WebRTC直连模式...');

        // 连接WebSocket
        const ws = new WebSocket('ws://localhost:8765');

        return new Promise((resolve) => {
          ws.onopen = () => {
            console.log('✅ WebRTC直连成功');
            this.webrtcSocket = ws;
            this.isWebRTCMode = true;

            // 发送ping测试
            ws.send(JSON.stringify({
              type: 'ping',
              timestamp: Date.now()
            }));

            resolve(true);
          };

          ws.onerror = () => {
            console.log('⚠️ WebRTC直连失败');
            resolve(false);
          };

          ws.onmessage = (event) => {
            const data = JSON.parse(event.data);
            this.handleWebRTCMessage(data);
          };

          // 3秒超时
          setTimeout(() => {
            if (ws.readyState !== WebSocket.OPEN) {
              ws.close();
              resolve(false);
            }
          }, 3000);
        });

      } catch (error) {
        console.error('WebRTC直连错误:', error);
        return false;
      }
    },

    // 处理WebRTC消息
    handleWebRTCMessage(data) {
      const messageType = data.type;
      console.log('📨 处理WebRTC消息类型:', messageType, '数据:', data);

      if (messageType === 'pong') {
        console.log('📡 WebRTC连接正常');
      } else if (messageType === 'detection_result') {
        console.log('🎯 收到检测结果，准备处理...');
        // 处理AI检测结果
        this.handleDetectionResult(data.result);
      } else if (messageType === 'detection_started') {
        console.log('🚀 WebRTC AI检测已启动');
        this.showAnalysisStatus('WebRTC AI检测已启动', 'success');
      } else {
        console.log('❓ 未知的WebRTC消息类型:', messageType);
      }
    },

    // 处理检测结果
    handleDetectionResult(result) {
      console.log('🎯 收到AI检测结果:', result);

      // 更新注意力数据
      this.attentionLevel = result.attention_level || 0.5;
      this.attentionStatus = result.attention_status || '检测中';
      this.attentionScore = Math.round((result.attention_level || 0.5) * 100);

      console.log('📊 更新前端数据:', {
        attentionLevel: this.attentionLevel,
        attentionStatus: this.attentionStatus,
        attentionScore: this.attentionScore
      });

      // 更新情绪状态
      if (result.attention_status === '专注') {
        this.currentEmotion = { text: '专注', icon: 'fas fa-bullseye' };
      } else if (result.attention_status === '正常') {
        this.currentEmotion = { text: '正常', icon: 'fas fa-eye' };
      } else if (result.attention_status === '分心') {
        this.currentEmotion = { text: '分心', icon: 'fas fa-eye-slash' };
      } else if (result.attention_status === '发呆') {
        this.currentEmotion = { text: '发呆', icon: 'fas fa-bed' };
      } else {
        this.currentEmotion = { text: result.attention_status, icon: 'fas fa-brain' };
      }

      console.log('😊 更新情绪状态:', this.currentEmotion);

      // 更新详细评分
      // 如果后端没有提供详细评分，基于现有数据生成
      if (result.eye_score !== undefined || result.face_score !== undefined || result.motion_score !== undefined) {
        // 使用后端提供的详细评分
        this.eyeScore = Math.round((result.eye_score || result.details?.eye_score || 0) * 100);
        this.faceScore = Math.round((result.face_score || result.details?.face_score || 0) * 100);
        this.motionScore = Math.round((result.motion_score || result.details?.motion_score || 0) * 100);
      } else {
        // 基于注意力水平和置信度生成合理的详细评分
        const baseScore = this.attentionScore;
        const confidence = (result.confidence || 0.5) * 100;

        // 眼动评分：主要基于注意力水平
        this.eyeScore = Math.round(baseScore + (Math.random() - 0.5) * 10);

        // 面部评分：基于面部检测数量和置信度
        const faceCount = result.face_count || 1;
        this.faceScore = Math.round(faceCount === 1 ? confidence : confidence * 0.8);

        // 动作评分：基于注意力状态
        if (result.attention_status === '专注') {
          this.motionScore = Math.round(baseScore + 10);
        } else if (result.attention_status === '正常') {
          this.motionScore = Math.round(baseScore);
        } else if (result.attention_status === '分心') {
          this.motionScore = Math.round(baseScore - 10);
        } else if (result.attention_status === '发呆') {
          this.motionScore = Math.round(baseScore - 20);
        } else {
          this.motionScore = Math.round(baseScore);
        }

        // 确保分数在0-100范围内
        this.eyeScore = Math.max(0, Math.min(100, this.eyeScore));
        this.faceScore = Math.max(0, Math.min(100, this.faceScore));
        this.motionScore = Math.max(0, Math.min(100, this.motionScore));
      }

      console.log('📊 更新详细评分:', {
        eyeScore: this.eyeScore,
        faceScore: this.faceScore,
        motionScore: this.motionScore
      });

      // 更新UI显示
      this.updateAttentionDisplay();

      // 处理特征点数据
      if (result.landmarks && Array.isArray(result.landmarks)) {
        console.log('🎯 收到特征点数据，数量:', result.landmarks.length);
        this.drawLandmarks(result.landmarks);
      } else {
        console.log('⚠️ 未收到特征点数据或数据格式不正确');
      }

      // 准备完整的检测数据
      const detectionData = {
        ...result,
        attention_score: this.attentionScore,
        eye_score: result.eye_score || result.details?.eye_score || 0,
        face_score: result.face_score || result.details?.face_score || 0,
        motion_score: result.motion_score || result.details?.motion_score || 0
      };

      // 记录检测数据
      this.recordDetectionData(detectionData);
    },

    // 启动WebRTC直连分析（简化版）
    async startWebRTCDirectAnalysis() {
      try {
        console.log('🚀 启动WebRTC直连AI分析...');

        // 清空之前的检测数据
        this.attentionTrend = [];
        this.tempDetectionData = [];
        this.lastSampleTime = 0;
        console.log('🔄 已清空之前的检测数据和临时数据');

        // 1. 连接WebSocket
        if (!this.webrtcSocket || this.webrtcSocket.readyState !== WebSocket.OPEN) {
          console.log('🔗 连接WebSocket服务器...');

          // 关闭旧连接
          if (this.webrtcSocket) {
            this.webrtcSocket.close();
          }

          this.webrtcSocket = new WebSocket('ws://localhost:8765');

          await new Promise((resolve, reject) => {
            this.webrtcSocket.onopen = () => {
              console.log('✅ WebSocket连接成功');
              resolve();
            };

            this.webrtcSocket.onerror = (error) => {
              console.error('❌ WebSocket连接错误:', error);
              reject(new Error('WebSocket连接失败，请确保Python服务器正在运行'));
            };

            this.webrtcSocket.onclose = (event) => {
              console.log('🔌 WebSocket连接已关闭:', event.code, event.reason);
              this.webrtcConnected = false;
            };

            setTimeout(() => reject(new Error('WebSocket连接超时')), 5000);
          });

          // 设置消息处理
          this.webrtcSocket.onmessage = (event) => {
            try {
              const data = JSON.parse(event.data);
              console.log('📨 收到WebSocket消息:', data);
              this.handleWebRTCMessage(data);
            } catch (error) {
              console.error('❌ 解析WebSocket消息失败:', error);
            }
          };
        }

        // 2. 启动摄像头
        const stream = await navigator.mediaDevices.getUserMedia({
          video: { width: 640, height: 480, facingMode: 'user' }
        });

        // 3. 设置视频元素
        const video = this.$refs.emotionVideo;
        video.srcObject = stream;
        await video.play();

        // 4. 创建canvas用于捕获帧
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        canvas.width = 640;
        canvas.height = 480;

        // 5. 发送开始检测信号
        this.webrtcSocket.send(JSON.stringify({
          type: 'start_detection',
          timestamp: Date.now()
        }));

        // 6. 等待视频完全加载后开始发送视频帧
        await new Promise(resolve => {
          if (video.readyState >= 2) {
            resolve();
          } else {
            video.addEventListener('loadeddata', resolve, { once: true });
          }
        });

        // 先设置状态，再开始帧捕获
        this.cameraActive = true;
        this.webrtcConnected = true;

        // 设置特征点Canvas
        this.setupCanvas();

        console.log('📹 视频已准备就绪，开始发送帧数据');
        this.startVideoFrameCapture(video, canvas, ctx);

        console.log('✅ WebRTC直连AI分析启动成功');

      } catch (error) {
        console.error('❌ WebRTC直连分析启动失败:', error);
        throw error;
      }
    },

    // 开始视频帧捕获
    startVideoFrameCapture(video, canvas, ctx) {
      console.log('🎬 开始视频帧捕获');

      const captureFrame = () => {
        if (!this.cameraActive || !this.webrtcSocket || this.webrtcSocket.readyState !== WebSocket.OPEN) {
          console.log('⏹️ 停止帧捕获 - 摄像头或连接已断开');
          return;
        }

        try {
          // 绘制当前帧到canvas
          ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

          // 转换为base64
          const frameData = canvas.toDataURL('image/jpeg', 0.8);

          // 发送帧数据
          this.webrtcSocket.send(JSON.stringify({
            type: 'video_frame',
            frame: frameData,
            timestamp: Date.now()
          }));

          console.log('📤 发送视频帧');

        } catch (error) {
          console.error('❌ 捕获视频帧失败:', error);
        }

        // 每100ms捕获一帧（10fps）
        setTimeout(captureFrame, 100);
      };

      // 立即开始捕获
      captureFrame();
    },

    // 更新注意力显示
    updateAttentionDisplay() {
      console.log(`📊 更新UI显示 - 注意力状态: ${this.attentionStatus}, 水平: ${this.attentionLevel}, 分数: ${this.attentionScore}`);

      // 强制触发Vue的响应式更新
      this.$forceUpdate();

      // 确保数据已经更新到DOM
      this.$nextTick(() => {
        console.log('✅ UI更新完成');
      });
    },

    // 尝试启动服务器（简化版）
    async tryStartupServerSimple() {
      try {
        const response = await fetch('http://localhost:3002/start-backend', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ timestamp: Date.now() })
        });

        return response.ok;
      } catch (error) {
        console.log('启动服务器未运行');
        return false;
      }
    },

    // 尝试直接启动后端
    async tryDirectBackendStart() {
      try {
        // 尝试访问后端的特殊启动端点
        const response = await fetch('http://localhost:3001/api/auto-start', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ action: 'start_all' })
        });

        return response.ok;
      } catch (error) {
        console.log('直接启动后端失败');
        return false;
      }
    },

    // 创建启动触发器
    async createStartupTrigger() {
      try {
        console.log('� 创建启动触发器...');

        // 发送启动事件
        const startupEvent = new CustomEvent('adhd-startup', {
          detail: {
            action: 'start_all_services',
            timestamp: Date.now()
          }
        });

        window.dispatchEvent(startupEvent);

        // 同时尝试通过postMessage
        window.postMessage({
          type: 'ADHD_START_ALL',
          action: 'start_backend_and_services'
        }, '*');

        // 给一些时间让启动事件处理
        await new Promise(resolve => setTimeout(resolve, 2000));

        return true;
      } catch (error) {
        console.error('创建启动触发器失败:', error);
        return false;
      }
    },

    // 直接自动启动后端服务器（简化版）
    async autoStartBackendDirectly() {
      try {
        console.log('🚀 正在尝试自动启动后端服务器...');
        this.showAnalysisStatus('正在自动启动后端服务器...', 'info');

        // 简单的启动尝试
        const success = await this.trySimpleStartup();

        if (success) {
          console.log('✅ 启动命令执行成功');
          return true;
        }

        console.log('⚠️ 自动启动需要用户手动操作');
        return false;

      } catch (error) {
        console.error('❌ 自动启动失败:', error);
        return false;
      }
    },

    // 尝试简单启动
    async trySimpleStartup() {
      try {
        console.log('🔧 尝试简单启动方案...');

        // 尝试通过fetch请求启动
        const response = await fetch('http://localhost:3002/start-backend', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            timestamp: Date.now()
          })
        });

        if (response.ok) {
          const result = await response.json();
          console.log('✅ 后端启动请求成功:', result);
          return true;
        } else {
          console.log('⚠️ 启动服务器未运行，需要手动启动');
          return false;
        }

      } catch (error) {
        console.log('⚠️ 无法连接启动服务器，需要手动启动');
        return false;
      }
    },



    // 自动启动后端服务器（保留原方法，但简化）
    async autoStartBackendServer() {
      try {
        console.log('🚀 尝试自动启动后端服务器...');
        
        // 显示启动提示
        this.showAnalysisStatus('正在尝试自动启动后端服务器...', 'info');
        
        // 方法1: 尝试通过HTTP请求触发启动
        try {
          console.log('🔧 尝试通过HTTP请求触发启动...');
          
          // 发送启动请求到一个特殊的启动端点
          const response = await fetch('http://localhost:3001/api/auto-start', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({
              action: 'start_all_services'
            })
          });
          
          if (response.ok) {
            console.log('✅ 启动请求发送成功');
            // 给一些时间让服务启动
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // 检查是否启动成功
            const isStarted = await this.testBackendConnection();
            if (isStarted) {
              console.log('✅ 后端服务器自动启动成功');
              this.showAnalysisStatus('后端服务器自动启动成功！', 'success');
              return true;
            }
          }
        } catch (error) {
          console.warn('HTTP启动请求失败:', error);
        }
        
        // 方法2: 尝试通过创建隐藏的启动页面
        try {
          console.log('🔧 尝试通过启动页面触发启动...');
          
          // 创建一个隐藏的iframe来加载启动页面
          const startupFrame = document.createElement('iframe');
          startupFrame.style.display = 'none';
          startupFrame.src = 'about:blank';
          document.body.appendChild(startupFrame);
          
          // 在iframe中运行启动脚本
          startupFrame.onload = () => {
            try {
              const script = startupFrame.contentDocument.createElement('script');
              script.textContent = `
                // 尝试通过各种方式启动后端
                console.log('尝试启动后端服务器...');
                
                // 方式1: 尝试通过Node.js模块
                if (typeof require !== 'undefined') {
                  try {
                    const { spawn } = require('child_process');
                    const backend = spawn('node', ['server/app.js'], {
                      detached: true,
                      stdio: 'ignore'
                    });
                    backend.unref();
                    console.log('通过Node.js模块启动后端');
                  } catch (e) {
                    console.warn('Node.js模块启动失败:', e);
                  }
                }
                
                // 方式2: 尝试通过Electron（如果在Electron环境中）
                if (typeof window !== 'undefined' && window.require) {
                  try {
                    const { ipcRenderer } = window.require('electron');
                    ipcRenderer.send('start-backend-server');
                    console.log('通过Electron启动后端');
                  } catch (e) {
                    console.warn('Electron启动失败:', e);
                  }
                }
              `;
              startupFrame.contentDocument.body.appendChild(script);
            } catch (error) {
              console.warn('启动脚本注入失败:', error);
            }
          };
          
          // 5秒后清理iframe
          setTimeout(() => {
            if (document.body.contains(startupFrame)) {
              document.body.removeChild(startupFrame);
            }
          }, 5000);
          
        } catch (error) {
          console.warn('启动页面方法失败:', error);
        }
        
        // 给一些时间让启动脚本运行
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 检查是否启动成功
        const isStarted = await this.testBackendConnection();
        if (isStarted) {
          console.log('✅ 后端服务器自动启动成功');
          this.showAnalysisStatus('后端服务器自动启动成功！', 'success');
          return true;
        } else {
          console.log('⚠️ 自动启动可能失败，显示用户引导');
          this.showBackendStartupGuide();
          return false;
        }
        
      } catch (error) {
        console.error('❌ 自动启动后端失败:', error);
        this.showAnalysisStatus('自动启动失败，请手动启动后端服务器', 'warning');
        this.showBackendStartupGuide();
        return false;
      }
    },
    
    // 显示后端启动引导
    showBackendStartupGuide() {
      const isWindows = navigator.platform.toLowerCase().includes('win');
      const startCommand = isWindows ? 'start_all.bat' : 'node server/app.js';
      
      const message = `
🚀 需要手动启动后端服务器

请选择以下任一方式启动：

方式1（推荐）：
• 双击项目根目录的 ${isWindows ? 'start_all.bat' : 'start_all.js'} 文件

方式2：
• 打开${isWindows ? '命令提示符' : '终端'}
• 进入项目目录
• 运行: ${startCommand}

方式3：
• 在VS Code中打开项目
• 按 Ctrl+Shift+P 打开命令面板
• 运行: "Tasks: Run Task" → "Start All Services"

启动完成后，重新点击"开始AI检测"按钮即可！

⚠️ 注意：首次启动可能需要1-2分钟，请耐心等待。
      `;
      
      // 显示更友好的启动引导
      if (confirm(message + '\n\n点击"确定"查看详细说明，点击"取消"关闭')) {
        // 打开详细的启动说明页面
        this.showDetailedStartupGuide();
      }
      
      // 同时在控制台输出
      console.log('📋 后端启动引导:', message);
    },
    
    // 显示详细的启动引导
    showDetailedStartupGuide() {
      const isWindows = navigator.platform.toLowerCase().includes('win');
      
      // 创建一个临时的启动引导页面
      const guideWindow = window.open('', '_blank', 'width=800,height=600');
      guideWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>ADHD项目 - 后端启动引导</title>
          <style>
            body { 
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
              padding: 20px; 
              background: #f5f5f5; 
              color: #333;
            }
            .guide-container {
              max-width: 700px;
              margin: 0 auto;
              background: white;
              border-radius: 10px;
              padding: 30px;
              box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 { 
              color: #2c3e50; 
              text-align: center;
              margin-bottom: 30px;
            }
            h2 { 
              color: #3498db; 
              border-bottom: 2px solid #3498db;
              padding-bottom: 5px;
            }
            .method {
              background: #f8f9fa;
              border-left: 4px solid #3498db;
              padding: 15px;
              margin: 15px 0;
              border-radius: 5px;
            }
            .method h3 {
              color: #2c3e50;
              margin-top: 0;
            }
            .code {
              background: #2c3e50;
              color: #ecf0f1;
              padding: 10px;
              border-radius: 5px;
              font-family: 'Consolas', monospace;
              margin: 10px 0;
            }
            .warning {
              background: #fff3cd;
              color: #856404;
              padding: 15px;
              border-radius: 5px;
              border: 1px solid #ffeaa7;
              margin: 20px 0;
            }
            .success {
              background: #d4edda;
              color: #155724;
              padding: 15px;
              border-radius: 5px;
              border: 1px solid #c3e6cb;
              margin: 20px 0;
            }
            .btn {
              background: #3498db;
              color: white;
              padding: 10px 20px;
              border: none;
              border-radius: 5px;
              cursor: pointer;
              font-size: 16px;
              margin: 10px 0;
            }
            .btn:hover {
              background: #2980b9;
            }
          </style>
        </head>
        <body>
          <div class="guide-container">
            <h1>🚀 ADHD项目后端启动引导</h1>
            
            <div class="warning">
              <strong>⚠️ 重要提醒：</strong>
              <p>AI检测功能需要后端服务器支持，请选择以下任一方式启动后端服务器。</p>
            </div>
            
            <h2>启动方式选择</h2>
            
            <div class="method">
              <h3>🎯 方式1：双击启动文件（推荐）</h3>
              <p>在项目根目录找到并双击：</p>
              <div class="code">${isWindows ? 'start_all.bat' : 'start_all.js'}</div>
              <p>这将自动启动前端和后端服务器。</p>
            </div>
            
            <div class="method">
              <h3>🔧 方式2：命令行启动</h3>
              <p>打开${isWindows ? '命令提示符' : '终端'}，进入项目目录，运行：</p>
              <div class="code">${isWindows ? 'start_all.bat' : 'node server/app.js'}</div>
              <p>或者分别启动前端和后端：</p>
              <div class="code">
                # 启动后端（端口3001）<br>
                cd server<br>
                node app.js<br>
                <br>
                # 新开终端，启动前端（端口8080）<br>
                npm run serve
              </div>
            </div>
            
            <div class="method">
              <h3>💻 方式3：VS Code启动</h3>
              <p>如果你使用VS Code：</p>
              <div class="code">
                1. 按 Ctrl+Shift+P 打开命令面板<br>
                2. 输入 "Tasks: Run Task"<br>
                3. 选择 "Start All Services"
              </div>
            </div>
            
            <h2>启动成功标志</h2>
            <div class="success">
              <strong>✅ 启动成功后，你应该看到：</strong>
              <ul>
                <li>后端服务器：http://localhost:3001 可访问</li>
                <li>前端应用：http://localhost:8080 可访问</li>
                <li>控制台显示 "服务器启动成功" 信息</li>
              </ul>
            </div>
            
            <div class="warning">
              <strong>📝 故障排除：</strong>
              <ul>
                <li>确保端口3001和8080未被占用</li>
                <li>检查是否安装了Node.js和Python</li>
                <li>确认已运行 npm install 安装依赖</li>
                <li>首次启动可能需要1-2分钟</li>
              </ul>
            </div>
            
            <button class="btn" onclick="window.close()">关闭引导</button>
          </div>
        </body>
        </html>
      `);
      
      guideWindow.document.close();
    },
    
    // 智能启动检测和引导
    async intelligentStartupAssistant() {
      console.log('🤖 启动智能启动助手...');
      
      // 检查各种可能的启动方式
      const startupMethods = [
        {
          name: '双击启动文件',
          description: '最简单的启动方式',
          command: 'start_all.bat',
          available: true,
          priority: 1
        },
        {
          name: '命令行启动',
          description: '适合开发者',
          command: 'node server/app.js',
          available: true,
          priority: 2
        },
        {
          name: 'VS Code任务',
          description: '在VS Code中启动',
          command: 'Tasks: Run Task',
          available: this.isVSCodeEnvironment(),
          priority: 3
        }
      ];
      
      // 按优先级排序
      startupMethods.sort((a, b) => a.priority - b.priority);
      
      // 显示推荐的启动方式
      const recommendedMethod = startupMethods.find(m => m.available);
      if (recommendedMethod) {
        console.log('💡 推荐启动方式:', recommendedMethod.name);
        this.showAnalysisStatus(`推荐使用: ${recommendedMethod.name}`, 'info');
      }
      
      return startupMethods;
    },
    
    // 检查是否在VS Code环境中
    isVSCodeEnvironment() {
      return typeof window !== 'undefined' && 
             (window.location.hostname === 'localhost' || 
              window.location.hostname === '127.0.0.1') &&
             navigator.userAgent.includes('VSCode');
    },
    
    // 启动状态持续监控
    async startBackendMonitoring() {
      console.log('📊 开始后端状态监控...');
      
      let attempts = 0;
      const maxAttempts = 30; // 最多检查30次，约1分钟
      const checkInterval = 2000; // 每2秒检查一次
      
      const monitor = setInterval(async () => {
        attempts++;
        
        try {
          const isConnected = await this.testBackendConnection();
          
          if (isConnected) {
            console.log('✅ 后端服务器已启动！');
            this.showAnalysisStatus('后端服务器已启动，可以开始AI检测了！', 'success');
            clearInterval(monitor);
            
            // 自动重试启动AI检测
            if (confirm('后端服务器已启动！是否现在开始AI检测？')) {
              this.startCamera();
            }
          } else {
            console.log(`🔄 检查中... (${attempts}/${maxAttempts})`);
            this.showAnalysisStatus(`正在等待后端启动... (${attempts}/${maxAttempts})`, 'info');
            
            if (attempts >= maxAttempts) {
              console.log('⏰ 监控超时，停止检查');
              this.showAnalysisStatus('等待超时，请检查后端是否正常启动', 'warning');
              clearInterval(monitor);
            }
          }
        } catch (error) {
          console.warn('监控检查失败:', error);
        }
      }, checkInterval);
      
      // 返回监控器，以便外部控制
      return monitor;
    },
    
    // 自动启动所有服务
    async autoStartAllServices() {
      console.log('🚀 尝试自动启动所有服务...');
      
      // 显示启动提示
      this.showAnalysisStatus('正在尝试自动启动所有服务...', 'info');
      
      // 方法1: 尝试通过HTTP请求触发启动
      try {
        console.log('🔧 尝试通过HTTP请求触发启动...');
        
        // 发送启动请求到一个特殊的启动端点
        const response = await fetch('http://localhost:3001/api/auto-start', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            action: 'start_all_services'
          })
        });
        
        if (response.ok) {
          console.log('✅ 启动请求发送成功');
          this.showAnalysisStatus('所有服务启动请求已发送', 'success');
        } else {
          const errorText = await response.text();
          console.error('❌ 启动请求失败:', response.status, errorText);
          this.showAnalysisStatus(`启动请求失败: ${response.status} ${response.statusText}`, 'error');
        }
      } catch (error) {
        console.error('❌ 启动请求异常:', error);
        this.showAnalysisStatus(`启动请求异常: ${error.message}`, 'error');
      }
      
      // 方法2: 尝试通过创建隐藏的启动页面
      try {
        console.log('🔧 尝试通过启动页面触发启动...');
        
        // 创建一个隐藏的iframe来加载启动页面
        const startupFrame = document.createElement('iframe');
        startupFrame.style.display = 'none';
        startupFrame.src = 'about:blank';
        document.body.appendChild(startupFrame);
        
        // 在iframe中运行启动脚本
        startupFrame.onload = () => {
          try {
            const script = startupFrame.contentDocument.createElement('script');
            script.textContent = `
              // 尝试通过各种方式启动后端
              console.log('尝试启动所有服务...');
              
              // 方式1: 尝试通过Node.js模块
              if (typeof require !== 'undefined') {
                try {
                  const { spawn } = require('child_process');
                  const backend = spawn('node', ['server/app.js'], {
                    detached: true,
                    stdio: 'ignore'
                  });
                  backend.unref();
                  console.log('通过Node.js模块启动后端');
                } catch (e) {
                  console.warn('Node.js模块启动失败:', e);
                }
              }
              
              // 方式2: 尝试通过Electron（如果在Electron环境中）
              if (typeof window !== 'undefined' && window.require) {
                try {
                  const { ipcRenderer } = window.require('electron');
                  ipcRenderer.send('start-all-services');
                  console.log('通过Electron启动所有服务');
                } catch (e) {
                  console.warn('Electron启动失败:', e);
                }
              }
            `;
            startupFrame.contentDocument.body.appendChild(script);
          } catch (error) {
            console.warn('启动脚本注入失败:', error);
          }
        };
        
        // 5秒后清理iframe
        setTimeout(() => {
          if (document.body.contains(startupFrame)) {
            document.body.removeChild(startupFrame);
          }
        }, 5000);
        
      } catch (error) {
        console.warn('启动页面方法失败:', error);
      }
      
      // 给一些时间让启动脚本运行
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 检查是否启动成功
      const isStarted = await this.testBackendConnection();
      if (isStarted) {
        console.log('✅ 所有服务自动启动成功');
        this.showAnalysisStatus('所有服务自动启动成功！', 'success');
      } else {
        console.log('⚠️ 自动启动可能失败，显示用户引导');
        this.showBackendStartupGuide();
      }
    },
    
    // 等待后端服务器启动
    async waitForBackendStartup(maxWaitTime = 30000) {
      const startTime = Date.now();
      const checkInterval = 2000; // 每2秒检查一次
      
      console.log('⏳ 等待后端服务器启动...');
      
      while (Date.now() - startTime < maxWaitTime) {
        try {
          const isConnected = await this.testBackendConnection();
          if (isConnected) {
            console.log('✅ 后端服务器已启动');
            return true;
          }
        } catch (error) {
          console.log('🔄 后端服务器尚未启动，继续等待...');
        }
        
        // 等待一段时间后重试
        await new Promise(resolve => setTimeout(resolve, checkInterval));
      }
      
      console.log('⏱️ 等待后端服务器启动超时');
      return false;
    },

    // 启动AI检测
    async startCamera() {
      if (this.cameraActive) {
        console.log('⚠️ 摄像头已在运行中');
        return;
      }

      this.isLoading = true;
      this.placeholderText = '正在检查摄像头权限...';

      try {
        // 1. 首先检查摄像头权限
        console.log('🎥 检查摄像头权限...');

        // 检查权限API支持
        if ('permissions' in navigator) {
          try {
            const permissionStatus = await navigator.permissions.query({ name: 'camera' });
            console.log('📋 摄像头权限状态:', permissionStatus.state);

            if (permissionStatus.state === 'denied') {
              throw new Error('摄像头权限被拒绝，请在浏览器设置中允许摄像头访问');
            }
          } catch (permError) {
            console.warn('⚠️ 权限查询失败，继续尝试获取摄像头:', permError);
          }
        }

        // 2. 尝试获取摄像头流（测试权限）
        this.placeholderText = '正在获取摄像头访问权限...';
        let testStream;
        try {
          testStream = await navigator.mediaDevices.getUserMedia({
            video: {
              width: { ideal: 640 },
              height: { ideal: 480 },
              facingMode: 'user'
            }
          });
          console.log('✅ 摄像头权限获取成功');

          // 立即停止测试流
          testStream.getTracks().forEach(track => track.stop());
        } catch (mediaError) {
          console.error('❌ 摄像头访问失败:', mediaError);
          if (mediaError.name === 'NotAllowedError') {
            throw new Error('摄像头权限被拒绝，请点击地址栏的摄像头图标允许访问');
          } else if (mediaError.name === 'NotFoundError') {
            throw new Error('未找到摄像头设备，请检查摄像头是否正确连接');
          } else {
            throw new Error(`摄像头访问失败: ${mediaError.message}`);
          }
        }

        // 3. 直接启动AI分析（简化版）
        this.placeholderText = '正在连接AI服务器...';

        // 4. 启动AI分析（简化版）
        this.placeholderText = '正在启动AI检测...';
        await this.startWebRTCDirectAnalysis();

        this.isLoading = false;
        this.showAnalysisStatus('AI检测启动成功！', 'success');
        console.log('✅ AI检测启动成功');

      } catch (error) {
        console.error('❌ 启动AI检测失败:', error);
        this.isLoading = false;
        this.cameraActive = false;
        this.webrtcConnected = false;
        this.placeholderText = `启动失败: ${error.message}`;
        this.showAnalysisStatus(error.message, 'error');
      }
    },

    // 启动WebRTC注意力分析
    async startWebRTCAnalysis() {
      try {
        // 1. 首先启动Python服务器
        this.placeholderText = '正在启动Python AI服务器...';
        console.log('🚀 启动Python服务器...');

        // 首先测试后端连接
        const backendConnected = await this.testBackendConnection();
        if (!backendConnected) {
          throw new Error('无法连接到后端服务器，请确保后端服务器正在运行 (node server/app.js)');
        }

        // 添加详细的网络调试信息
        console.log('🔍 尝试连接到:', 'http://localhost:3001/api/python-server/start');
        console.log('🌐 当前页面URL:', window.location.href);
        console.log('🔧 User Agent:', navigator.userAgent);

        // 带重试机制的服务器启动
        let startResponse;
        let retryCount = 0;
        const maxRetries = 3;

        while (retryCount < maxRetries) {
          try {
            console.log(`🔄 尝试启动Python服务器 (${retryCount + 1}/${maxRetries})...`);

            startResponse = await fetch('http://localhost:3001/api/python-server/start', {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              // 增加超时时间
              signal: AbortSignal.timeout(15000) // 15秒超时
            });

            console.log('📡 响应状态:', startResponse.status, startResponse.statusText);

            if (startResponse.ok) {
              break; // 成功，跳出重试循环
            } else {
              const errorText = await startResponse.text();
              console.warn(`⚠️ 第${retryCount + 1}次尝试失败:`, startResponse.status, errorText);

              if (retryCount === maxRetries - 1) {
                throw new Error(`启动Python服务器失败: ${startResponse.status} ${startResponse.statusText} - ${errorText}`);
              }
            }
          } catch (error) {
            console.warn(`⚠️ 第${retryCount + 1}次连接失败:`, error.message);

            if (retryCount === maxRetries - 1) {
              throw new Error(`连接后端服务器失败: ${error.message}`);
            }
          }

          retryCount++;
          if (retryCount < maxRetries) {
            console.log(`⏳ 等待${retryCount * 2}秒后重试...`);
            await new Promise(resolve => setTimeout(resolve, retryCount * 2000));
          }
        }

        const startResult = await startResponse.json();
        console.log('✅ Python服务器启动成功:', startResult);

        // 2. 等待服务器完全启动
        this.placeholderText = '等待服务器完全启动...';
        await new Promise(resolve => setTimeout(resolve, 2000));

        // 3. 初始化WebRTC客户端
        this.placeholderText = '正在连接WebRTC注意力分析服务器...';
        if (!this.webrtcClient) {
          this.initWebRTCClient();
        }

        // 4. 检查服务器状态
        const isHealthy = await this.webrtcClient.checkServerHealth();
        if (!isHealthy) {
          throw new Error('WebRTC服务器连接失败，请检查Python服务器状态');
        }

        // 5. 设置视频元素引用
        this.webrtcClient.setVideoElement(this.$refs.emotionVideo);

        // 6. 启动WebRTC分析
        await this.webrtcClient.startAnalysis();

        // 注意：不再设置本地流，因为我们要显示处理后的远程流
        console.log('⏳ 等待接收处理后的视频流...');

        this.cameraActive = true;
        this.isLoading = false;
        this.placeholderText = 'AI检测已启动，正在分析...';

        // 记录会话开始时间和ID
        this.sessionStartTime = new Date().toISOString();
        this.sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

        console.log('✅ WebRTC注意力分析启动成功');

      } catch (error) {
        console.error('❌ WebRTC分析启动失败:', error);

        // 详细的错误信息
        if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
          console.error('🔍 网络连接错误详情:');
          console.error('  - 错误类型: 网络请求失败');
          console.error('  - 可能原因: 后端服务器未启动或端口被占用');
          console.error('  - 检查项目: 确认 http://localhost:3001 是否可访问');
          this.placeholderText = '启动失败: 无法连接到后端服务器 (端口3001)';
        } else if (error.name === 'AbortError') {
          console.error('🔍 请求超时错误');
          this.placeholderText = '启动失败: 请求超时，请检查网络连接';
        } else {
          console.error('🔍 其他错误:', error.name, error.message);
          this.placeholderText = `启动失败: ${error.message}`;
        }

        this.isLoading = false;
        this.cameraActive = false;
        throw error; // 重新抛出错误，让调用者处理
      }
    },
    
    // 停止AI检测
    async stopCamera() {
      if (!this.cameraActive) {
        this.showAnalysisStatus('AI检测未在运行', 'info');
        return;
      }

      try {
        // 停止WebRTC连接
        if (this.webrtcSocket) {
          this.webrtcSocket.send(JSON.stringify({
            type: 'stop_detection',
            timestamp: Date.now()
          }));
          this.webrtcSocket.close();
          this.webrtcSocket = null;
        }

        // 停止摄像头流
        if (this.$refs.emotionVideo && this.$refs.emotionVideo.srcObject) {
          const stream = this.$refs.emotionVideo.srcObject;
          stream.getTracks().forEach(track => track.stop());
          this.$refs.emotionVideo.srcObject = null;
        }

        // 重置状态
        this.cameraActive = false;
        this.webrtcConnected = false;
        this.placeholderText = '点击"开始AI检测"启动AI分析';

        // 先获取当前会话数据（在结束前）
        const currentSessionBeforeEnd = dataCollectionService.getCurrentSession();
        console.log('🔍 停止前的当前会话:', currentSessionBeforeEnd);

        // 结束数据收集会话
        this.endDataCollection();

        // 重置界面显示的检测结果
        this.resetDetectionResults();

        this.showAnalysisStatus('AI检测已停止', 'info');
        console.log('✅ AI检测已停止');

      } catch (error) {
        console.error('停止AI检测失败:', error);
        this.showAnalysisStatus(`停止失败: ${error.message}`, 'error');
      }
    },

    // 重置检测结果显示
    resetDetectionResults() {
      this.currentEmotion = {
        text: '未检测',
        icon: 'fas fa-meh'
      };
      this.attentionScore = 0;
      this.detailedScores = null;
      this.attentionTrend = [];
      this.tempDetectionData = []; // 清空临时数据
      this.lastSampleTime = 0;
      this.eyeScore = 0;
      this.faceScore = 0;
      this.motionScore = 0;
      this.currentAttentionLevel = null;
    },



    // 计算会话统计数据（使用采样数据）
    calculateSessionStats() {
      console.log('📊 calculateSessionStats 被调用');
      console.log('📊 tempDetectionData长度:', this.tempDetectionData?.length || 0);
      console.log('📊 tempDetectionData内容:', this.tempDetectionData);

      const stats = {
        avgAttentionLevel: 0,
        avgAttentionScore: 0,
        level0Count: 0,
        level1Count: 0,
        level2Count: 0,
        level3Count: 0,
        totalDetections: 0,
        maxAttentionScore: 0,
        minAttentionScore: 100
      };

      console.log('🔍 计算会话统计 - 临时采样数据:', this.tempDetectionData);

      if (this.tempDetectionData && this.tempDetectionData.length > 0) {
        const scores = this.tempDetectionData.map(item => item.score);
        const levels = this.tempDetectionData.map(item => item.level);

        console.log('📊 提取的分数数组:', scores);
        console.log('📊 提取的等级数组:', levels);

        stats.totalDetections = scores.length;
        stats.avgAttentionScore = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
        stats.maxAttentionScore = Math.max(...scores);
        stats.minAttentionScore = Math.min(...scores);

        // 直接使用等级数据计算分布
        levels.forEach(level => {
          if (level === 0) {
            stats.level0Count++; // 专注
          } else if (level === 1) {
            stats.level1Count++; // 正常
          } else if (level === 2) {
            stats.level2Count++; // 分心
          } else if (level === 3) {
            stats.level3Count++; // 发呆
          }
        });

        // 计算平均注意力等级
        const totalLevels = stats.level0Count * 0 + stats.level1Count * 1 + stats.level2Count * 2 + stats.level3Count * 3;
        stats.avgAttentionLevel = parseFloat((totalLevels / stats.totalDetections).toFixed(2));

        console.log('📈 计算完成的统计数据:', stats);
      } else {
        console.warn('⚠️ 临时采样数据为空或未定义');
      }

      return stats;
    },











    // 立即分析（WebRTC模式下的手动触发）
    async captureSnapshot() {
      if (!this.webrtcConnected) {
        this.showAnalysisStatus('WebRTC未连接，无法进行分析', 'warning');
        return;
      }

      // WebRTC模式下，分析是实时进行的，这里只是显示状态
      this.showAnalysisStatus('AI正在实时分析中...', 'info');

      setTimeout(() => {
        this.showAnalysisStatus('实时AI分析进行中', 'success');
      }, 1000);
    },
    
    // 更新AI检测结果
    updateEmotionResults(result) {
      console.log('🎯 更新界面检测结果:', result);

      // 更新当前注意力状态
      this.currentEmotion = {
        text: result.level_name || '未检测',
        icon: this.getAttentionIcon(result.attention_level)
      };

      // 更新注意力分数 - 使用confidence作为注意力分数
      this.attentionScore = Math.round((result.confidence || 0) * 100);
      this.currentAttentionLevel = result.attention_level || 0;

      // 更新详细评分
      this.eyeScore = Math.round((result.eye_score || 0) * 100);
      this.faceScore = Math.round((result.face_score || 0) * 100);
      this.motionScore = Math.round((result.motion_score || 0) * 100);

      console.log('📊 界面数据已更新:', {
        attentionScore: this.attentionScore,
        eyeScore: this.eyeScore,
        faceScore: this.faceScore,
        motionScore: this.motionScore,
        currentEmotion: this.currentEmotion
      });

      // 添加到历史记录
      this.addToHistory(result);

      // 更新建议
      this.updateSuggestions(result);

      // 更新趋势数据（仅用于界面显示）
      console.log('🔄 准备更新趋势数据，当前注意力分数:', this.attentionScore);
      this.updateAttentionTrend(this.attentionScore);

      // 声音提醒（如果启用且注意力低）
      if (this.detectionSettings.soundAlert && result.attention_level < 2) {
        this.playAlertSound();
      }
    },
    
 
    // 获取注意力状态图标
    getAttentionIcon(level) {
      const iconMap = {
        0: 'fas fa-bullseye',   // 专注 (80-100分)
        1: 'fas fa-eye',        // 正常 (60-79分)
        2: 'fas fa-eye-slash',  // 分心 (40-59分)
        3: 'fas fa-bed'         // 发呆 (0-39分)
      };
      return iconMap[level] || 'fas fa-meh';
    },

    // 更新注意力趋势
    updateAttentionTrend(score) {
      // 存储为对象格式，包含score和时间戳
      const dataPoint = {
        score: score,
        timestamp: new Date().toISOString()
      };

      this.attentionTrend.push(dataPoint);
      console.log('📈 添加注意力数据点:', dataPoint, '当前总数:', this.attentionTrend.length);

      // 保持最近100个数据点（增加数据量以便更好的统计）
      if (this.attentionTrend.length > 100) {
        this.attentionTrend.shift();
      }
    },

    // 添加到历史记录
    addToHistory(result) {
      const record = {
        id: Date.now(),
        time: new Date().toLocaleTimeString(),
        emotion: result.emotion || '中性',
        score: result.attention_score || this.attentionScore
      };
      
      this.detectionHistory.unshift(record);
      
      // 只保留最近10条记录
      if (this.detectionHistory.length > 10) {
        this.detectionHistory = this.detectionHistory.slice(0, 10);
      }
    },
    
    // 更新建议
    updateSuggestions(result) {
      const suggestions = [];
      
      if (result.attention_score < 70) {
        suggestions.push({
          id: 1,
          icon: 'fas fa-coffee',
          text: '注意力较低，建议适当休息或进行注意力训练'
        });
      }
      
      if (result.emotion === '焦虑') {
        suggestions.push({
          id: 2,
          icon: 'fas fa-leaf',
          text: '检测到焦虑情绪，建议进行深呼吸或放松练习'
        });
      }
      
      if (result.emotion === '疲惫') {
        suggestions.push({
          id: 3,
          icon: 'fas fa-bed',
          text: '检测到疲惫状态，建议适当休息'
        });
      }
      
      if (suggestions.length === 0) {
        suggestions.push({
          id: 4,
          icon: 'fas fa-thumbs-up',
          text: '情绪状态良好，继续保持！'
        });
      }
      
      this.currentSuggestions = suggestions;
    },
    
    // 加载检测历史
    async loadDetectionHistory() {
      try {
        const response = await emotionAPI.getEmotionHistory({ limit: 10 });
        const history = response.data || response || [];
        this.detectionHistory = history.map(item => ({
          id: item.id,
          time: new Date(item.created_at).toLocaleTimeString(),
          emotion: item.emotion,
          score: item.attention_score
        }));
      } catch (error) {
        console.error('加载历史记录失败:', error);
        // 使用空数组作为默认值
        this.detectionHistory = [];
      }
    },





    // 生成会话ID
    generateSessionId() {
      const timestamp = Date.now();
      const random = Math.random().toString(36).substring(2, 11);
      return `session_${timestamp}_${random}`;
    },

    // 设置Canvas尺寸
    setupCanvas() {
      const video = this.$refs.emotionVideo;
      const canvas = this.$refs.emotionOverlay;

      if (video && canvas) {
        // 获取video元素的实际显示尺寸
        const rect = video.getBoundingClientRect();

        // 设置Canvas的像素尺寸和CSS尺寸都匹配video的显示尺寸
        canvas.width = rect.width;
        canvas.height = rect.height;
        canvas.style.width = rect.width + 'px';
        canvas.style.height = rect.height + 'px';

        console.log('Canvas size set to:', canvas.width, 'x', canvas.height);
        console.log('Video display size:', rect.width, 'x', rect.height);
        console.log('Video native size:', video.videoWidth, 'x', video.videoHeight);
      }
    },

    // 绘制MediaPipe特征点（增强版468点显示）
    drawLandmarks(landmarks) {
      if (!landmarks || landmarks.length === 0) {
        console.log('没有特征点数据');
        return;
      }

      // 验证特征点数量
      if (landmarks.length !== 468) {
        console.warn(`⚠️ 特征点数量异常: 收到${landmarks.length}个点，期望468个点`);
        console.warn('MediaPipe Face Mesh标准为468个特征点（索引0-467）');

        // 如果超过468个点，截取前468个
        if (landmarks.length > 468) {
          landmarks = landmarks.slice(0, 468);
          console.warn('已截取前468个特征点进行显示');
        }
      }

      // 如果未开启特征点显示，直接返回
      if (!this.showLandmarks) {
        return;
      }

      const canvas = this.$refs.emotionOverlay;
      const video = this.$refs.emotionVideo;
      if (!canvas || !video) {
        console.log('Canvas或Video元素不存在，跳过特征点绘制');
        return;
      }

      // 确保Canvas尺寸正确
      this.setupCanvas();

      const ctx = canvas.getContext('2d');

      // 更新当前特征点数据
      this.currentLandmarks = landmarks;



      // 清除画布
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // 根据类型获取要显示的点位索引
      const indicesToShow = this.getIndicesToShow();

      // 绘制不同类型的特征点
      this.drawLandmarksByType(ctx, landmarks, indicesToShow);
    },



    // 根据选择的类型获取要显示的点位索引
    getIndicesToShow() {
      return this.landmarkIndices[this.landmarkType] || this.landmarkIndices.all;
    },

    // 根据类型绘制特征点
    drawLandmarksByType(ctx, landmarks, indicesToShow) {
      const video = this.$refs.emotionVideo;

      // 计算视频在Canvas中的实际显示区域
      const videoRect = video.getBoundingClientRect();

      // 计算视频的实际显示尺寸（考虑object-fit: contain）
      const videoAspectRatio = video.videoWidth / video.videoHeight;
      const containerAspectRatio = videoRect.width / videoRect.height;

      let videoDisplayWidth, videoDisplayHeight, offsetX, offsetY;

      if (videoAspectRatio > containerAspectRatio) {
        // 视频更宽，以宽度为准
        videoDisplayWidth = videoRect.width;
        videoDisplayHeight = videoRect.width / videoAspectRatio;
        offsetX = 0;
        offsetY = (videoRect.height - videoDisplayHeight) / 2;
      } else {
        // 视频更高，以高度为准
        videoDisplayHeight = videoRect.height;
        videoDisplayWidth = videoRect.height * videoAspectRatio;
        offsetX = (videoRect.width - videoDisplayWidth) / 2;
        offsetY = 0;
      }

      indicesToShow.forEach((index) => {
        // 严格验证索引范围
        if (index < 0 || index >= 468 || index >= landmarks.length) {
          if (index >= 468) {
            console.warn(`⚠️ 特征点索引${index}超出MediaPipe Face Mesh范围（0-467）`);
          }
          return;
        }

        const landmark = landmarks[index];

        // 将MediaPipe归一化坐标转换为Canvas坐标
        // 考虑视频的实际显示区域和偏移
        const x = offsetX + (landmark.x * videoDisplayWidth);
        const y = offsetY + (landmark.y * videoDisplayHeight);

        // 根据点位类型设置不同颜色
        let color = this.getLandmarkColor(index);
        ctx.fillStyle = color;

        // 绘制圆点
        ctx.beginPath();
        ctx.arc(x, y, this.getLandmarkSize(index), 0, 2 * Math.PI);
        ctx.fill();
      });
    },

    // 获取特征点颜色
    getLandmarkColor(index) {
      // 眼部区域 - 绿色
      if (this.landmarkIndices.eyes.includes(index)) {
        return '#00FF00';
      }
      // 虹膜 - 红色
      if ([474, 475, 476, 477, 469, 470, 471, 472].includes(index)) {
        return '#FF0000';
      }
      // 嘴部 - 蓝色
      if ([61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318].includes(index)) {
        return '#0000FF';
      }
      // 鼻子 - 黄色
      if ([1, 2, 5, 4, 6, 19, 94, 125].includes(index)) {
        return '#FFFF00';
      }
      // 眉毛 - 紫色
      if ([70, 63, 105, 66, 107, 55, 65, 52, 53, 46].includes(index)) {
        return '#FF00FF';
      }
      // 面部轮廓 - 青色
      return '#00FFFF';
    },

    // 获取特征点大小
    getLandmarkSize(index) {
      // 虹膜点位更大
      if ([474, 475, 476, 477, 469, 470, 471, 472].includes(index)) {
        return 3;
      }
      // 重要特征点稍大
      if ([1, 33, 362, 61, 17].includes(index)) {
        return 2.5;
      }
      // 普通点位
      return 1.5;
    },





    // 清除画布上的特征点
    clearLandmarks() {
      const canvas = this.$refs.emotionOverlay;
      if (!canvas) {
        console.log('Canvas元素不存在，跳过清除操作');
        return;
      }
      const ctx = canvas.getContext('2d');
      ctx.clearRect(0, 0, canvas.width, canvas.height);
    },

    // 切换检测模式
    toggleDetectionMode() {
      this.isRealTimeMode = !this.isRealTimeMode;

      if (this.isRealTimeMode) {
        // 开启实时检测
        this.detectionInterval = setInterval(() => {
          this.captureAndAnalyze();
        }, this.detectionSettings.interval);
      } else {
        // 停止实时检测
        if (this.detectionInterval) {
          clearInterval(this.detectionInterval);
          this.detectionInterval = null;
        }
      }
    },

    // 更新会话统计
    updateSessionStats(result) {
      this.sessionStats.totalDetections++;

      // 计算平均注意力分数
      const currentAvg = this.sessionStats.avgAttentionScore;
      const newScore = result.attention_score || this.attentionScore;
      this.sessionStats.avgAttentionScore = Math.round(
        (currentAvg * (this.sessionStats.totalDetections - 1) + newScore) / this.sessionStats.totalDetections
      );

      // 计算会话时长
      if (this.sessionStats.startTime) {
        this.sessionStats.sessionDuration = Math.floor(
          (new Date() - this.sessionStats.startTime) / 1000 / 60
        ); // 分钟
      }
    },

    // 导出检测报告
    exportReport() {
      const report = {
        sessionInfo: {
          startTime: this.sessionStats.startTime,
          duration: this.sessionStats.sessionDuration,
          totalDetections: this.sessionStats.totalDetections,
          avgAttentionScore: this.sessionStats.avgAttentionScore
        },
        detectionHistory: this.detectionHistory,
        currentEmotion: this.currentEmotion,
        suggestions: this.currentSuggestions
      };

      const dataStr = JSON.stringify(report, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const link = document.createElement('a');
      link.href = URL.createObjectURL(dataBlob);
      link.download = `emotion_report_${new Date().toISOString().split('T')[0]}.json`;
      link.click();
    },

    // 清空历史记录
    clearHistory() {
      if (confirm('确定要清空所有检测历史吗？')) {
        this.detectionHistory = [];
        this.sessionStats = {
          totalDetections: 0,
          avgAttentionScore: 0,
          sessionDuration: 0,
          startTime: null
        };
      }
    },

    // 显示分析状态
    showAnalysisStatus(message, type = 'info') {
      // 可以在这里添加状态显示逻辑
      console.log(`[${type.toUpperCase()}] ${message}`);

      // 如果需要在界面上显示状态，可以设置一个状态变量
      this.analysisStatus = {
        message,
        type,
        timestamp: new Date()
      };

      // 自动清除状态消息
      setTimeout(() => {
        this.analysisStatus = null;
      }, 3000);
    },

    // 测试后端连接
    async testBackendConnection() {
      try {
        console.log('🔍 测试后端连接...');
        const response = await fetch('http://localhost:3001/api/health', {
          method: 'GET',
          signal: AbortSignal.timeout(5000)
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ 后端连接正常:', data);
          return true;
        } else {
          console.error('❌ 后端响应异常:', response.status);
          return false;
        }
      } catch (error) {
        console.error('❌ 后端连接失败:', error.message);
        return false;
      }
    },

    // 播放提醒声音
    playAlertSound() {
      try {
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);

        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);

        oscillator.start();
        oscillator.stop(audioContext.currentTime + 0.2);
      } catch (error) {
        console.log('声音提醒播放失败:', error);
      }
    },

    // 开始数据收集会话
    startDataCollection() {
      try {
        this.currentSession = dataCollectionService.startSession();
        this.sessionActive = true;

        // 重置会话统计
        this.sessionStats = {
          totalDetections: 0,
          avgAttentionScore: 0,
          sessionDuration: 0,
          startTime: new Date()
        };

        // 通知数据同步
        dataSync.notifySessionStart(this.currentSession);

        // 启动会话时长更新定时器
        this.startSessionTimer();

        console.log('📊 数据收集会话已开始:', this.currentSession.id);
      } catch (error) {
        console.error('开始数据收集失败:', error);
      }
    },

    // 记录检测数据
    recordDetectionData(result) {
      try {
        // 验证数据完整性
        if (!result || typeof result !== 'object') {
          console.warn('⚠️ 检测数据无效:', result);
          return;
        }

        console.log('📊 recordDetectionData - 接收到的数据:', result);
        console.log('📊 attention_level:', result.attention_level, '类型:', typeof result.attention_level);

        if (!this.sessionActive || !this.currentSession) {
          console.warn('⚠️ 没有活动的数据收集会话，尝试自动启动');
          this.startDataCollection();

          // 再次检查会话状态
          if (!this.sessionActive || !this.currentSession) {
            console.error('❌ 无法启动数据收集会话');
            return;
          }
        }

        console.log('📊 记录检测数据到会话:', this.currentSession.id);
        console.log('📊 数据内容:', result);

        // 记录到数据收集服务
        console.log('📊 准备调用 dataCollectionService.recordDetection');
        const recordResult = dataCollectionService.recordDetection(result);
        console.log('📊 dataCollectionService.recordDetection 返回结果:', recordResult);

        if (recordResult) {
          console.log('✅ 数据记录成功:', recordResult);

          // 更新本地会话统计
          this.updateSessionStats(result);

          // 通知数据同步
          dataSync.notifyDetectionUpdate(result);

          console.log('📈 检测数据已记录并同步');
        } else {
          console.error('❌ 数据记录失败');
        }
      } catch (error) {
        console.error('❌ 记录检测数据失败:', error);
      }
    },

    // 启动会话时长更新定时器
    startSessionTimer() {
      // 清除现有定时器
      if (this.sessionTimer) {
        clearInterval(this.sessionTimer);
      }

      // 每秒更新会话时长
      this.sessionTimer = setInterval(() => {
        if (this.sessionStats.startTime) {
          this.sessionStats.sessionDuration = Math.floor(
            (new Date() - this.sessionStats.startTime) / 1000 / 60
          ); // 分钟
        }
      }, 1000);
    },

    // 停止会话时长更新定时器
    stopSessionTimer() {
      if (this.sessionTimer) {
        clearInterval(this.sessionTimer);
        this.sessionTimer = null;
      }
    },

    // 结束数据收集会话
    endDataCollection() {
      try {
        if (!this.sessionActive || !this.currentSession) {
          console.warn('没有活动的数据收集会话');
          return;
        }

        // 结束会话并保存数据
        const completedSession = dataCollectionService.endSession();

        if (completedSession) {
          console.log('✅ 数据收集会话已结束:', completedSession.id);
          console.log('📊 会话统计:', {
            duration: completedSession.duration,
            detections: completedSession.statistics.totalDetections,
            avgAttention: completedSession.statistics.avgAttentionScore
          });

          // 显示会话完成提示
          this.showAnalysisStatus(
            `检测会话完成！持续${completedSession.duration}分钟，共检测${completedSession.statistics.totalDetections}次`,
            'success'
          );

          // 通知数据同步
          dataSync.notifySessionEnd(completedSession);
          dataSync.notifyStatisticsUpdate();
        }

        this.currentSession = null;
        this.sessionActive = false;

        // 停止会话时长更新定时器
        this.stopSessionTimer();

      } catch (error) {
        console.error('结束数据收集失败:', error);
      }
    },

    // 获取数据统计（供数据分析页面使用）
    getCollectedData() {
      return dataCollectionService.getAllData();
    },

    // 获取最近会话数据
    getRecentSessions(limit = 10) {
      return dataCollectionService.getRecentSessions(limit);
    },

    // 获取指定时间范围的数据
    getDataByRange(range = 'week') {
      return dataCollectionService.getDataByRange(range);
    },







    // 🤖 模拟检测数据（用于测试数据收集流程）
    startMockDetection() {
      console.log('🤖 startMockDetection 被调用');

      if (this.mockDetectionInterval) {
        console.log('🛑 停止模拟检测');
        clearInterval(this.mockDetectionInterval);
        this.mockDetectionInterval = null;
        alert('模拟检测已停止');
        return;
      }

      console.log('🤖 开始模拟检测数据...');
      alert('开始模拟检测，将生成30条测试数据');

      // 确保数据收集会话已启动
      console.log('🤖 检查数据收集会话状态:', this.sessionActive);
      if (!this.sessionActive) {
        console.log('🤖 启动数据收集会话...');
        this.startDataCollection();
        console.log('🤖 数据收集会话启动后状态:', this.sessionActive);
      } else {
        console.log('🤖 数据收集会话已经活跃');
      }

      let detectionCount = 0;
      this.mockDetectionInterval = setInterval(() => {
        detectionCount++;

        // 模拟不同的注意力状态
        const attentionStates = [
          { level: 0, status: '专注', score: 0.8 + Math.random() * 0.2 },
          { level: 1, status: '正常', score: 0.5 + Math.random() * 0.3 },
          { level: 2, status: '分心', score: 0.2 + Math.random() * 0.3 },
          { level: 3, status: '发呆', score: 0.0 + Math.random() * 0.2 }
        ];

        const randomState = attentionStates[Math.floor(Math.random() * attentionStates.length)];

        const mockResult = {
          attention_level: randomState.level,
          attention_status: randomState.status,
          attention_score: randomState.score,
          confidence: 0.7 + Math.random() * 0.3,
          eye_score: 0.6 + Math.random() * 0.4,
          face_score: 0.5 + Math.random() * 0.5,
          motion_score: 0.4 + Math.random() * 0.6,
          emotion: 'neutral',
          timestamp: new Date().toISOString()
        };

        console.log(`🤖 模拟检测 #${detectionCount}:`, mockResult);

        // 处理模拟的检测结果
        this.handleDetectionResult(mockResult);

        // 模拟30次检测后停止
        if (detectionCount >= 30) {
          console.log('🤖 模拟检测完成，共生成30条数据');
          clearInterval(this.mockDetectionInterval);
          this.mockDetectionInterval = null;
        }
      }, 1000); // 每秒一次检测

      console.log('🤖 模拟检测已启动，将生成30条测试数据');
    },

    // 468特征点控制方法
    toggleLandmarksDisplay() {
      this.showLandmarks = !this.showLandmarks;


      if (!this.showLandmarks) {
        this.clearLandmarks();
      }
    },

    setLandmarkType(type) {
      this.landmarkType = type;


      // 如果当前有特征点数据，重新绘制
      if (this.currentLandmarks.length > 0) {
        this.drawLandmarks(this.currentLandmarks);
      }
    },



    // 处理来自WebRTC的特征点数据
    handleLandmarksData(landmarksData) {
      if (!landmarksData || !Array.isArray(landmarksData)) {
        console.log('无效的特征点数据');
        return;
      }



      // 转换数据格式（如果需要）
      const formattedLandmarks = landmarksData.map((point, index) => ({
        x: point.x || point[0] || 0,
        y: point.y || point[1] || 0,
        z: point.z || point[2] || 0,
        index: index
      }));

      // 绘制特征点
      this.drawLandmarks(formattedLandmarks);
    },

    // 验证特征点索引配置
    validateLandmarkIndices() {
      console.log('🔍 验证MediaPipe Face Mesh特征点索引配置...');

      Object.keys(this.landmarkIndices).forEach(type => {
        const indices = this.landmarkIndices[type];
        const invalidIndices = indices.filter(index => index < 0 || index >= 468);

        if (invalidIndices.length > 0) {
          console.error(`❌ ${type}类型包含无效的特征点索引:`, invalidIndices);
          console.error('MediaPipe Face Mesh有效索引范围: 0-467');

          // 移除无效索引
          this.landmarkIndices[type] = indices.filter(index => index >= 0 && index < 468);
          console.warn(`✅ 已移除${type}类型中的无效索引，剩余${this.landmarkIndices[type].length}个有效索引`);
        } else {
          console.log(`✅ ${type}类型特征点索引验证通过，共${indices.length}个点`);
        }
      });
    },

    // 导出特征点数据
    exportLandmarksData() {
      if (this.currentLandmarks.length === 0) {
        alert('当前没有特征点数据可导出');
        return;
      }

      const data = {
        timestamp: new Date().toISOString(),
        landmarkType: this.landmarkType,
        totalPoints: this.currentLandmarks.length,
        landmarks: this.currentLandmarks
      };

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `landmarks_${Date.now()}.json`;
      a.click();
      URL.revokeObjectURL(url);


    }
  }
}
</script>

<style scoped>
.home-page {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.content-wrapper {
  display: flex;
  min-height: calc(100vh - 60px);
}

.main-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 情绪检测容器 */
.emotion-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.area-header {
  padding: 18px 25px;
  background: linear-gradient(to right, #e8efff, #f8f9fc);
  border-bottom: 1px solid #e3e6f0;
}

.area-header h3 {
  font-size: 20px;
  color: #3a5bbd;
  display: flex;
  align-items: center;
}

.area-header h3 i {
  margin-right: 12px;
  font-size: 22px;
  color: #4e73df;
}

/* 情绪检测内容 */
.emotion-content {
  display: flex;
  padding: 25px;
  gap: 30px;
}

/* 摄像头区域 */
.camera-section {
  flex: 1;
  min-width: 600px;
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.camera-container {
  position: relative;
  width: 100%;
  height: 70vh;
  min-height: 500px;
  border-radius: 20px;
  overflow: hidden;
  background-color: #f8f9fc;
  border: 3px dashed #e3e6f0;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.camera-container video {
  width: 100%;
  height: 100%;
  object-fit: contain;  /* 保持宽高比，不裁剪 */
  image-rendering: -webkit-optimize-contrast;  /* 优化图像渲染 */
  image-rendering: crisp-edges;  /* 保持边缘清晰 */
  image-rendering: pixelated;  /* 防止模糊 */
  transform: scale(1);  /* 确保不缩放 */
  filter: none;  /* 移除任何滤镜 */
}

.camera-container canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #7e7e7e;
  text-align: center;
  padding: 20px;
}

.video-placeholder i {
  font-size: 64px;
  margin-bottom: 20px;
  color: #e3e6f0;
}

.video-placeholder p {
  font-size: 16px;
  line-height: 1.5;
  font-weight: 500;
}

/* 摄像头控制按钮 */
.camera-controls {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.primary-btn, .secondary-btn {
  flex: 1;
  min-width: 140px;
  padding: 16px 24px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  transition: all 0.3s;
}

.primary-btn {
  background: linear-gradient(to right, #4e73df, #3a5bbd);
  color: white;
  box-shadow: 0 4px 10px rgba(71, 118, 230, 0.2);
}

.primary-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(71, 118, 230, 0.3);
}

.primary-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.secondary-btn {
  background: #6c757d;
  color: white;
}

.secondary-btn:hover:not(:disabled) {
  background: #5a6268;
  transform: translateY(-2px);
}

.capture-btn, .mode-btn {
  background: linear-gradient(to right, #1cc88a, #17a673);
  color: white;
  box-shadow: 0 4px 10px rgba(28, 200, 138, 0.2);
}

.capture-btn:hover:not(:disabled), .mode-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(28, 200, 138, 0.3);
}

.secondary-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 结果区域 */
.results-section {
  flex: 0 0 35%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

.result-card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e3e6f0;
}

.result-card h4 {
  margin-bottom: 15px;
  color: #4e4e4e;
  font-size: 16px;
  display: flex;
  align-items: center;
}

.result-card h4 i {
  margin-right: 8px;
  color: #4e73df;
}

/* 情绪状态 */
.emotion-status {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-weight: 500;
  color: #4e4e4e;
}

.status-value {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-text {
  font-weight: 600;
  color: #4e73df;
}

.status-level {
  font-size: 12px;
  color: #7f8c8d;
  margin-left: 8px;
  padding: 2px 6px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 4px;
  font-weight: 500;
}

.emotion-icon {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e8efff;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emotion-icon i {
  color: #4e73df;
  font-size: 16px;
}

/* 进度条 */
.progress-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.progress {
  width: 120px;
  height: 8px;
  background-color: #e3e6f0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(to right, #4e73df, #1cc88a);
  transition: width 0.5s ease;
}

.progress-text {
  font-weight: 600;
  color: #4e73df;
  min-width: 40px;
}

/* 历史记录 */
.history-list {
  max-height: 200px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f1f2f6;
}

.history-item:last-child {
  border-bottom: none;
}

.history-time {
  font-size: 12px;
  color: #7e7e7e;
}

.history-emotion {
  font-weight: 500;
  color: #4e4e4e;
}

.history-score {
  font-weight: 600;
  color: #4e73df;
}

.no-history {
  text-align: center;
  color: #7e7e7e;
  padding: 20px;
  font-size: 14px;
}

/* 会话统计样式 */
.session-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #7e7e7e;
  font-size: 14px;
}

.stat-value {
  font-weight: 600;
  color: #2c3e50;
  font-size: 16px;
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.action-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.3s;
  background: linear-gradient(to right, #4e73df, #3a5bbd);
  color: white;
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(78, 115, 223, 0.3);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.action-btn.danger {
  background: linear-gradient(to right, #e74a3b, #c0392b);
}

.action-btn.danger:hover:not(:disabled) {
  box-shadow: 0 4px 10px rgba(231, 74, 59, 0.3);
}

/* 建议列表 */
.suggestions {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background: #f8f9ff;
  border-radius: 6px;
  border-left: 3px solid #4e73df;
}

.suggestion-item i {
  color: #4e73df;
  font-size: 16px;
}

.suggestion-item span {
  font-size: 14px;
  color: #4e4e4e;
  line-height: 1.4;
}

.no-suggestions {
  text-align: center;
  color: #7e7e7e;
  padding: 20px;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 800px) {
  .emotion-content {
    flex-direction: column;
  }

  .camera-section {
    flex: none;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .main-area {
    padding: 15px;
  }

  .emotion-content {
    padding: 15px;
  }

  .camera-container {
    height: 50vh;
    min-height: 300px;
  }

  .camera-section {
    min-width: 100%;
  }

  .results-section {
    flex: none;
    max-width: none;
  }

  .camera-controls {
    flex-direction: column;
  }

  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

/* 连接状态指示器 */
.connection-status {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  backdrop-filter: blur(10px);
}

.status-indicator.connected {
  background: rgba(40, 167, 69, 0.9);
}

.status-indicator.connecting {
  background: rgba(255, 193, 7, 0.9);
}

/* AI详细评分样式 */
.detailed-scores {
  margin-top: 15px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.score-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.score-item:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateX(2px);
}

.score-name {
  min-width: 80px;
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

.score-bar {
  flex: 1;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, #ff6b6b, #feca57, #48dbfb, #0abde3);
  border-radius: 4px;
  transition: width 0.5s ease;
  position: relative;
}

.bar-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.score-num {
  min-width: 45px;
  font-size: 13px;
  font-weight: 600;
  color: #2c3e50;
  text-align: right;
  padding: 2px 6px;
  background: rgba(52, 152, 219, 0.1);
  border-radius: 4px;
}

/* AI设置样式 */
.ai-settings {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.setting-label {
  font-size: 14px;
  color: #333;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: 15px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #6c757d;
  color: white;
}

.toggle-btn.active {
  background: #28a745;
}

.toggle-btn:hover {
  transform: translateY(-1px);
}

.debug-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  background: #17a2b8;
  color: white;
}

.debug-btn:hover {
  background: #138496;
  transform: translateY(-1px);
}

.status-badge {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  background: #dc3545;
  color: white;
}

.status-badge.connected {
  background: #28a745;
}

/* 468特征点显示相关样式 */
.landmark-type-selector {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.type-btn {
  padding: 6px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  color: #64748b;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.type-btn:hover {
  border-color: #667eea;
  color: #667eea;
}

.type-btn.active {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.landmarks-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8fafc;
  border-radius: 6px;
  border-left: 3px solid #667eea;
}

.info-label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.info-value {
  font-weight: 600;
  color: #667eea;
  font-size: 14px;
}



/* 特征点画布样式增强 */
.emotion-overlay {
  position: absolute;
  top: 0;
  left: 0;
  pointer-events: none;
  z-index: 10;
}

/* 特征点控制面板 */
.landmarks-control-panel {
  position: absolute;
  top: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 8px;
  padding: 12px;
  color: white;
  font-size: 12px;
  z-index: 20;
}

.landmarks-control-panel .control-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.landmarks-control-panel .control-item:last-child {
  margin-bottom: 0;
}

.landmarks-control-panel button {
  padding: 4px 8px;
  border: 1px solid #667eea;
  border-radius: 4px;
  background: transparent;
  color: #667eea;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.landmarks-control-panel button:hover {
  background: #667eea;
  color: white;
}

.landmarks-control-panel button.active {
  background: #667eea;
  color: white;
}


</style>
