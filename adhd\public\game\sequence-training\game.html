<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数字字母倒序挑战</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #495057;
            overflow: hidden;
            position: relative;
        }

        /* 🔥 医院风格背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 30% 70%, rgba(255, 193, 7, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(0, 123, 255, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        .stars {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .star {
            position: absolute;
            background: white;
            border-radius: 50%;
            animation: twinkle 2s infinite;
        }

        @keyframes twinkle {
            0%, 100% { opacity: 0.3; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }

        .game-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            text-align: center;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            max-width: 900px;
            width: 95%;
            border: 2px solid rgba(255, 193, 7, 0.2);
            position: relative;
            z-index: 2;
            animation: slideIn 0.8s ease-out, containerPulse 4s ease-in-out infinite;
        }

        @keyframes containerPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.02); }
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .game-title {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #ffc107, #fd7e14, #e83e8c);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: glow 2s ease-in-out infinite alternate, titleWave 3s ease-in-out infinite;
            position: relative;
            display: inline-block;
        }

        .game-title::before {
            content: '🔢';
            position: absolute;
            left: -60px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2rem;
            animation: iconSpin 3s linear infinite;
        }

        .game-title::after {
            content: '🔤';
            position: absolute;
            right: -60px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2rem;
            animation: iconSpin 3s linear infinite reverse;
        }

        @keyframes glow {
            from { filter: drop-shadow(0 0 5px rgba(255, 193, 7, 0.5)); }
            to { filter: drop-shadow(0 0 20px rgba(255, 193, 7, 0.8)); }
        }

        @keyframes titleWave {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-5px); }
        }

        @keyframes iconSpin {
            from { transform: translateY(-50%) rotate(0deg); }
            to { transform: translateY(-50%) rotate(360deg); }
        }
        }

        .game-description {
            font-size: 1.3rem;
            margin-bottom: 30px;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 25px 0;
        }

        .stat-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #ffd700;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .stat-label {
            font-size: 0.9rem;
            margin-top: 5px;
            opacity: 0.8;
        }

        .round-progress {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin: 25px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .progress-text {
            font-size: 1.2rem;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ade80, #22c55e, #16a34a);
            border-radius: 6px;
            transition: width 0.8s ease;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .sequence-display {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: 50px;
            margin: 30px 0;
            min-height: 180px;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 3.5rem;
            font-weight: bold;
            letter-spacing: 20px;
            color: #ffd700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            position: relative;
            overflow: hidden;
        }

        .sequence-display.showing {
            animation: pulse 0.5s ease-in-out;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .timer-display {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            padding: 15px 25px;
            margin: 20px 0;
            color: white;
            font-weight: bold;
            font-size: 1.4rem;
            animation: countdown 1s ease-in-out infinite;
        }

        @keyframes countdown {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .input-container {
            margin: 30px 0;
            position: relative;
        }

        .sequence-input {
            background: rgba(255, 255, 255, 0.9);
            border: 3px solid rgba(255, 255, 255, 0.5);
            border-radius: 15px;
            padding: 20px 25px;
            font-size: 1.8rem;
            color: #333;
            text-align: center;
            width: 400px;
            letter-spacing: 10px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .sequence-input:focus {
            outline: none;
            border-color: #ffd700;
            box-shadow: 0 0 30px rgba(255, 215, 0, 0.5);
            transform: scale(1.05);
        }

        .sequence-input::placeholder {
            color: #666;
            font-weight: normal;
        }

        .btn {
            background: linear-gradient(45deg, #4ade80, #22c55e);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 30px;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            margin: 0 15px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(34, 197, 94, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(34, 197, 94, 0.4);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            background: #6b7280;
        }

        .feedback {
            margin: 25px 0;
            font-size: 1.5rem;
            font-weight: bold;
            min-height: 40px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .correct {
            color: #4ade80;
            animation: bounce 0.6s ease-in-out;
        }

        .incorrect {
            color: #ff6b6b;
            animation: shake 0.6s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            80% { transform: translateY(-5px); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .level-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1rem;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
    </style>
</head>
<body>
    <div class="stars" id="stars"></div>
    
    <div class="level-badge">
        关卡 <span id="levelDisplay">1</span>
    </div>

    <div class="game-container">
        <h1 class="game-title">🔄 数字字母倒序挑战</h1>
        <p class="game-description">记住序列5秒后按倒序排列，挑战你的工作记忆！</p>

        <div class="stats-container">
            <div class="stat-item">
                <div class="stat-value" id="score">0</div>
                <div class="stat-label">得分</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="streak">0</div>
                <div class="stat-label">连击</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="accuracy">0%</div>
                <div class="stat-label">正确率</div>
            </div>
        </div>

        <div class="round-progress">
            <div class="progress-text" id="progressText">准备开始 - 第1轮/共10轮</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="sequence-display" id="sequenceDisplay">
            点击开始游戏
        </div>

        <div class="timer-display" id="timerDisplay" style="display: none;">
            记忆时间: <span id="countdown">5</span>秒
        </div>

        <div class="input-container">
            <input type="text" class="sequence-input" id="sequenceInput" 
                   placeholder="请按倒序输入..." disabled>
        </div>

        <div class="feedback" id="feedback"></div>

        <div class="game-controls">
            <button class="btn" id="startBtn" onclick="startGame()">🚀 开始游戏</button>
            <button class="btn" id="submitBtn" onclick="checkAnswer()" disabled>✅ 提交答案</button>
            <button class="btn" id="nextBtn" onclick="nextRound()" disabled style="display: none;">➡️ 下一题</button>
        </div>
    </div>

    <script>
        // 创建星空背景
        function createStars() {
            const starsContainer = document.getElementById('stars');
            for (let i = 0; i < 100; i++) {
                const star = document.createElement('div');
                star.className = 'star';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.width = star.style.height = Math.random() * 3 + 1 + 'px';
                star.style.animationDelay = Math.random() * 2 + 's';
                starsContainer.appendChild(star);
            }
        }

        // 游戏状态
        let gameState = {
            score: 0,
            streak: 0,
            level: 1,
            gameActive: false,
            currentRound: 1,
            totalRounds: 10,
            currentSequence: '',
            correctSequence: '',
            totalQuestions: 0,
            correctAnswers: 0,
            showingSequence: false
        };

        // 生成序列
        function generateSequence() {
            const round = gameState.currentRound;
            // 从3个开始，逐渐递增到6个
            const length = Math.min(3 + Math.floor((round - 1) / 2), 6);
            let sequence = [];

            // 数字字母混合
            for (let i = 0; i < length; i++) {
                if (Math.random() < 0.5) {
                    sequence.push(Math.floor(Math.random() * 9) + 1);
                } else {
                    sequence.push(String.fromCharCode(65 + Math.floor(Math.random() * 26)));
                }
            }

            gameState.currentSequence = sequence.join(' ');
            gameState.correctSequence = [...sequence].reverse().join(' ');
        }

        // 开始游戏
        function startGame() {
            gameState.gameActive = true;
            gameState.score = 0;
            gameState.streak = 0;
            gameState.level = 1;
            gameState.currentRound = 1;
            gameState.totalQuestions = 0;
            gameState.correctAnswers = 0;

            document.getElementById('startBtn').disabled = true;
            nextRound();
        }

        // 下一轮
        function nextRound() {
            if (gameState.currentRound > gameState.totalRounds) {
                endGame();
                return;
            }

            generateSequence();
            gameState.showingSequence = true;

            document.getElementById('progressText').textContent =
                `第${gameState.currentRound}轮/共${gameState.totalRounds}轮 - 记住序列并倒序输入`;

            const sequenceDisplay = document.getElementById('sequenceDisplay');
            sequenceDisplay.textContent = gameState.currentSequence;
            sequenceDisplay.classList.add('showing');

            document.getElementById('sequenceInput').value = '';
            document.getElementById('feedback').textContent = '';
            document.getElementById('nextBtn').style.display = 'none';

            document.getElementById('submitBtn').disabled = true;
            document.getElementById('sequenceInput').disabled = true;

            showSequenceTimer();
        }

        // 显示序列计时器
        function showSequenceTimer() {
            const timerDisplay = document.getElementById('timerDisplay');
            const countdown = document.getElementById('countdown');

            timerDisplay.style.display = 'block';
            let timeLeft = 5;
            countdown.textContent = timeLeft;

            const timer = setInterval(() => {
                timeLeft--;
                if (timeLeft > 0) {
                    countdown.textContent = timeLeft;
                } else {
                    clearInterval(timer);
                    hideSequenceAndStartInput();
                }
            }, 1000);
        }

        // 隐藏序列并开始输入
        function hideSequenceAndStartInput() {
            gameState.showingSequence = false;

            const sequenceDisplay = document.getElementById('sequenceDisplay');
            sequenceDisplay.textContent = '请按倒序输入刚才看到的序列';
            sequenceDisplay.classList.remove('showing');
            document.getElementById('timerDisplay').style.display = 'none';

            document.getElementById('submitBtn').disabled = false;
            document.getElementById('sequenceInput').disabled = false;
            document.getElementById('sequenceInput').focus();

            updateDisplay();
        }

        // 检查答案
        function checkAnswer() {
            const userAnswer = document.getElementById('sequenceInput').value.trim().toUpperCase();
            const correctAnswer = gameState.correctSequence.toUpperCase();
            const feedback = document.getElementById('feedback');

            gameState.totalQuestions++;

            if (userAnswer === correctAnswer) {
                const points = 10 * gameState.currentRound;
                gameState.score += points;
                gameState.streak++;
                gameState.correctAnswers++;
                feedback.textContent = `🎉 正确！+${points}分`;
                feedback.className = 'feedback correct';
                createParticles('success');
            } else {
                gameState.streak = 0;
                feedback.textContent = `❌ 错误！正确答案是: ${gameState.correctSequence}`;
                feedback.className = 'feedback incorrect';
                createParticles('error');
            }

            gameState.currentRound++;

            document.getElementById('submitBtn').disabled = true;
            document.getElementById('sequenceInput').disabled = true;
            document.getElementById('nextBtn').style.display = 'inline-block';
            document.getElementById('nextBtn').disabled = false;

            updateDisplay();
        }

        // 创建粒子效果
        function createParticles(type) {
            const container = document.querySelector('.game-container');
            for (let i = 0; i < 10; i++) {
                const particle = document.createElement('div');
                particle.className = `particle ${type}-particle`;
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.width = particle.style.height = Math.random() * 8 + 4 + 'px';
                container.appendChild(particle);

                setTimeout(() => {
                    particle.remove();
                }, 1000);
            }
        }

        // 更新显示
        function updateDisplay() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('streak').textContent = gameState.streak;
            document.getElementById('levelDisplay').textContent = gameState.level;

            const accuracy = gameState.totalQuestions > 0 ?
                Math.round((gameState.correctAnswers / gameState.totalQuestions) * 100) : 0;
            document.getElementById('accuracy').textContent = accuracy + '%';

            const progress = ((gameState.currentRound - 1) / gameState.totalRounds) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 结束游戏
        function endGame() {
            gameState.gameActive = false;

            const accuracy = gameState.totalQuestions > 0 ?
                Math.round((gameState.correctAnswers / gameState.totalQuestions) * 100) : 0;

            document.getElementById('sequenceDisplay').textContent = '🎊 游戏结束！';
            document.getElementById('progressText').textContent =
                `游戏完成！正确率: ${accuracy}% (${gameState.correctAnswers}/${gameState.totalQuestions})`;
            document.getElementById('feedback').textContent = `🏆 最终得分: ${gameState.score}分`;
            document.getElementById('feedback').className = 'feedback';

            document.getElementById('startBtn').disabled = false;
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('sequenceInput').disabled = true;
            document.getElementById('nextBtn').style.display = 'none';
            document.getElementById('timerDisplay').style.display = 'none';

            createParticles('success');

            if (window.opener) {
                window.opener.postMessage({
                    type: 'gameEnd',
                    score: gameState.score
                }, '*');
            }
        }

        // 键盘事件
        document.getElementById('sequenceInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !document.getElementById('submitBtn').disabled) {
                checkAnswer();
            }
        });

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            createStars();
            updateDisplay();
        });
    </script>
</body>
</html>
