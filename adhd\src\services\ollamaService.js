/**
 * Ollama本地大模型服务
 * 用于与本地部署的Ollama API进行交互
 */

class OllamaService {
  constructor() {
    // Ollama默认运行在localhost:11434
    this.baseURL = 'http://localhost:11434';
    this.defaultModel = 'qwen2.5:14b'; // 使用qwen2.5:14b模型
  }

  /**
   * 检查Ollama服务是否可用
   */
  async checkHealth() {
    try {
      const response = await fetch(`${this.baseURL}/api/tags`);
      return response.ok;
    } catch (error) {
      console.error('Ollama服务不可用:', error);
      return false;
    }
  }

  /**
   * 获取可用的模型列表
   */
  async getModels() {
    try {
      const response = await fetch(`${this.baseURL}/api/tags`);
      if (!response.ok) {
        throw new Error('获取模型列表失败');
      }
      const data = await response.json();
      return data.models || [];
    } catch (error) {
      console.error('获取模型列表失败:', error);
      return [];
    }
  }

  /**
   * 生成康复计划
   * @param {Object} userProfile - 用户信息
   * @param {string} userProfile.name - 姓名
   * @param {string} userProfile.age - 年龄段 (child/teen/adult)
   * @param {string} userProfile.adhdType - ADHD类型
   * @param {Object} severity - 症状严重程度
   * @param {Array} focusAreas - 重点康复领域
   */
  async generateRehabilitationPlan(userProfile, severity, focusAreas) {
    const prompt = this.buildRehabilitationPrompt(userProfile, severity, focusAreas);
    
    try {
      const response = await this.generateCompletion(prompt);
      return this.parseRehabilitationResponse(response);
    } catch (error) {
      console.error('生成康复计划失败:', error);
      throw new Error('生成康复计划失败，请检查Ollama服务是否正常运行');
    }
  }

  /**
   * 构建康复计划生成的提示词
   */
  buildRehabilitationPrompt(userProfile, severity, focusAreas) {
    const ageMap = {
      'child': '儿童(6-12岁)',
      'teen': '青少年(13-17岁)', 
      'adult': '成人(18岁以上)'
    };

    const typeMap = {
      'inattentive': '注意力缺陷为主型',
      'hyperactive': '多动冲动为主型',
      'combined': '混合型'
    };

    const focusMap = {
      'academic': '学业表现',
      'social': '社交能力',
      'emotion': '情绪管理',
      'executive': '执行功能',
      'daily': '日常生活'
    };

    const focusAreasText = focusAreas.map(area => focusMap[area] || area).join('、');

    return `你是一位专业的ADHD康复治疗师，请为以下患者制定详细的个性化康复计划：

患者信息：
- 姓名：${userProfile.name}
- 年龄段：${ageMap[userProfile.age] || userProfile.age}
- ADHD类型：${typeMap[userProfile.adhdType] || userProfile.adhdType}
- 注意力问题严重程度：${severity.attention}/10
- 多动/冲动严重程度：${severity.hyperactivity}/10
- 情绪调节问题严重程度：${severity.emotion}/10
- 重点康复领域：${focusAreasText}

请按照以下JSON格式返回康复计划，确保内容专业、实用且针对性强：

{
  "planTitle": "康复计划标题",
  "planDuration": "建议执行周期",
  "planOverview": "计划概述(200-300字)",
  "cognitiveTraining": [
    "认知训练项目1",
    "认知训练项目2",
    "认知训练项目3"
  ],
  "behavioralStrategies": [
    "行为策略1", 
    "行为策略2",
    "行为策略3"
  ],
  "lifestyleAdjustments": [
    "生活方式调整1",
    "生活方式调整2", 
    "生活方式调整3"
  ],
  "socialSupport": [
    "社会支持建议1",
    "社会支持建议2",
    "社会支持建议3"
  ],
  "resources": [
    "推荐资源1",
    "推荐资源2",
    "推荐资源3"
  ],
  "milestones": [
    "第1-2周目标",
    "第3-4周目标", 
    "第5-6周目标",
    "第7-8周目标"
  ]
}

要求：
1. 内容必须基于循证医学和ADHD最佳实践
2. 考虑患者的年龄特点和ADHD类型
3. 根据症状严重程度调整干预强度
4. 重点关注用户选择的康复领域
5. 提供具体可操作的建议
6. 使用简洁明了的中文表达
7. 确保返回有效的JSON格式

请直接返回JSON格式的康复计划，不要包含其他解释文字。`;
  }

  /**
   * 调用Ollama生成API
   */
  async generateCompletion(prompt, model = this.defaultModel) {
    const response = await fetch(`${this.baseURL}/api/generate`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model,
        prompt: prompt,
        stream: false,
        options: {
          temperature: 0.7,
          top_p: 0.9,
          max_tokens: 2000
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Ollama API请求失败: ${response.status}`);
    }

    const data = await response.json();
    return data.response;
  }

  /**
   * 解析康复计划响应
   */
  parseRehabilitationResponse(response) {
    try {
      // 尝试提取JSON部分
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('响应中未找到有效的JSON格式');
      }

      const planData = JSON.parse(jsonMatch[0]);
      
      // 验证必要字段
      const requiredFields = [
        'planTitle', 'planDuration', 'planOverview',
        'cognitiveTraining', 'behavioralStrategies', 
        'lifestyleAdjustments', 'socialSupport', 'resources'
      ];

      for (const field of requiredFields) {
        if (!planData[field]) {
          throw new Error(`缺少必要字段: ${field}`);
        }
      }

      return planData;
    } catch (error) {
      console.error('解析康复计划响应失败:', error);
      console.log('原始响应:', response);
      
      // 返回默认的康复计划结构
      return this.getDefaultPlan();
    }
  }

  /**
   * 获取默认康复计划（当AI生成失败时使用）
   */
   
  /**
   * 智能分析量表结果
   * @param {Object} scaleData - 量表数据
   * @param {Array} answers - 用户答案数组
   * @param {Object} userInfo - 用户信息
   * @param {Array} questionsWithAnswers - 题目和答案详情
   */
  async analyzeScaleResults(scaleData, answers, userInfo = {}, questionsWithAnswers = null) {
    const prompt = this.buildScaleAnalysisPrompt(scaleData, answers, userInfo, questionsWithAnswers);

    try {
      const response = await this.generateCompletion(prompt);
      return this.parseScaleAnalysisResponse(response);
    } catch (error) {
      console.error('量表分析失败:', error);
      throw new Error('量表分析失败，请检查Ollama服务是否正常运行');
    }
  }

  /**
   * 构建量表分析的提示词
   */
  buildScaleAnalysisPrompt(scaleData, answers, userInfo, questionsWithAnswers = null) {
    const scaleTypeMap = {
      'depression': 'PHQ-A青少年抑郁症筛查量表',
      'anxiety': 'GAD-7广泛性焦虑障碍量表',
      'adhd': 'SNAP-IV ADHD量表',
      'sleep': '睡眠质量评估量表'
    };

    const scaleName = scaleTypeMap[scaleData.type] || scaleData.title || '心理健康筛查量表';
    const totalScore = answers.reduce((sum, score) => sum + score, 0);
    const maxScore = answers.length * 3; // 假设最高分为3分制
    const scorePercentage = Math.round((totalScore / maxScore) * 100);

    // 构建问题和答案对照 - 优先使用详细的题目信息
    let questionAnswerPairs = '';
    if (questionsWithAnswers && questionsWithAnswers.length > 0) {
      questionAnswerPairs = questionsWithAnswers.map((item, index) => {
        const categoryText = item.category ? `[${item.category}] ` : '';
        return `${index + 1}. ${categoryText}${item.questionText} - 答案: ${item.answer} 分`;
      }).join('\n');
    } else if (scaleData.questions) {
      questionAnswerPairs = scaleData.questions.map((q, index) =>
        `${index + 1}. ${q.text || q} - 答案: ${answers[index]} 分`
      ).join('\n');
    } else if (scaleData.attentionQuestions && scaleData.hyperactivityQuestions) {
      // ADHD量表特殊处理
      const attentionAnswers = answers.slice(0, scaleData.attentionQuestions.length);
      const hyperactivityAnswers = answers.slice(scaleData.attentionQuestions.length);

      questionAnswerPairs = '注意力缺陷相关问题:\n' +
        scaleData.attentionQuestions.map((q, index) =>
          `${index + 1}. ${q.text || q} - 答案: ${attentionAnswers[index]} 分`
        ).join('\n') +
        '\n\n多动/冲动相关问题:\n' +
        scaleData.hyperactivityQuestions.map((q, index) =>
          `${index + 1}. ${q.text || q} - 答案: ${hyperactivityAnswers[index]} 分`
        ).join('\n');
    }

    // 构建详细信息部分
    let detailedInfoSection = '';
    if (userInfo.detailedInfo) {
      const info = userInfo.detailedInfo;
      detailedInfoSection = `

额外详细信息：
- 家庭结构：${info.familyStructure || '未提供'}
- 孩子发病史：${info.medicalHistory || '未提供'}
- 日常行为表现：${info.behavioralObservation || '未提供'}
- 学习情况：${info.academicPerformance || '未提供'}`;
    }

    return `你是一位专业的心理健康评估专家，请基于以下量表结果生成详细的临床分析报告：

【患者基本信息】
- 姓名：${userInfo.name || '未提供'}
- 年龄：${userInfo.age || '未提供'}
- 性别：${userInfo.gender || '未提供'}
- 评估工具：${scaleName}
- 填写者：${userInfo.filledBy || '本人'}
- 完成时间：${userInfo.completionTime || '未记录'}${detailedInfoSection}

【量表评分结果】
- 总分：${totalScore}/${maxScore} 分
- 得分百分比：${scorePercentage}%
- 量表类型：${scaleData.type}
- 适用年龄：${scaleData.ageRange || '未指定'}

【详细答题情况】
${questionAnswerPairs}

请按照以下JSON格式返回专业的分析结果：

{
  "totalScore": ${totalScore},
  "maxScore": ${maxScore},
  "scorePercentage": ${scorePercentage},
  "riskLevel": "低风险/中等风险/高风险",
  "severityLevel": "正常/轻度/中度/重度",
  "interpretation": "详细的结果解读(300-500字)",
  "keyScoreAnalysis": [
    {
      "questionNumber": 1,
      "symptomDimension": "症状维度名称",
      "score": "得分/满分",
      "clinicalSignificance": "临床意义说明",
      "patientDescription": "患者具体描述或表现"
    }
  ],
  "clinicalAnalysis": [
    "基于第X题得分X分以及患者提供的XX信息，我推测患者存在XX症状特点",
    "基于第X题得分X分，提示XX维度异常",
    "结合患者提供的XX信息并结合第X题得分，我认为患者有XX特点"
  ],
  "crossAnalysisFindings": {
    "symptomClusters": [
      {
        "name": "核心症状群",
        "questions": [1, 2],
        "totalScore": "得分/满分",
        "description": "症状群描述"
      }
    ],
    "riskSignals": [
      "需要重点关注的危险信号"
    ],
    "protectiveFactors": [
      "患者的积极保护因素"
    ]
  },
  "preliminaryClinicalJudgment": {
    "primaryDiagnosis": "基于量表结果符合的诊断",
    "diagnosticCode": "DSM-5或ICD-11编码",
    "differentialDiagnosis": ["需要排除的疾病"],
    "priorityIssues": ["建议优先处理的问题"]
  },
  "familyContextAnalysis": {
    "familyStructureImpact": "家庭结构对症状的影响分析",
    "medicalHistoryRelevance": "发病史与当前表现的关联",
    "behavioralPatterns": "行为表现的模式分析",
    "academicCorrelation": "学习情况与症状的相关性"
  },
  "treatmentRecommendations": {
    "urgencyLevel": "⭐⭐⭐（根据风险等级评定1-5星）",
    "primaryApproach": [
      "具体的首选治疗方案",
      "行为干预建议"
    ],
    "medicationConsiderations": "药物治疗建议（如适用）",
    "differentialDiagnosisPoints": [
      "鉴别诊断要点"
    ],
    "followUpPlan": "随访建议和时间安排"
  },
  "confidence": 0.85,
  "disclaimer": "本评估仅供参考，不能替代专业医学诊断。如有疑虑，请咨询专业医疗机构。"
}

【重要分析要求】：
1. **具体题目分析**：必须具体说明每个结论基于哪道题的得分（如：基于第3题得分3分）
2. **详细信息整合**：结合患者提供的家庭结构、发病史、行为表现、学习情况等详细信息
3. **症状集群分析**：将相关题目组合分析，形成症状群（如：核心症状群题1+2得分6/6）
4. **临床意义说明**：每个高分题目都要说明其临床意义和风险提示
5. **证据链条**：每个结论都要有明确的证据支撑（题目得分+患者信息）
6. **专业术语**：使用标准的医学诊断术语和分类编码
7. **风险分级**：根据得分情况进行紧急度评估（1-5星）
8. **可操作建议**：提供具体的、医生可直接参考的诊疗建议
9. **鉴别诊断**：基于症状特点提出需要排除的疾病
10. **发展特点**：考虑儿童青少年的发展阶段特点
11. **家长填写**：如果是家长代填，要考虑观察者偏差
12. **JSON格式**：确保返回完整有效的JSON格式数据

请直接返回JSON格式的分析结果，不要包含其他解释文字。`;
  }

  /**
   * 解析量表分析响应
   */
  parseScaleAnalysisResponse(response) {
    try {
      // 尝试提取JSON部分
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (!jsonMatch) {
        throw new Error('响应中未找到有效的JSON格式');
      }

      const analysisData = JSON.parse(jsonMatch[0]);

      // 验证必要字段
      const requiredFields = [
        'totalScore', 'riskLevel', 'severityLevel', 'interpretation',
        'recommendations', 'confidence'
      ];

      for (const field of requiredFields) {
        if (analysisData[field] === undefined) {
          throw new Error(`缺少必要字段: ${field}`);
        }
      }

      return analysisData;
    } catch (error) {
      console.error('解析量表分析响应失败:', error);
      console.log('原始响应:', response);

      // 返回默认的分析结果
      return this.getDefaultAnalysis();
    }
  }

   /**
   * 生成康复建议
   * @param {string} question - 用户问题
   * @param {Object} context - 上下文信息
   */
  async generateAdvice(question, context = {}) {
    const prompt = `作为ADHD康复专家，请回答以下问题：

问题：${question}

${context.userProfile ? `用户背景：${JSON.stringify(context.userProfile)}` : ''}

请提供专业、实用的建议，语言简洁明了。`;

    try {
      const response = await this.generateCompletion(prompt);
      return response;
    } catch (error) {
      console.error('生成建议失败:', error);
      return '抱歉，当前无法生成建议。请稍后重试或咨询专业医疗人员。';
    }
  }
}

// 创建单例实例
const ollamaService = new OllamaService();

export default ollamaService;
