{"doc": " 角色表 sys_role\n\n <AUTHOR> Li\n", "fields": [{"name": "roleId", "doc": " 角色ID\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": " 角色名称\n"}, {"name": "<PERSON><PERSON><PERSON>", "doc": " 角色权限\n"}, {"name": "roleSort", "doc": " 角色排序\n"}, {"name": "dataScope", "doc": " 数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限 6：部门及以下或本人数据权限）\n"}, {"name": "menu<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc": " 菜单树选择项是否关联显示（ 0：父子不互相关联显示 1：父子互相关联显示）\n"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "doc": " 部门树选择项是否关联显示（0：父子不互相关联显示 1：父子互相关联显示 ）\n"}, {"name": "status", "doc": " 角色状态（0正常 1停用）\n"}, {"name": "delFlag", "doc": " 删除标志（0代表存在 1代表删除）\n"}, {"name": "remark", "doc": " 备注\n"}], "enumConstants": [], "methods": [], "constructors": []}