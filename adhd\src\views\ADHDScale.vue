<template>
  <div class="page-container">
    <!-- 公共头部组件 -->
    <AppHeader
      :user="currentUser"
      current-route="adhd-scale"
      @search="handleSearch"
      @show-notifications="showNotifications"
      @show-messages="showMessages"
      @show-settings="showSettings"
      @show-user-menu="showUserMenu"
    />

    <!-- 内容包装区 -->
    <div class="content-wrapper">
      <!-- 公共侧边栏组件 -->
      <AppSidebar
        current-route="adhd-scale"
        :expanded="navExpanded"
        @expand="expandNav"
        @collapse="collapseNav"
      />

      <!-- 主内容区 -->
      <div class="main-area">
        <div class="page-main-container">
          <!-- 量表类型选择页面 -->
          <div v-if="showScaleSelectionPage" class="scale-container">
            <div class="area-header">
              <h3><i class="fas fa-clipboard-check"></i> 心理健康筛查量表 </h3>
            </div>

          <div class="scale-selection-content">
            <div class="scale-intro">
              <h2>欢迎使用心理健康筛查量表</h2>
              <p>我们提供多种专业的心理健康评估工具，涵盖抑郁、焦虑、ADHD、自闭症、创伤和睡眠等多个领域。请选择您需要的评估类型。</p>
            </div>

            <div class="scale-categories">
              <!-- 情绪障碍筛查 -->
              <div class="category-section">
                <h3><i class="fas fa-heart-broken"></i> 情绪障碍筛查</h3>
                <div class="scale-cards">
                  <div class="scale-card" @click="selectScale('phq-a')">
                    <div class="scale-icon">
                      <i class="fas fa-sad-tear"></i>
                    </div>
                    <h4>PHQ-A 青少年抑郁症筛查</h4>
                    <p>适用年龄：12-18岁</p>
                    <div class="scale-description">
                      <p>评估青少年抑郁症状的专业工具</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('cdi-2')">
                    <div class="scale-icon">
                      <i class="fas fa-child"></i>
                    </div>
                    <h4>CDI-2 儿童抑郁量表</h4>
                    <p>适用年龄：7-17岁</p>
                    <div class="scale-description">
                      <p>儿童抑郁症状评估的金标准工具</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('sds')">
                    <div class="scale-icon">
                      <i class="fas fa-frown"></i>
                    </div>
                    <h4>SDS 抑郁自评量表</h4>
                    <p>适用年龄：成人</p>
                    <div class="scale-description">
                      <p>Zung抑郁自评量表，广泛应用的抑郁筛查工具</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('madrs')">
                    <div class="scale-icon">
                      <i class="fas fa-sad-tear"></i>
                    </div>
                    <h4>MADRS 蒙哥马利量表</h4>
                    <p>适用年龄：≥15岁</p>
                    <div class="scale-description">
                      <p>蒙哥马利抑郁评定量表，临床抑郁症评估工具</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 焦虑障碍筛查 -->
              <div class="category-section">
                <h3><i class="fas fa-brain"></i> 焦虑障碍筛查</h3>
                <div class="scale-cards">
                  <div class="scale-card" @click="selectScale('scared')">
                    <div class="scale-icon">
                      <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h4>SCARED 儿童焦虑筛查</h4>
                    <p>适用年龄：8-18岁</p>
                    <div class="scale-description">
                      <p>儿童青少年焦虑症状综合评估</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('gad-7')">
                    <div class="scale-icon">
                      <i class="fas fa-dizzy"></i>
                    </div>
                    <h4>GAD-7 广泛性焦虑障碍</h4>
                    <p>适用年龄：≥12岁</p>
                    <div class="scale-description">
                      <p>广泛性焦虑症快速筛查工具</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('sas')">
                    <div class="scale-icon">
                      <i class="fas fa-heartbeat"></i>
                    </div>
                    <h4>SAS 焦虑自评量表</h4>
                    <p>适用年龄：成人</p>
                    <div class="scale-description">
                      <p>Zung焦虑自评量表，评估焦虑症状的经典工具</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('spai-c')">
                    <div class="scale-icon">
                      <i class="fas fa-user-friends"></i>
                    </div>
                    <h4>SPAI-C 社交恐惧量表</h4>
                    <p>适用年龄：8-14岁</p>
                    <div class="scale-description">
                      <p>儿童社交恐惧症评估量表</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('social-anxiety')">
                    <div class="scale-icon">
                      <i class="fas fa-users"></i>
                    </div>
                    <h4>儿童社交焦虑量表</h4>
                    <p>适用年龄：8-18岁</p>
                    <div class="scale-description">
                      <p>评估儿童青少年社交焦虑水平</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- ADHD筛查 -->
              <div class="category-section">
                <h3><i class="fas fa-child"></i> ADHD筛查</h3>
                <div class="scale-cards">
                  <div class="scale-card" @click="selectScale('conners-parent')">
                    <div class="scale-icon">
                      <i class="fas fa-users"></i>
                    </div>
                    <h4>Conners 父母用量表</h4>
                    <p>适用年龄：3-16岁</p>
                    <div class="scale-description">
                      <p>康氏儿童行为评定量表，筛查多动症的广泛工具</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('conners-teacher')">
                    <div class="scale-icon">
                      <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                    <h4>Conners 教师用量表</h4>
                    <p>适用年龄：3-16岁</p>
                    <div class="scale-description">
                      <p>教师评估版康氏量表，学校环境行为评估</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('conners-teacher-short')">
                    <div class="scale-icon">
                      <i class="fas fa-clipboard-list"></i>
                    </div>
                    <h4>Conners 教师简化版</h4>
                    <p>适用年龄：3-16岁</p>
                    <div class="scale-description">
                      <p>快速筛查版，10题简化评估工具</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('attention-level')">
                    <div class="scale-icon">
                      <i class="fas fa-eye"></i>
                    </div>
                    <h4>儿童注意力水平测评</h4>
                    <p>适用年龄：5岁以上</p>
                    <div class="scale-description">
                      <p>中国儿童注意力水平专业测评量表</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('sensory-integration')">
                    <div class="scale-icon">
                      <i class="fas fa-brain"></i>
                    </div>
                    <h4>感觉统合能力发展评定</h4>
                    <p>适用年龄：4-12岁</p>
                    <div class="scale-description">
                      <p>评估儿童感觉统合失调问题</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('snap-iv')">
                    <div class="scale-icon">
                      <i class="fas fa-running"></i>
                    </div>
                    <h4>SNAP-IV ADHD量表</h4>
                    <p>适用年龄：6-12岁</p>
                    <div class="scale-description">
                      <p>儿童ADHD症状快速筛查工具</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('asrs')">
                    <div class="scale-icon">
                      <i class="fas fa-user-tie"></i>
                    </div>
                    <h4>ASRS 成人ADHD量表</h4>
                    <p>适用年龄：≥18岁</p>
                    <div class="scale-description">
                      <p>成人ADHD症状自评量表</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 创伤与压力相关量表 -->
              <div class="category-section">
                <h3><i class="fas fa-shield-alt"></i> 创伤与压力评估</h3>
                <div class="scale-cards">
                  <div class="scale-card" @click="selectScale('cries-13')">
                    <div class="scale-icon">
                      <i class="fas fa-heart-broken"></i>
                    </div>
                    <h4>CRIES-13 儿童创伤事件量表</h4>
                    <p>适用年龄：8-16岁</p>
                    <div class="scale-description">
                      <p>评估儿童创伤后应激障碍症状</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('aces')">
                    <div class="scale-icon">
                      <i class="fas fa-child"></i>
                    </div>
                    <h4>ACEs 童年不良经历量表</h4>
                    <p>适用年龄：全年龄段</p>
                    <div class="scale-description">
                      <p>评估童年期不良经历对健康的影响</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 行为问题评估 -->
              <div class="category-section">
                <h3><i class="fas fa-clipboard-check"></i> 行为问题评估</h3>
                <div class="scale-cards">
                  <div class="scale-card" @click="selectScale('cbcl')">
                    <div class="scale-icon">
                      <i class="fas fa-child"></i>
                    </div>
                    <h4>CBCL 儿童行为量表</h4>
                    <p>适用年龄：4-18岁</p>
                    <div class="scale-description">
                      <p>Achenbach儿童行为量表，评估行为问题和社会能力</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('social-living-skills')">
                    <div class="scale-icon">
                      <i class="fas fa-users-cog"></i>
                    </div>
                    <h4>社会生活能力量表</h4>
                    <p>适用年龄：0-15岁</p>
                    <div class="scale-description">
                      <p>评估从婴儿到初中生的社会生活能力发展</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 自闭谱系障碍筛查 -->
              <div class="category-section">
                <h3><i class="fas fa-puzzle-piece"></i> 自闭谱系障碍筛查</h3>
                <div class="scale-cards">
                  <div class="scale-card" @click="selectScale('abc')">
                    <div class="scale-icon">
                      <i class="fas fa-list-alt"></i>
                    </div>
                    <h4>ABC 异常行为量表</h4>
                    <p>适用年龄：3-16岁</p>
                    <div class="scale-description">
                      <p>评估自闭症谱系障碍相关行为问题</p>
                    </div>
                  </div>

                  <div class="scale-card" @click="selectScale('asrs-autism')">
                    <div class="scale-icon">
                      <i class="fas fa-brain"></i>
                    </div>
                    <h4>ASRS 自闭症评估量表</h4>
                    <p>适用年龄：2-18岁</p>
                    <div class="scale-description">
                      <p>自闭症谱系障碍综合评估工具</p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 睡眠障碍筛查 -->
              <div class="category-section">
                <h3><i class="fas fa-moon"></i> 睡眠障碍筛查</h3>
                <div class="scale-cards">
                  <div class="scale-card" @click="selectScale('psqi')">
                    <div class="scale-icon">
                      <i class="fas fa-bed"></i>
                    </div>
                    <h4>PSQI 睡眠质量指数</h4>
                    <p>适用年龄：全年龄段</p>
                    <div class="scale-description">
                      <p>评估睡眠质量和睡眠障碍</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </div>

          <!-- 输入方式选择页面 -->
          <div v-if="showInputMethodSelection" class="input-method-selection-container">
            <div class="area-header">
              <h3><i class="fas fa-clipboard-check"></i> {{ scaleTitle }} - 选择填写方式</h3>
              <button class="back-btn" @click="goBackToScaleSelection">
                <i class="fas fa-arrow-left"></i> 返回选择量表
              </button>
            </div>

            <div class="method-selection-content">
              <div class="selection-description">
                <h4>请选择您偏好的答题方式</h4>
                <p>我们提供两种便捷的答题方式，您可以根据个人喜好选择</p>
              </div>

              <div class="method-cards-container">
                <!-- 语音识别输入卡片 -->
                <div class="method-card voice-method-card" @click="selectInputMethod('voice')">
                  <div class="card-header">
                    <div class="card-icon voice-icon">
                      <i class="fas fa-microphone"></i>
                    </div>
                    <h4>语音识别输入</h4>
                  </div>

                  <div class="card-content">
                    <p class="card-description">通过语音回答问题，AI助手将引导您完成整个评估过程</p>

                    <div class="card-features">
                      <div class="feature-item">
                        <i class="fas fa-robot"></i>
                        <span>AI语音助手引导</span>
                      </div>
                      <div class="feature-item">
                        <i class="fas fa-volume-up"></i>
                        <span>题目语音播报</span>
                      </div>
                      <div class="feature-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>实时安全监测</span>
                      </div>
                      <div class="feature-item">
                        <i class="fas fa-heart"></i>
                        <span>情感交互体验</span>
                      </div>
                    </div>
                  </div>

                  <div class="card-footer">
                    <div class="select-btn voice-select-btn">
                      <i class="fas fa-arrow-right"></i>
                      <span>选择语音输入</span>
                    </div>
                  </div>
                </div>

                <!-- 文字输入卡片 -->
                <div class="method-card text-method-card" @click="selectInputMethod('text')">
                  <div class="card-header">
                    <div class="card-icon text-icon">
                      <i class="fas fa-edit"></i>
                    </div>
                    <h4>文字选择输入</h4>
                  </div>

                  <div class="card-content">
                    <p class="card-description">传统的选择题模式，通过点击选项快速完成评估</p>

                    <div class="card-features">
                      <div class="feature-item">
                        <i class="fas fa-mouse-pointer"></i>
                        <span>点击选择答案</span>
                      </div>
                      <div class="feature-item">
                        <i class="fas fa-list-check"></i>
                        <span>选择题模式</span>
                      </div>
                      <div class="feature-item">
                        <i class="fas fa-clock"></i>
                        <span>快速高效</span>
                      </div>
                      <div class="feature-item">
                        <i class="fas fa-eye"></i>
                        <span>可视化界面</span>
                      </div>
                    </div>
                  </div>

                  <div class="card-footer">
                    <div class="select-btn text-select-btn">
                      <i class="fas fa-arrow-right"></i>
                      <span>选择文字输入</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 语音识别界面 -->
          <div v-if="inputMethod === 'voice' && !showScaleSelectionPage && !showInputMethodSelection" class="voice-interface-container">
            <div class="area-header">
              <h3><i class="fas fa-microphone"></i> {{ scaleTitle }} - AI语音助手</h3>
              <button class="back-btn" @click="goBackToInputMethodSelection">
                <i class="fas fa-arrow-left"></i> 返回选择方式
              </button>
            </div>

            <!-- 风险警报显示 -->
            <div v-if="riskAlert.show" class="risk-alert-overlay">
              <div class="risk-alert-modal" :class="'alert-' + riskAlert.level">
                <div class="alert-header">
                  <i class="fas fa-exclamation-triangle"></i>
                  <h3>{{ riskAlert.title }}</h3>
                </div>
                <div class="alert-content">
                  <p class="alert-message">{{ riskAlert.message }}</p>
                  <div class="detected-content">
                    <strong>检测到的内容：</strong>
                    <span class="risk-text">"{{ riskAlert.detectedText }}"</span>
                  </div>
                  <div class="emergency-contacts">
                    <h4>紧急求助热线：</h4>
                    <div class="contact-item">
                      <i class="fas fa-phone"></i>
                      <span>全国心理危机干预热线：************</span>
                    </div>
                    <div class="contact-item">
                      <i class="fas fa-ambulance"></i>
                      <span>紧急情况：120 / 110</span>
                    </div>
                  </div>
                </div>
                <div class="alert-actions">
                  <button class="alert-btn continue-btn" @click="continueAfterAlert">
                    <i class="fas fa-play"></i>
                    继续评估
                  </button>
                  <button class="alert-btn stop-btn" @click="stopAfterAlert">
                    <i class="fas fa-stop"></i>
                    结束评估
                  </button>
                </div>
              </div>
            </div>

            <div class="voice-interface-content">
              <!-- AI助手区域 -->
              <div class="ai-assistant-section">
                <div class="assistant-container">
                  <!-- 动态笑脸 -->
                  <div class="assistant-face" :class="assistantState">
                    <div class="face-circle">
                      <div class="eyes">
                        <div class="eye left-eye">
                          <div class="pupil"></div>
                        </div>
                        <div class="eye right-eye">
                          <div class="pupil"></div>
                        </div>
                      </div>
                      <div class="mouth" :class="mouthState"></div>
                      <div class="cheeks">
                        <div class="cheek left-cheek"></div>
                        <div class="cheek right-cheek"></div>
                      </div>
                    </div>
                  </div>

                  <!-- 助手状态显示 -->
                  <div class="assistant-status">
                    <div class="status-text">{{ assistantStatusText }}</div>
                    <div class="status-indicator" :class="assistantState"></div>
                  </div>
                </div>
              </div>

              <!-- 题目显示区域 -->
              <div class="question-display-section">
                <div class="question-progress">
                  <div class="progress-info">
                    <span class="current-question">第 {{ currentVoiceQuestionIndex + 1 }} 题</span>
                    <span class="total-questions">共 {{ getTotalQuestionCount() }} 题</span>
                  </div>
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                  </div>
                </div>

                <div class="question-content">
                  <div class="question-text">
                    {{ getCurrentVoiceQuestion() }}
                  </div>
                </div>
              </div>

              <!-- 语音控制区域 -->
              <div class="voice-controls-section">
                <div class="control-buttons">
                  <button class="control-btn read-question-btn"
                          @click="readCurrentQuestion"
                          :disabled="isSpeaking">
                    <i class="fas fa-volume-up"></i>
                    <span>{{ isSpeaking ? '正在朗读...' : '重新朗读' }}</span>
                  </button>

                  <button class="control-btn voice-input-btn"
                          @click="toggleVoiceRecording"
                          :class="{ recording: isListening }"
                          :disabled="isSpeaking">
                    <i :class="isListening ? 'fas fa-stop' : 'fas fa-microphone'"></i>
                    <span>{{ isListening ? '停止录音' : '语音回答' }}</span>
                  </button>
                </div>

                <!-- 语音识别结果 -->
                <div v-if="voiceRecognitionResult" class="recognition-result">
                  <div class="result-header">
                    <i class="fas fa-quote-left"></i>
                    <span>识别结果</span>
                    <i class="fas fa-quote-right"></i>
                  </div>
                  <div class="result-text">{{ voiceRecognitionResult }}</div>
                  <div class="result-actions">
                    <button class="result-btn confirm-btn" @click="confirmVoiceAnswer">
                      <i class="fas fa-check"></i>
                      确认答案
                    </button>
                    <button class="result-btn retry-btn" @click="retryVoiceInput">
                      <i class="fas fa-redo"></i>
                      重新录音
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 文字测试页面 -->
          <div v-if="inputMethod === 'text' && !showScaleSelectionPage && !showInputMethodSelection" class="scale-container">
            <div class="area-header">
              <h3><i class="fas fa-clipboard-check"></i> {{ scaleTitle }}</h3>
              <button class="back-btn" @click="goBackToScaleSelection">
                <i class="fas fa-arrow-left"></i> 返回选择量表
              </button>
            </div>

          <!-- 量表内容 -->
          <div class="scale-content">
            <!-- 量表说明 -->
            <div class="scale-intro">
              <h4>{{ currentScaleData.title }}</h4>
              <p>{{ currentScaleData.description }}</p>
            </div>

            <!-- 简化的提交成功显示 -->
            <div v-if="showResult" class="result-section">
              <div class="result-header">
                <h3><i class="fas fa-check-circle"></i> 量表提交成功</h3>
                <div class="success-indicator">
                  <span class="success-message">
                    <i class="fas fa-paper-plane"></i> 已发送给医生
                  </span>
                </div>
              </div>
              
              <div class="result-content">
                <!-- 基本分数显示 -->
                <div class="score-summary">
                  <div class="score-item total-score">
                    <div class="score-label">总分</div>
                    <div class="score-value">
                      <span class="current-score">{{ totalScore }}</span>
                      <span class="score-separator">/</span>
                      <span class="max-score">{{ maxTotalScore }}</span>
                    </div>
                    <div class="score-percentage" :class="'risk-' + getRiskLevel(totalScorePercentage)">
                      {{ totalScorePercentage }}% - {{ getRiskText(totalScorePercentage) }}
                    </div>
                    <div class="score-progress">
                      <div class="progress-bar"
                           :class="'risk-' + getRiskLevel(totalScorePercentage)"
                           :style="{ width: totalScorePercentage + '%' }"></div>
                    </div>
                  </div>
                  <div v-if="attentionScore !== null" class="score-item">
                    <div class="score-label">注意力缺陷</div>
                    <div class="score-value">
                      <span class="current-score">{{ attentionScore }}</span>
                      <span class="score-separator">/</span>
                      <span class="max-score">{{ maxAttentionScore }}</span>
                    </div>
                    <div class="score-percentage" :class="'risk-' + getRiskLevel(attentionScorePercentage)">
                      {{ attentionScorePercentage }}% - {{ getRiskText(attentionScorePercentage) }}
                    </div>
                    <div class="score-progress">
                      <div class="progress-bar"
                           :class="'risk-' + getRiskLevel(attentionScorePercentage)"
                           :style="{ width: attentionScorePercentage + '%' }"></div>
                    </div>
                  </div>
                  <div v-if="hyperactivityScore !== null" class="score-item">
                    <div class="score-label">多动/冲动</div>
                    <div class="score-value">
                      <span class="current-score">{{ hyperactivityScore }}</span>
                      <span class="score-separator">/</span>
                      <span class="max-score">{{ maxHyperactivityScore }}</span>
                    </div>
                    <div class="score-percentage" :class="'risk-' + getRiskLevel(hyperactivityScorePercentage)">
                      {{ hyperactivityScorePercentage }}% - {{ getRiskText(hyperactivityScorePercentage) }}
                    </div>
                    <div class="score-progress">
                      <div class="progress-bar"
                           :class="'risk-' + getRiskLevel(hyperactivityScorePercentage)"
                           :style="{ width: hyperactivityScorePercentage + '%' }"></div>
                    </div>
                  </div>
                  <div v-if="oppositionScore !== null" class="score-item">
                    <div class="score-label">对立违抗</div>
                    <div class="score-value">
                      <span class="current-score">{{ oppositionScore }}</span>
                      <span class="score-separator">/</span>
                      <span class="max-score">{{ maxOppositionScore }}</span>
                    </div>
                    <div class="score-percentage" :class="'risk-' + getRiskLevel(oppositionScorePercentage)">
                      {{ oppositionScorePercentage }}% - {{ getRiskText(oppositionScorePercentage) }}
                    </div>
                    <div class="score-progress">
                      <div class="progress-bar"
                           :class="'risk-' + getRiskLevel(oppositionScorePercentage)"
                           :style="{ width: oppositionScorePercentage + '%' }"></div>
                    </div>
                  </div>
                </div>

                <div class="interpretation">
                  <h4><i class="fas fa-chart-line"></i> 结果解读</h4>
                  <div class="interpretation-content">
                    <div class="score-summary">
                      <h5><i class="fas fa-calculator"></i> 得分概况</h5>
                      <div class="score-details">
                        <div class="score-item">
                          <span class="score-label">总分：</span>
                          <span class="score-value">{{ totalScore }}/{{ maxTotalScore }}</span>
                          <span class="score-percentage">({{ totalScorePercentage }}%)</span>
                        </div>
                        <div v-if="attentionScore !== null" class="score-item">
                          <span class="score-label">注意力缺陷：</span>
                          <span class="score-value">{{ attentionScore }}/{{ maxAttentionScore }}</span>
                          <span class="score-percentage">({{ attentionScorePercentage }}%)</span>
                        </div>
                        <div v-if="hyperactivityScore !== null" class="score-item">
                          <span class="score-label">多动/冲动：</span>
                          <span class="score-value">{{ hyperactivityScore }}/{{ maxHyperactivityScore }}</span>
                          <span class="score-percentage">({{ hyperactivityScorePercentage }}%)</span>
                        </div>
                        <div v-if="oppositionScore !== null" class="score-item">
                          <span class="score-label">对立违抗：</span>
                          <span class="score-value">{{ oppositionScore }}/{{ maxOppositionScore }}</span>
                          <span class="score-percentage">({{ oppositionScorePercentage }}%)</span>
                        </div>
                      </div>
                    </div>

                    <div class="risk-assessment">
                      <h5><i class="fas fa-exclamation-triangle"></i> 风险评估</h5>
                      <div class="risk-level-display">
                        <span class="risk-badge" :class="getRiskLevelClass(riskLevel)">
                          {{ riskLevel }}
                        </span>
                        <span class="risk-description">{{ getRiskDescription(riskLevel) }}</span>
                      </div>
                    </div>

                    <div class="detailed-interpretation">
                      <h5><i class="fas fa-file-medical-alt"></i> 详细解读</h5>
                      <p>{{ interpretationText }}</p>
                    </div>

                    <div class="completion-info">
                      <h5><i class="fas fa-info-circle"></i> 完成信息</h5>
                      <div class="completion-details">
                        <div class="completion-item">
                          <i class="fas fa-clock"></i>
                          <span>完成时间：{{ formatCompletionTime() }}</span>
                        </div>
                        <div class="completion-item">
                          <i class="fas fa-list-check"></i>
                          <span>题目数量：{{ getTotalQuestionCount() }}题</span>
                        </div>
                        <div class="completion-item">
                          <i class="fas fa-user"></i>
                          <span>填写者：{{ getFillerInfo() }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>



                <div class="result-actions">
                  <button class="info-btn" @click="retakeTest">重新测试</button>
                </div>
              </div>
            </div>

            <!-- 量表问题 -->
            <div v-if="!showResult" class="questions-section">
              <!-- 通用问题格式 -->
              <div v-if="currentScaleData.questions" class="question-group">
                <h4>{{ currentScaleData.title }}</h4>
                <div v-for="(question, index) in currentScaleData.questions" :key="'question-' + index" class="question-item">
                  <div class="question-text">{{ index + 1 }}. {{ question }}</div>
                  <div class="question-options">
                    <!-- 特殊选项配置（如儿童注意力水平测评量表） -->
                    <template v-if="currentScaleData.specialOptions && currentScaleData.specialOptions[index]">
                      <label v-for="(option, optionIndex) in currentScaleData.specialOptions[index]" :key="optionIndex" class="option-label special-option">
                        <input
                          type="radio"
                          :name="'question-' + index"
                          :value="getSpecialOptionScore(index, optionIndex)"
                          @change="updateAnswer('general', index, getSpecialOptionScore(index, optionIndex))"
                        >
                        <span class="option-text">{{ String.fromCharCode(65 + optionIndex) }}. {{ option }}</span>
                      </label>
                    </template>
                    <!-- 普通选项配置 -->
                    <template v-else>
                      <label v-for="(label, optionIndex) in currentOptionLabels" :key="optionIndex" class="option-label">
                        <input
                          type="radio"
                          :name="'question-' + index"
                          :value="optionIndex"
                          @change="updateAnswer('general', index, optionIndex)"
                        >
                        <span class="option-text">{{ label }}</span>
                      </label>
                    </template>
                  </div>
                </div>
              </div>

              <!-- CBCL儿童行为量表特殊格式 -->
              <div v-else-if="currentScale === 'cbcl'" class="question-group">
                <h4>{{ currentScaleData.title }}</h4>

                <!-- 内化问题部分 -->
                <div class="cbcl-section">
                  <h5 class="section-title">内化问题 (情绪问题)</h5>
                  <div v-for="(question, index) in currentScaleData.internalizing" :key="'internal-' + index" class="question-item">
                    <div class="question-text">{{ index + 1 }}. {{ question }}</div>
                    <div class="question-options">
                      <label v-for="(label, optionIndex) in currentOptionLabels" :key="optionIndex" class="option-label">
                        <input
                          type="radio"
                          :name="'question-' + index"
                          :value="optionIndex"
                          @change="updateAnswer('general', index, optionIndex)"
                        >
                        <span class="option-text">{{ label }}</span>
                      </label>
                    </div>
                  </div>
                </div>

                <!-- 外化问题部分 -->
                <div class="cbcl-section">
                  <h5 class="section-title">外化问题 (行为问题)</h5>
                  <div v-for="(question, index) in currentScaleData.externalizing" :key="'external-' + index" class="question-item">
                    <div class="question-text">{{ currentScaleData.internalizing.length + index + 1 }}. {{ question }}</div>
                    <div class="question-options">
                      <label v-for="(label, optionIndex) in currentOptionLabels" :key="optionIndex" class="option-label">
                        <input
                          type="radio"
                          :name="'question-' + (currentScaleData.internalizing.length + index)"
                          :value="optionIndex"
                          @change="updateAnswer('general', currentScaleData.internalizing.length + index, optionIndex)"
                        >
                        <span class="option-text">{{ label }}</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 社会生活能力量表特殊格式 -->
              <div v-else-if="currentScale === 'social-living-skills'" class="question-group">
                <h4>{{ currentScaleData.title }}</h4>

                <!-- 年龄组选择 -->
                <div class="age-group-selection">
                  <h5>请选择适合的年龄组：</h5>
                  <div class="age-group-options">
                    <label v-for="(questions, ageGroup) in currentScaleData.ageGroups" :key="ageGroup" class="age-group-label">
                      <input
                        type="radio"
                        name="age-group"
                        :value="ageGroup"
                        @change="selectAgeGroup(ageGroup)"
                      >
                      <span>{{ ageGroup }}</span>
                    </label>
                  </div>
                </div>

                <!-- 选定年龄组的问题 -->
                <div v-if="selectedAgeGroup" class="age-group-questions">
                  <h5 class="section-title">{{ selectedAgeGroup }} 发展评估</h5>
                  <div v-for="(question, index) in currentScaleData.ageGroups[selectedAgeGroup]" :key="'age-' + index" class="question-item">
                    <div class="question-text">{{ index + 1 }}. {{ question }}</div>
                    <div class="question-options">
                      <label v-for="(label, optionIndex) in currentOptionLabels" :key="optionIndex" class="option-label">
                        <input
                          type="radio"
                          :name="'question-' + index"
                          :value="optionIndex"
                          @change="updateAnswer('general', index, optionIndex)"
                        >
                        <span class="option-text">{{ label }}</span>
                      </label>
                    </div>
                  </div>
                </div>
              </div>

              <!-- ADHD专用格式 -->
              <template v-if="currentScaleData.attentionQuestions">
                <!-- 注意力缺陷问题 -->
                <div class="question-group">
                  <h4>注意力缺陷相关问题</h4>
                  <div v-for="(question, index) in currentScaleData.attentionQuestions" :key="'attention-' + index" class="question-item">
                    <div class="question-text">{{ index + 1 }}. {{ question }}</div>
                    <div class="question-options">
                      <label v-for="(label, optionIndex) in currentOptionLabels" :key="optionIndex" class="option-label">
                        <input
                          type="radio"
                          :name="'attention-' + index"
                          :value="optionIndex"
                          @change="updateAnswer('attention', index, optionIndex)"
                        >
                        <span class="option-text">{{ label }}</span>
                      </label>
                    </div>
                  </div>
                </div>

                <!-- 多动/冲动问题 -->
                <div class="question-group">
                  <h4>多动/冲动相关问题</h4>
                  <div v-for="(question, index) in currentScaleData.hyperactivityQuestions" :key="'hyperactivity-' + index" class="question-item">
                    <div class="question-text">{{ currentScaleData.attentionQuestions.length + index + 1 }}. {{ question }}</div>
                    <div class="question-options">
                      <label v-for="(label, optionIndex) in currentOptionLabels" :key="optionIndex" class="option-label">
                        <input
                          type="radio"
                          :name="'hyperactivity-' + index"
                          :value="optionIndex"
                          @change="updateAnswer('hyperactivity', index, optionIndex)"
                        >
                        <span class="option-text">{{ label }}</span>
                      </label>
                    </div>
                  </div>
                </div>
              </template>

              <!-- 提交按钮 -->
              <div class="submit-section">
                <button class="submit-btn" @click="submitScale" :disabled="!isScaleComplete || isSubmitting">
                  <i v-if="isSubmitting" class="fas fa-spinner fa-spin"></i>
                  <i v-else class="fas fa-check"></i>
                  {{ isSubmitting ? '正在提交...' : '提交评测' }}
                </button>
                <div v-if="!isScaleComplete" class="completion-hint">
                  请完成所有问题后再提交
                </div>
                <div v-if="isSubmitting" class="submitting-hint">
                  正在同步数据到医生端系统，请稍候...
                </div>
              </div>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>

    <!-- 详细信息收集弹窗 -->
    <div v-if="showDetailInfoDialog" class="detail-info-dialog-overlay">
      <div class="detail-info-dialog">
        <div class="dialog-header">
          <h3>完善详细信息</h3>
        </div>
        <div class="dialog-content">
          <p>为了提供更准确的分析结果，我们希望收集一些额外的信息：</p>
          <ul>
            <li>家庭环境信息</li>
            <li>孩子发病史</li>
            <li>日常行为表现</li>
            <li>学习生活情况</li>
          </ul>
          <p>这些信息将帮助AI系统提供更精准的分析和建议。</p>

          <div v-if="showDetailInfoForm" class="detail-info-form">
            <div class="form-group">
              <label>家庭结构</label>
              <select v-model="detailInfo.familyStructure">
                <option value="">请选择</option>
                <option value="nuclear">核心家庭(父母+子女)</option>
                <option value="extended">大家庭(含祖父母等)</option>
                <option value="single">单亲家庭</option>
                <option value="other">其他</option>
              </select>
            </div>

            <div class="form-group">
              <label>孩子发病史</label>
              <textarea v-model="detailInfo.medicalHistory" placeholder="请描述孩子的既往病史，包括确诊情况、用药情况等"></textarea>
            </div>

            <div class="form-group">
              <label>日常行为表现</label>
              <textarea v-model="detailInfo.behavioralObservation" placeholder="请描述孩子在家庭和学校的行为表现"></textarea>
            </div>

            <div class="form-group">
              <label>学习情况</label>
              <textarea v-model="detailInfo.academicPerformance" placeholder="请描述孩子的学习情况和学校反馈"></textarea>
            </div>
          </div>
        </div>
        <div class="dialog-footer">
          <button class="btn-skip" @click="handleDetailInfoChoice(false)">
            跳过，直接分析
          </button>
          <button v-if="!showDetailInfoForm" class="btn-primary" @click="showDetailInfoForm = true">
            填写详细信息
          </button>
          <button v-else class="btn-primary" @click="handleDetailInfoChoice(true, detailInfo)">
            提交并分析
          </button>
        </div>
      </div>
    </div>

    <!-- 公共页脚组件 -->
    <AppFooter />


  </div>
</template>

<script>
import AppHeader from '@/components/common/AppHeader.vue'
import AppSidebar from '@/components/common/AppSidebar.vue'
import AppFooter from '@/components/common/AppFooter.vue'
import { submitScaleWithAIAnalysis } from '@/api/scaleAPI.js'




export default {
  name: 'ADHDScale',
  components: {
    AppHeader,
    AppSidebar,
    AppFooter
  },
  data() {
    return {
      // 用户信息
      currentUser: {
        name: '张同学',
        avatar: 'https://randomuser.me/api/portraits/men/44.jpg'
      },
      
      // 导航状态
      navExpanded: false,

      showScaleSelectionPage: true,
      showInputMethodSelection: false,
      inputMethod: 'text', // 'voice' 或 'text'
      currentScale: '',
      scaleTitle: '心理健康筛查量表',
      scaleDescription: '请根据过去的表现，为每个问题选择最符合的选项。',
      showResult: false,
      resultTitle: '评测结果',

      // 语音识别相关状态
      currentVoiceQuestionIndex: 0,
      isSpeaking: false,
      isListening: false,
      voiceRecognitionResult: '',
      speechSynthesis: null,
      speechRecognition: null,

      // AI助手状态
      assistantState: 'idle', // 'idle', 'speaking', 'listening', 'thinking'
      assistantStatusText: '您好！我是您的AI助手，准备开始评估',

      // 风险评估系统
      riskAlert: {
        show: false,
        level: 'yellow', // 'yellow', 'orange', 'red'
        title: '',
        message: '',
        detectedText: ''
      },
      riskKeywords: {
        yellow: ['累', '没意思', '烦', '无聊', '不开心'],
        orange: ['割腕', '吃药', '自残', '伤害自己', '痛苦'],
        red: ['想死', '自杀', '不想活', '结束生命', '杀了他', '报复', '同归于尽']
      },
      
      // 量表答案
      answers: [],

      // 评分相关
      totalScore: 0,
      attentionScore: 0,
      hyperactivityScore: 0,
      oppositionScore: null,
      interpretationText: '请完成量表评测以查看结果解读。',
      riskLevel: '',

      // 新量表相关状态
      selectedAgeGroup: null, // 用于社会生活能力量表的年龄组选择
      // 注意：severityLevel和professionalAdvice只用于后端传输，前端不显示

      // 时间记录
      scaleStartTime: null,

      // 提交状态
      isSubmitting: false,



      // 详细信息收集相关
      showDetailInfoDialog: false,
      showDetailInfoForm: false,
      userDetailInfo: null,
      detailInfo: {
        familyStructure: '',
        medicalHistory: '',
        behavioralObservation: '',
        academicPerformance: ''
      },



      // 选项标签
      optionLabels: ['没有', '有一点', '不少', '很多'],
      
      // 不同类型的量表问题
      scaleQuestions: {
        // PHQ-A 青少年抑郁症筛查量表
        'phq-a': {
          title: "PHQ-A 青少年抑郁症筛查量表 (12-18岁)",
          description: "本量表适用于12-18岁青少年，用于筛查抑郁症状。请根据过去两周内的感受，选择最符合您情况的选项。",
          type: 'depression',
          optionLabels: ['完全没有', '几天', '一半以上的天数', '几乎每天'],
          questions: [
            "做事时提不起劲或没有兴趣",
            "感到心情低落、沮丧或绝望",
            "入睡困难、睡不安稳或睡眠过多",
            "感觉疲倦或没有活力",
            "食欲不振或吃太多",
            "觉得自己很糟糕，或觉得自己很失败，或让自己或家人失望",
            "对事物专注有困难，例如阅读报纸或看电视时",
            "动作或说话速度缓慢到别人已经察觉？或正好相反—烦躁或坐立不安、动来动去的情况超过平常",
            "有不如死掉或用某种方式伤害自己的念头"
          ]
        },

        // CDI-2 儿童抑郁量表
        'cdi-2': {
          title: "CDI-2 儿童抑郁量表 (7-17岁)",
          description: "本量表适用于7-17岁儿童青少年，用于评估抑郁症状。请选择最能描述您过去两周感受的选项。",
          type: 'depression',
          optionLabels: ['从不', '有时', '经常', '总是'],
          questions: [
            "我感到悲伤",
            "我对任何事情都不感兴趣",
            "我觉得自己很糟糕",
            "我觉得没有人真正爱我",
            "我觉得自己永远做不好任何事情",
            "我担心会发生不好的事情",
            "我讨厌自己",
            "我觉得都是我的错",
            "我想哭",
            "我感到孤独",
            "我很难做决定",
            "我觉得自己很丑"
          ]
        },

        // SCARED 儿童焦虑筛查量表
        'scared': {
          title: "SCARED 儿童焦虑相关情绪障碍筛查量表 (8-18岁)",
          description: "本量表适用于8-18岁儿童青少年，用于筛查焦虑症状。请根据您的真实感受选择最符合的选项。",
          type: 'anxiety',
          optionLabels: ['几乎从不', '有时', '经常', '几乎总是'],
          questions: [
            "当我害怕时，我很难呼吸",
            "我头痛",
            "我避免去让我害怕的地方或做让我害怕的事",
            "我睡在父母旁边",
            "我担心别人对我的看法",
            "当我害怕时，我感到头晕",
            "我紧张或焦虑",
            "我跟随我的母亲或父亲到任何地方",
            "人们告诉我我看起来很紧张",
            "我感到紧张，和我不太了解的人在一起",
            "我肚子痛",
            "当我害怕时，我感到好像要昏倒了"
          ]
        },

        // GAD-7 广泛性焦虑障碍量表
        'gad-7': {
          title: "GAD-7 广泛性焦虑障碍量表 (≥12岁)",
          description: "本量表适用于12岁以上人群，用于筛查广泛性焦虑障碍。请根据过去两周的感受选择最符合的选项。",
          type: 'anxiety',
          optionLabels: ['完全没有', '几天', '一半以上的天数', '几乎每天'],
          questions: [
            "感觉紧张、焦虑或急躁",
            "无法停止或控制担忧",
            "对各种各样的事情担忧过多",
            "很难放松下来",
            "坐立不安，难以安静地坐着",
            "变得容易烦恼或易怒",
            "感到好像有什么可怕的事情会发生"
          ]
        },

        // SNAP-IV ADHD量表
        'snap-iv': {
          title: "SNAP-IV ADHD量表 (6-12岁)",
          description: "本量表适用于6-12岁儿童，用于评估ADHD症状。请根据孩子过去6个月的表现选择最符合的选项。",
          type: 'adhd',
          optionLabels: ['没有', '有一点', '不少', '很多'],
          attentionQuestions: [
            "在学校做作业或者其他活动时，无法专注于细节的部分或出现粗心的错误",
            "很难持续专注于工作或游戏活动",
            "看起来好像没有听到别人对他（她）说话的内容",
            "无法遵循指示，无法完成学校作业、家务或工作场所的职责",
            "很难组织工作和活动",
            "逃避、不喜欢或不愿意从事需要持续心理努力的工作",
            "遗失工作或活动所需的物品",
            "容易被外在刺激转移注意力",
            "在日常活动中健忘"
          ],
          hyperactivityQuestions: [
            "手脚不停地动或在座位上扭动",
            "在应该坐着的时候离开座位",
            "在不适当的场合跑来跑去或爬上爬下",
            "很难安静地游戏或从事休闲活动",
            "经常处于活跃状态，好像被马达驱动一样",
            "说话过多",
            "在问题还没说完时就抢着回答",
            "很难等待轮到自己",
            "打断或干扰别人"
          ]
        },

        // ASRS 成人ADHD量表
        'asrs': {
          title: "ASRS 成人ADHD自评量表 (≥18岁)",
          description: "本量表适用于18岁以上成人，用于自我评估ADHD症状。请根据过去6个月的表现选择最符合的选项。",
          type: 'adhd',
          optionLabels: ['从不', '很少', '有时', '经常', '非常频繁'],
          questions: [
            "很难专注于工作或活动的细节，或者经常出现粗心的错误",
            "很难在工作或其他活动中保持注意力",
            "当别人直接对您说话时，您似乎没有在听",
            "很难按照指示完成工作，或者无法完成工作任务",
            "很难组织任务和活动",
            "避免或不愿意从事需要持续心理努力的任务",
            "遗失完成任务或活动所需的物品",
            "容易被外界刺激分心",
            "在日常活动中健忘",
            "坐着时手脚不安或扭动",
            "在需要保持坐着的场合离开座位",
            "感到坐立不安",
            "很难安静地参与休闲活动",
            "感觉自己总是在活动或像被马达驱动一样",
            "说话过多",
            "在问题说完之前就抢答",
            "很难等待轮到自己",
            "打断或干扰别人的对话或活动"
          ]
        },

        // PSQI 睡眠质量指数
        'psqi': {
          title: "PSQI 匹兹堡睡眠质量指数 (全年龄段)",
          description: "本量表用于评估睡眠质量和睡眠障碍。请根据过去一个月的睡眠情况选择最符合的选项。",
          type: 'sleep',
          optionLabels: ['非常好', '比较好', '比较差', '非常差'],
          questions: [
            "您对自己的睡眠质量总体评价如何？",
            "您通常需要多长时间才能入睡？",
            "您通常每晚实际睡眠时间有多长？",
            "您的睡眠效率如何？（实际睡眠时间/在床上的时间）",
            "过去一个月，您因为以下原因睡眠困难的频率：半夜醒来或早醒",
            "过去一个月，您因为以下原因睡眠困难的频率：起夜上厕所",
            "过去一个月，您因为以下原因睡眠困难的频率：呼吸不畅",
            "过去一个月，您因为以下原因睡眠困难的频率：咳嗽或打鼾",
            "过去一个月，您因为以下原因睡眠困难的频率：感觉太冷",
            "过去一个月，您因为以下原因睡眠困难的频率：感觉太热",
            "过去一个月，您因为以下原因睡眠困难的频率：做恶梦",
            "过去一个月，您因为以下原因睡眠困难的频率：疼痛不适"
          ]
        },

        // Conners 父母用量表
        'conners-parent': {
          title: "Conners 父母用量表 (3-16岁)",
          description: "康氏儿童行为问卷，由父母根据儿童实际表现情况评分。请根据孩子最近的行为表现选择最符合的选项。",
          type: 'adhd',
          optionLabels: ['无', '稍有', '相当多', '很多'],
          questions: [
            "某种小动作（如咬指甲、吸手指、拉头发、拉衣服上的布毛）",
            "对大人粗鲁无礼",
            "在交朋友或保持友谊上存在问题",
            "易兴奋，易冲动",
            "爱指手划脚",
            "吸吮或咬嚼（拇指、衣服、毯子）",
            "容易或经常哭叫",
            "脾气很大",
            "白日梦",
            "学习困难",
            "扭动不停",
            "惧怕（新环境、陌生人、陌生地方、上学）",
            "坐立不定，经常\"忙碌\"",
            "破坏性",
            "撒谎或捏造情节",
            "怕羞",
            "造成的麻烦比同龄孩子多",
            "说话与同龄儿童不同（像婴儿说话、口吃、别人不易听懂）",
            "抵赖错误或归罪他人",
            "好争吵",
            "撅嘴和生气",
            "偷窃",
            "不服从或勉强服从",
            "忧虑比别人多（忧虑、孤独、疾病、死亡）",
            "做事有始无终",
            "感情易受损害",
            "欺凌别人",
            "不能停止重复性活动",
            "残忍",
            "稚气或不成熟（自己会的事要人帮忙，依缠别人，常需别人鼓励、支持）",
            "容易分心或注意不集中成为一个问题",
            "头痛",
            "情绪变化迅速剧烈",
            "不喜欢或不遵从纪律或约束",
            "经常打架",
            "与兄弟姐妹不能很好相处",
            "在努力中容易泄气",
            "妨害其他儿童",
            "基本上是一个不愉快的小孩",
            "有饮食问题（食欲不佳、进食中常跑开）",
            "胃痛",
            "有睡眠问题（不能入睡、早醒、夜间起床）",
            "其他疼痛",
            "呕吐或恶心",
            "感到在家庭圈子中被欺骗",
            "自夸和吹牛",
            "让自己受别人欺负",
            "有大便问题（腹泻、排便不规则、便秘）"
          ]
        },

        // Conners 教师用量表
        'conners-teacher': {
          title: "Conners 教师用量表 (3-16岁)",
          description: "康氏儿童行为问卷教师版，由教师根据学生在学校的行为表现评分。请根据学生最近的课堂表现选择最符合的选项。",
          type: 'adhd',
          optionLabels: ['无', '稍有', '相当多', '很多'],
          questions: [
            "扭动不停",
            "在不应出声的场合制造噪音",
            "提出要求必须立即得到满足",
            "动作粗鲁（唐突无礼）",
            "暴怒及不能预料的行为",
            "对批评过分敏感",
            "容易分心或注意不集中成为问题",
            "妨害其他儿童",
            "白日梦",
            "撅嘴和生气",
            "情绪变化迅速和激烈",
            "好争吵",
            "能顺从权威",
            "坐立不定，经常\"忙碌\"",
            "易兴奋，易冲动",
            "过分要求教师的注意",
            "好像不为集体所接受",
            "好像容易被其他小孩领导",
            "缺少公平合理竞赛的意识",
            "好像缺乏领导能力",
            "做事有始无终",
            "稚气和不成熟",
            "抵赖错误或归罪他人",
            "不能与其他儿童相处",
            "与同学不合作",
            "在努力中容易泄气",
            "与教师不合作",
            "学习困难"
          ]
        },

        // Conners 教师简化版量表
        'conners-teacher-short': {
          title: "Conners 教师简化版量表 (3-16岁)",
          description: "康氏教师量表简化版，用于多动症快速筛查。10分为阳性，可作进一步评估。请根据学生表现选择最符合的选项。",
          type: 'adhd',
          optionLabels: ['无', '稍有', '相当多', '很多'],
          questions: [
            "活动过多，一刻不停",
            "兴奋激动，容易冲动",
            "惹恼其他儿童",
            "做事不能有始有终",
            "坐立不安",
            "注意不易集中，容易分心",
            "必须立即满足其要求，否则容易灰心丧气",
            "容易哭泣、喊叫",
            "情绪变化迅速剧烈",
            "勃然大怒，或出现意料不到的行为"
          ]
        },

        // 儿童注意力水平测评量表
        'attention-level': {
          title: "中国儿童注意力水平测评量表 (5岁以上)",
          description: "本量表适用于5岁以上儿童，由孩子本人填写，可快速测评注意力水平。35-45分：注意力非常棒；25-34分：基本维持需要；15-24分：急待提高。",
          type: 'attention',
          optionLabels: ['A选项', 'B选项', 'C选项'],
          questions: [
            "做作业时，你喜欢开着电视或听着音乐吗？",
            "听别人讲话时，你会常常想着另外一件事吗？",
            "你常常在做作业的时候还能耳听八方吗？",
            "做暑假作业时，你花几天的时间就能将所有的作业做完吗？",
            "你每次看书的时间有多长？",
            "你经常在看完一页书后却不知书上讲的是什么吗？",
            "做试卷时，你会经常漏掉题目吗？",
            "上课时，你是否经常想起昨天发生的事情？",
            "妈妈叫你拿碗筷，你却常常会拿错其他的东西吗？",
            "你放的东西经常会找不着吗？",
            "上课时如果外面下雨，你会分心吗？",
            "心里一有事，你就会在上课时坐不住吗？",
            "班上来了新老师，你会将注意力放在老师的穿着上吗？",
            "当家里来了客人，你会取消做作业的计划吗？",
            "一旦身体不舒服，你会请假不上学吗？"
          ],
          // 特殊的选项配置
          specialOptions: {
            0: ["是的，我觉得只有这样做作业才不会枯燥", "不是，我做作业一向很专心，一边看电视，一边做作业会相互干扰", "一般不会，但有时做作业时间长的时候会看看电视"],
            1: ["是的，我会不由自主地想其它一些事情", "我会尽量应付讲话的人", "我不会一心二用，否则可能两件事情都做不好"],
            2: ["我在做作业时对周围的一切了如指掌", "我做作业时不关心周围的事情", "这种情况不常发生，除非我在抄习题"],
            3: ["是的，快速做完后，会有更多的时间用来玩", "基本不是，因为这样做会影响做功课的质量", "偶尔，如果有事想出去玩才会这样"],
            4: ["我一般最长能看一个小时左右", "是的，看一小会儿我就想玩，坐不住", "我每次看书时间都很长，能坚持住两个小时左右"],
            5: ["是的，我很难集中注意力", "我只能记住一点", "我看完书后，能记住书上所讲的内容"],
            6: ["是的，我很粗心，做题有点心不在焉", "不会，我做任何事情都很认真", "我几乎每次都要漏掉点什么"],
            7: ["是的，我很容易想起昨天开心的事情", "上课的时候，我会跟着老师的节拍走", "当上课不紧张时，我会开一会儿小差"],
            8: ["当我在看喜欢的动画片时会", "一般不会，我一向做事很准确", "是的，我会经常拿错东西"],
            9: ["我放的东西有条有理，除非别人挪动了位置", "我经常会乱放东西", "我常常找不到橡皮、尺子等小东西"],
            10: ["我会听一会儿雨声，然后再继续上课", "上课时外面的雨声不会让我分心", "是的，我会被雨声吸引"],
            11: ["是的，我常常会念念不忘心里的事", "我会将不愉快的事情放在一边", "我会在不影响上课的前提下想想心事"],
            12: ["我会花点时间想想新老师的事情", "我会像原来一样认真听课", "我会好奇地一直打量着新老师"],
            13: ["我会和客人聊一会儿再做作业", "是的，正好有理由热闹热闹", "不会，我会按照自己的计划做作业"],
            14: ["如果不上新课的话我就请假", "正好有理由不去上课", "我不会因为小病而影响上课"]
          }
        },

        // 儿童社交焦虑量表
        'social-anxiety': {
          title: "儿童社交焦虑量表 (8-18岁)",
          description: "本量表用于评估儿童青少年的社交焦虑水平。请根据孩子在社交场合的真实表现选择最符合的选项。",
          type: 'anxiety',
          optionLabels: ['从不', '很少', '有时', '经常', '总是'],
          questions: [
            "害怕在班级或小组面前说话",
            "担心别人会笑话自己",
            "在陌生人面前感到紧张",
            "害怕参加聚会或社交活动",
            "担心在公共场所做错事情",
            "避免与不熟悉的人交谈",
            "在需要表演或展示时感到恐惧",
            "担心别人对自己的看法",
            "在餐厅或公共场所吃饭时感到不自在",
            "害怕成为别人注意的焦点",
            "在电话中与陌生人交谈感到困难",
            "参加体育活动或游戏时感到焦虑",
            "担心自己的外表不够好",
            "害怕在课堂上回答问题",
            "避免参加需要与他人合作的活动",
            "在社交场合感到脸红或出汗",
            "担心别人会拒绝自己",
            "害怕邀请别人参加活动",
            "在群体中感到孤独或被排斥",
            "担心自己说话时会结巴或说错话"
          ]
        },

        // 儿童行为量表(CBCL)
        'cbcl': {
          title: "儿童行为量表 CBCL (4-18岁)",
          description: "Achenbach儿童行为量表，由父母或照顾者填写，用于评估儿童的行为问题和社会能力。请根据孩子过去6个月的表现选择最符合的选项。",
          type: 'behavior',
          optionLabels: ['不符合', '有时符合', '经常符合'],
          internalizing: [
            "行为幼稚，比实际年龄小",
            "哭得太多",
            "要求必须完美",
            "感到别人想害他/她",
            "感到太内疚",
            "自我意识太强或容易难堪",
            "容易嫉妒",
            "感到过分疲劳而无明显原因",
            "身体不适而无医学原因（头痛、胃痛等）",
            "害怕某些动物、情境或地方",
            "害怕上学",
            "害怕自己可能想或做坏事",
            "感到自己必须是完美的",
            "感到没有人爱他/她",
            "感到别人想抓住他/她",
            "感到自己毫无价值或低人一等",
            "经常受伤，容易发生事故",
            "经常打架",
            "宁愿与比自己小的孩子在一起",
            "拒绝说话",
            "重复某些动作（强迫行为）",
            "分泌物多（不是由于感冒）",
            "害羞或胆小",
            "睡眠比大多数孩子少",
            "睡眠比大多数孩子多",
            "涂抹或玩弄大便",
            "言语问题",
            "凝视或发呆",
            "情绪或感受突然改变",
            "多疑",
            "发脾气或脾气暴躁",
            "想得太多",
            "睡眠困难",
            "大声或吵闹",
            "容易紧张或紧张过度",
            "动作太慢",
            "不快乐、悲伤或抑郁",
            "异常大声",
            "退缩，不与别人接触"
          ],
          externalizing: [
            "为了得到注意而表现或装小丑",
            "破坏自己的东西",
            "破坏属于家庭或其他孩子的东西",
            "不服从父母",
            "不服从学校",
            "不表现出内疚感",
            "容易嫉妒",
            "经常打架",
            "与坏孩子交往",
            "撒谎或欺骗",
            "宁愿与比自己小的孩子在一起",
            "身体攻击别人",
            "尖叫很多",
            "炫耀或吹牛",
            "脾气暴躁",
            "威胁别人",
            "大声或吵闹",
            "争论很多",
            "注意力不能集中，不能长时间专注",
            "不能坐着不动，坐立不安或过度活跃",
            "不能很好地与其他孩子相处",
            "破坏性",
            "不服从或对抗",
            "容易分心或注意力有问题",
            "妨碍其他孩子",
            "冲动或行动不经思考",
            "大声要求注意",
            "情绪或感受突然改变",
            "发脾气或脾气暴躁",
            "异常大声"
          ]
        },

        // 婴儿初中生社会生活能力量表
        'social-living-skills': {
          title: "婴儿初中生社会生活能力量表 (0-15岁)",
          description: "本量表用于评估从婴儿到初中生的社会生活能力发展水平。请根据孩子的实际能力选择最符合的选项。",
          type: 'development',
          optionLabels: ['完全不会', '需要帮助', '基本会做', '完全掌握'],
          ageGroups: {
            '0-2岁': [
              "能够识别熟悉的人",
              "对陌生人表现出警觉",
              "能够表达基本需求（饿、渴、困）",
              "会模仿简单的动作",
              "能够与照顾者进行眼神交流",
              "会用手势表达需要",
              "能够独立坐立",
              "会爬行或行走",
              "能够抓握小物品",
              "会发出有意义的声音"
            ],
            '3-5岁': [
              "能够说出自己的姓名",
              "会使用简单的礼貌用语",
              "能够独立上厕所",
              "会自己穿脱简单的衣物",
              "能够与其他孩子一起玩耍",
              "会分享玩具或食物",
              "能够遵守简单的规则",
              "会表达自己的情感",
              "能够独立吃饭",
              "会帮助做简单的家务"
            ],
            '6-8岁': [
              "能够独立完成个人卫生",
              "会整理自己的物品",
              "能够与同伴合作完成任务",
              "会主动帮助他人",
              "能够控制自己的情绪",
              "会解决简单的人际冲突",
              "能够遵守学校和家庭规则",
              "会承担适当的责任",
              "能够独立完成作业",
              "会使用基本的社交技能"
            ],
            '9-12岁': [
              "能够建立和维持友谊",
              "会处理同伴压力",
              "能够独立管理时间",
              "会制定和执行计划",
              "能够理解他人的感受",
              "会在群体中表达自己的观点",
              "能够承担更多的家庭责任",
              "会使用适当的沟通技巧",
              "能够独立解决问题",
              "会参与社区活动"
            ],
            '13-15岁': [
              "能够建立更深层的友谊",
              "会处理复杂的社交情况",
              "能够独立做出决定",
              "会为自己的行为承担责任",
              "能够理解和尊重不同观点",
              "会参与志愿服务或社会活动",
              "能够管理自己的情绪和压力",
              "会使用成熟的沟通技巧",
              "能够制定长期目标",
              "会展现领导能力"
            ]
          }
        },

        // SDS 抑郁自评量表
        'sds': {
          title: "SDS 抑郁自评量表 (成人)",
          description: "本量表由Zung编制于1965年，用于评估抑郁症状的主观感受。请根据您最近一周来的感受选择最符合的选项。很少=少于1天或没有；有时=2-3天；经常=4-5天；持续=几乎每天。",
          type: 'depression',
          optionLabels: ['很少', '有时', '经常', '持续'],
          questions: [
            "我觉得闷闷不乐，情绪低沉",
            "我觉得一天之中早晨最好",
            "老是莫名地哭出来或觉得想哭",
            "我晚上睡眠不好",
            "我吃饭像平时一样多",
            "我与异性密切接触时和以往一样感到愉快",
            "我感觉自己的体重在下降",
            "我有便秘的烦恼",
            "我觉得心跳比平时快了",
            "我无缘无故感到疲乏",
            "我的头脑跟平时一样清楚",
            "我做事情像平时一样不感到有什么困难",
            "我坐卧不安，难以保持平静",
            "我对未来感到有希望",
            "我比平时容易生气激动",
            "我觉得做出决定是容易的事",
            "我觉得自己是有用的人，别人需要我",
            "我的生活过得很有意义",
            "我认为如果我死了别人会生活得更好",
            "对于平常感兴趣的事我仍旧感兴趣"
          ],
          // SDS特殊评分规则：第2、5、6、11、12、14、16、17、18、20题反向计分
          reverseScoreItems: [1, 4, 5, 10, 11, 13, 15, 16, 17, 19]
        },

        // MADRS 蒙哥马利抑郁评定量表
        'madrs': {
          title: "MADRS 蒙哥马利抑郁评定量表 (≥15岁)",
          description: "蒙哥马利-阿斯伯格抑郁评定量表，由临床医生评定，用于评估抑郁症状的严重程度。每项评分0-6分。",
          type: 'depression',
          optionLabels: ['0分', '1分', '2分', '3分', '4分', '5分', '6分'],
          questions: [
            "表观忧愁（基于观察）- 代表沮丧、绝望、无助的感觉，通过言语、面部表情和姿态反映出来",
            "报告的忧愁（基于患者的主观体验）- 代表报告的情绪，不管是否在面谈中表现出来",
            "内疚感 - 代表自责、羞耻、自我贬低的感觉",
            "自杀意念 - 代表感到生活不值得过，自然死亡的愿望，自杀想法和自杀准备",
            "入睡困难 - 代表入睡困难或睡眠时间缩短",
            "睡眠中断 - 代表夜间睡眠不安或间断，注意早醒是另一项目",
            "早醒 - 代表比平时早醒",
            "工作和活动 - 代表工作能力下降、活动减少、效率降低和兴趣减退",
            "迟缓 - 代表思维和言语缓慢、注意力不集中、运动活动减少",
            "激越 - 代表坐立不安和烦躁不安"
          ]
        },

        // SAS 焦虑自评量表
        'sas': {
          title: "SAS 焦虑自评量表 (成人)",
          description: "本量表由W.K.Zung于1971年编制，用于评估焦虑症状的主观感受。请根据自己最近一周以来的实际情况选择。很少=没有或很少时间；有时=少部分时间；经常=相当多时间；持续=绝大部分或全部时间。",
          type: 'anxiety',
          optionLabels: ['很少', '有时', '经常', '持续'],
          questions: [
            "觉得比平常容易紧张和着急",
            "无缘无故地感到害怕",
            "容易心里烦乱或觉得惊恐",
            "觉得可能要发疯",
            "觉得一切都很好，也不会发生什么不幸",
            "手脚发抖打颤",
            "因为头痛、头颈痛和背痛而苦恼",
            "感觉容易衰弱和疲乏",
            "觉得心平气和，并且容易安静地坐着",
            "觉得心跳得很快",
            "因为一阵阵头晕而苦恼",
            "有晕倒发作，或觉得要晕倒似的",
            "吸气呼气都感到很容易",
            "手脚麻木和刺痛",
            "因为胃痛和消化不良而苦恼",
            "常常要小便",
            "手常常是干燥温暖的",
            "脸红发热",
            "容易入睡并且睡得很好",
            "做噩梦"
          ],
          // SAS特殊评分规则：第5、9、13、17、19题反向计分
          reverseScoreItems: [4, 8, 12, 16, 18]
        },

        // SPAI-C 儿童社交恐惧量表
        'spai-c': {
          title: "SPAI-C 儿童社交恐惧量表 (8-14岁)",
          description: "儿童社交恐惧症评估量表，用于评估儿童在社交情境中的恐惧和回避行为。请根据孩子的实际情况选择最符合的选项。",
          type: 'social_anxiety',
          optionLabels: ['从不或很少', '有时', '经常', '总是'],
          questions: [
            "我害怕在班上回答问题",
            "我害怕邀请其他孩子做某些事情",
            "我害怕在其他孩子面前说话",
            "我害怕在学校吃午饭",
            "我害怕写黑板",
            "我害怕和不认识的孩子说话",
            "我害怕看起来很愚蠢",
            "我害怕被其他孩子取笑",
            "我害怕其他孩子不喜欢我",
            "我害怕在其他孩子面前阅读",
            "我害怕参加聚会",
            "我害怕和其他孩子一起工作",
            "我害怕在课堂上举手",
            "我害怕在其他孩子面前表演",
            "我害怕开始与其他孩子的对话",
            "我害怕参加体育活动",
            "我害怕问老师问题",
            "我害怕在学校的走廊里走路",
            "我害怕参加学校的活动",
            "我害怕和其他孩子一起玩游戏",
            "我害怕在其他孩子面前犯错误",
            "我害怕被其他孩子注意到",
            "我害怕看起来与众不同",
            "我害怕其他孩子会说我的坏话",
            "我害怕在其他孩子面前哭泣",
            "我害怕其他孩子认为我很奇怪"
          ]
        },

        // CRIES-13 儿童创伤事件量表
        'cries-13': {
          title: "CRIES-13 儿童创伤事件量表 (8-16岁)",
          description: "儿童创伤后应激障碍筛查量表，用于评估儿童在经历创伤事件后的心理反应。请根据孩子最近一周的情况选择最符合的选项。",
          type: 'trauma',
          optionLabels: ['从不', '很少', '有时', '经常'],
          questions: [
            "我想到发生的事情时会感到不安",
            "我想到发生的事情时会感到害怕",
            "我会做关于发生事情的噩梦",
            "我会想起发生的事情的画面",
            "我会想起发生的事情时的声音",
            "我会想起发生的事情时的气味",
            "我觉得发生的事情又在重新发生",
            "我会避免提醒我想起发生事情的地方",
            "我会避免提醒我想起发生事情的人",
            "我会避免提醒我想起发生事情的活动",
            "我很难入睡",
            "我很难集中注意力",
            "我比以前更容易被吓到"
          ]
        },

        // ACEs 童年不良经历量表
        'aces': {
          title: "ACEs 童年不良经历量表 (全年龄段)",
          description: "童年不良经历量表，用于评估18岁之前的不良经历对健康的影响。请根据18岁之前的经历如实回答。",
          type: 'trauma',
          optionLabels: ['否', '是'],
          questions: [
            "父母或其他成年家庭成员是否经常或非常经常对您大喊大叫、辱骂您或羞辱您？",
            "父母或其他成年家庭成员是否经常或非常经常推搡、抓住、掌掴或向您扔东西？",
            "成年人或比您大至少5岁的人是否曾经触摸或爱抚您的身体，或让您触摸他们的身体？",
            "您是否经常或非常经常感到家里没有人爱您、认为您很重要或很特别？",
            "您是否经常或非常经常感到没有足够的食物吃、穿着脏衣服，没有人保护您？",
            "您的父母是否曾经分居或离婚？",
            "您的母亲或继母是否经常或非常经常被推搡、抓住、掌掴或被扔东西？",
            "您是否与酗酒或吸毒的人一起生活？",
            "家庭成员是否患有抑郁症或其他精神疾病，或曾试图自杀？",
            "家庭成员是否曾入狱？"
          ]
        },

        // 感觉统合能力发展评定量表
        'sensory-integration': {
          title: "儿童感觉统合能力发展评定量表 (4-12岁)",
          description: "本量表适用于4-12岁儿童感觉统合能力发展评定。请根据儿童最近半年的表现认真填写。得分低于40分为轻度感觉统合失调，低于30分为严重失调。",
          type: 'sensory',
          optionLabels: ['从不(5分)', '很少(4分)', '有时候(3分)', '常常(2分)', '总是如此(1分)'],
          questions: [
            // 第一部分 (1-14题)
            "特别爱玩会旋转的凳椅或游乐设施，而不会晕",
            "喜欢旋转或绕圈子跑，而不晕不累",
            "虽看到了仍常碰撞桌椅、旁人、柱子、门墙",
            "行动、吃饭、敲鼓、画画时双手协调不良，常忘了另一边",
            "手脚笨拙，容易跌倒",
            "俯卧地板和床上，头、颈、胸无法抬高",
            "爬上爬下，跑进跑出，不听劝阻",
            "不安地乱动，东摸西扯，不听劝阻，处罚无效",
            "喜欢惹人、捣蛋、恶作剧",
            "经常自言自语，重复别人的话，并且喜欢背诵广告语言",
            "表面左撇子，其实左右手都用，而且无固定使用哪只手",
            "分不清左右方向，鞋子衣服常常穿反",
            "对陌生地方的电梯或楼梯，不敢坐或动作缓慢",
            "组织力不佳，经常弄乱东西，不喜欢整理自己的环境",

            // 第二部分 (15-35题)
            "对亲人特别攀躁，强词夺理，到陌生环境则害怕",
            "害怕到新场合，常常不久便要求离开",
            "偏食，挑食，不吃青菜或软皮",
            "害羞，不安，喜欢孤独，不爱和别人玩",
            "容易粘妈妈或固定某个人，不喜欢陌生环境，喜欢被接抱",
            "看电视或听故事，容易大受感动，大叫或大笑，害怕恐怖镜头",
            "严重怕黑，不喜欢在空屋，到处要人陪",
            "早上懒床，晚上睡不着，上学前常拒绝到学校，放学后又不想回家",
            "容易生小病，并生病后便不想上学，常常没有原因拒绝上学",
            "常吸吮手指或咬指甲，不喜欢别人帮忙剪指甲",
            "换床睡不着，不能换被或睡衣，出外常担心睡眠问题",
            "独占性强，别人碰他的东西，常会无缘无故发脾气",
            "不喜欢和别人谈天，不喜欢和别人玩碰触游戏，视洗脸和洗澡为痛苦",
            "过分保护自己的东西，尤其讨厌别人由后面接近他",
            "怕玩沙土、水，有洁癖倾向",
            "不喜欢直接视觉接触，常必须用手来表达其需要",
            "对危险和疼痛反应迟钝或反应过于激烈",
            "听而不见，过分安静，表情冷漠又无故嘻笑",
            "过度安静或坚持奇怪玩法",
            "喜欢咬人，并且常咬固定的友伴，并无故碰坏东西",
            "内向，软弱，爱哭又常会触摸生殖器官",

            // 第三部分 (36-47题)
            "穿脱衣裤、钮扣、拉链、系鞋带动作缓慢，笨拙",
            "顽固，偏执，不合群，孤僻",
            "吃饭时常掉饭粒，口水控制不住",
            "语言不清，发音不佳，语言能力发展缓慢",
            "懒惰，行动慢，作事没有效率",
            "不喜欢翻跟头，打滚，爬高",
            "上幼儿园，仍不会洗手，擦脸，剪纸及自己擦屁股",
            "上幼儿园（大、中班）仍无法用筷子，不会拿笔，攀爬或荡秋千",
            "对小伤特别敏感，依赖他人过度照料",
            "不善于玩积木，组合东西，排队，投球",
            "怕爬高，拒走平衡木",
            "到新的陌生环境很容易迷失方向",

            // 第四部分 (48-55题)
            "看来有正常智慧，但学习阅读或做算数特别困难",
            "阅读常跳字，抄写常漏字，漏行，写字笔划常颠倒",
            "不专心，坐不住，上课常左右看",
            "用蜡笔着色或用笔写字也写不好，写字慢而常超出格子外",
            "看书容易眼酸，特别害怕数学",
            "认字能力虽好，却不知其意义，而且无法组成较长的语句",
            "混淆背景中的特殊图形，不易看出或认出",
            "对老师的要求及作业无法有效完成，常有严重挫折",

            // 第五部分 (56-58题)
            "对使用工具能力差，对劳作或家事均做不好",
            "自己的桌子或周围无法保持干净，收拾上很困难",
            "对事情反应过强，无法控制情绪，容易消极"
          ]
        },

        // ABC 异常行为量表
        'abc': {
          title: "ABC 异常行为量表 (3-16岁)",
          description: "异常行为检查表，用于评估自闭症谱系障碍相关的行为问题。请根据孩子最近的行为表现选择最符合的选项。",
          type: 'autism',
          optionLabels: ['不是问题', '轻微问题', '中等问题', '严重问题'],
          questions: [
            "过度活跃（如跑来跑去、跳上跳下）",
            "伤害自己（如咬自己、撞头）",
            "嗜睡，缺乏活力",
            "攻击性行为（如打人、踢人）",
            "寻求关注",
            "撕毁自己的衣服",
            "刻板行为（如摇摆、重复动作）",
            "大喊大叫",
            "冲动行为（如突然跑开）",
            "易怒，脾气暴躁",
            "伤害他人（如咬人、打人）",
            "破坏财物",
            "不适当的哭泣",
            "刻板的语言（如重复词语）",
            "不服从指令",
            "注意力不集中",
            "焦虑不安",
            "重复的身体动作",
            "社交退缩",
            "不适当的面部表情"
          ]
        },

        // ASRS 自闭症评估量表
        'asrs-autism': {
          title: "ASRS 自闭症评估量表 (2-18岁)",
          description: "自闭症谱系障碍评估量表，用于综合评估自闭症相关症状。请根据孩子的实际表现选择最符合的选项。",
          type: 'autism',
          optionLabels: ['从不', '很少', '有时', '经常', '总是'],
          questions: [
            "避免眼神接触或眼神接触异常",
            "对声音过度敏感或反应不足",
            "对触觉刺激过度敏感或寻求触觉刺激",
            "重复性的身体动作（如摇摆、转圈）",
            "对日常活动的改变感到不安",
            "坚持特定的例行公事或仪式",
            "对特定物品有异常的依恋",
            "语言发展迟缓或异常",
            "重复他人的话语（回声语言）",
            "难以理解非语言交流（如手势、面部表情）",
            "难以发起或维持对话",
            "缺乏社交互动的兴趣",
            "难以理解他人的情感",
            "缺乏想象性游戏",
            "对同龄人缺乏兴趣",
            "难以适应社交情境",
            "表现出不寻常的兴趣或专注",
            "对疼痛的反应异常",
            "睡眠模式异常",
            "饮食习惯异常或挑食严重"
          ]
        }
      }
    }
  },

  computed: {
    // 进度百分比
    progressPercentage() {
      const total = this.getTotalQuestionCount();
      if (total === 0) return 0;
      return Math.round(((this.currentVoiceQuestionIndex + 1) / total) * 100);
    },

    // 嘴巴状态
    mouthState() {
      if (this.assistantState === 'speaking') return 'speaking';
      if (this.assistantState === 'listening') return 'listening';
      return 'happy';
    },

    currentScaleData() {
      return this.scaleQuestions[this.currentScale] || {};
    },

    isScaleComplete() {
      if (!this.currentScaleData.questions && !this.currentScaleData.attentionQuestions) return false;

      let totalQuestions = 0;
      if (this.currentScaleData.questions) {
        totalQuestions = this.currentScaleData.questions.length;
      } else if (this.currentScaleData.attentionQuestions) {
        totalQuestions = this.currentScaleData.attentionQuestions.length +
                        this.currentScaleData.hyperactivityQuestions.length;
      }

      return this.answers.length === totalQuestions &&
             this.answers.every(answer => answer !== undefined && answer !== null);
    },

    currentOptionLabels() {
      return this.currentScaleData.optionLabels || ['没有', '有一点', '不少', '很多'];
    },

    // 计算最大分数
    maxTotalScore() {
      if (!this.currentScaleData.questions && !this.currentScaleData.attentionQuestions) return 0;

      let totalQuestions = 0;
      if (this.currentScaleData.questions) {
        totalQuestions = this.currentScaleData.questions.length;
      } else if (this.currentScaleData.attentionQuestions) {
        totalQuestions = this.currentScaleData.attentionQuestions.length +
                        this.currentScaleData.hyperactivityQuestions.length;
      }

      // 最高分选项通常是3分（0,1,2,3）
      const maxScorePerQuestion = (this.currentOptionLabels.length - 1);
      return totalQuestions * maxScorePerQuestion;
    },

    maxAttentionScore() {
      if (!this.currentScaleData.attentionQuestions) return 0;
      const maxScorePerQuestion = (this.currentOptionLabels.length - 1);
      return this.currentScaleData.attentionQuestions.length * maxScorePerQuestion;
    },

    maxHyperactivityScore() {
      if (!this.currentScaleData.hyperactivityQuestions) return 0;
      const maxScorePerQuestion = (this.currentOptionLabels.length - 1);
      return this.currentScaleData.hyperactivityQuestions.length * maxScorePerQuestion;
    },

    maxOppositionScore() {
      if (!this.currentScaleData.oppositionQuestions) return 0;
      const maxScorePerQuestion = (this.currentOptionLabels.length - 1);
      return this.currentScaleData.oppositionQuestions.length * maxScorePerQuestion;
    },

    // 计算百分比
    totalScorePercentage() {
      if (this.maxTotalScore === 0) return 0;
      return Math.round((this.totalScore / this.maxTotalScore) * 100);
    },

    attentionScorePercentage() {
      if (this.maxAttentionScore === 0) return 0;
      return Math.round((this.attentionScore / this.maxAttentionScore) * 100);
    },

    hyperactivityScorePercentage() {
      if (this.maxHyperactivityScore === 0) return 0;
      return Math.round((this.hyperactivityScore / this.maxHyperactivityScore) * 100);
    },

    oppositionScorePercentage() {
      if (this.maxOppositionScore === 0) return 0;
      return Math.round((this.oppositionScore / this.maxOppositionScore) * 100);
    }
  },

  async mounted() {
    // 初始化导航状态
    if (localStorage.getItem('navExpanded') === 'true') {
      this.navExpanded = true;
    }

    // 自动获取并填充用户信息
    await this.loadUserInfo();
  },

  methods: {
    // 加载用户信息并自动填充
    async loadUserInfo() {
      try {
        console.log('🔍 开始加载用户信息...');

        // 从localStorage获取当前用户信息
        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
        console.log('📱 本地用户信息:', currentUser);

        if (currentUser && (currentUser.userId || currentUser.username)) {
          // 如果有用户信息，检查userId有效性
          const validUserId = currentUser.userId && !currentUser.userId.startsWith('user_') 
            ? currentUser.userId 
            : currentUser.patientId;
            
          if (!validUserId) {
            console.warn('⚠️ 无有效用户ID，需要重新登录');
            this.showErrorMessage('用户信息异常，请重新登录');
            this.$router.push('/login');
            return;
          }
          
          this.userInfo = {
            userId: validUserId,
            username: currentUser.username || currentUser.patientName || '新用户',
            age: currentUser.age || 0,
            gender: currentUser.gender || '',
            phone: currentUser.phone || currentUser.phoneNumber || '',
            avatar: currentUser.avatar || '',
            ...currentUser
          };
          console.log('✅ 用户信息加载成功:', this.userInfo);
        } else {
          console.warn('⚠️ 未找到用户信息，需要重新登录');
          // 跳转到登录页面，不创建默认用户
          this.showErrorMessage('请先登录您的账户');
          this.$router.push('/login');
          return;
        }

      } catch (error) {
        console.error('❌ 加载用户信息失败:', error);

        // 错误时跳转到登录页面，不创建默认用户
        this.showErrorMessage('加载用户信息失败，请重新登录');
        this.$router.push('/login');
        return;
      }
    },

    // 获取风险等级
    getRiskLevel(percentage) {
      if (percentage >= 75) return 'high';
      if (percentage >= 50) return 'medium';
      if (percentage >= 25) return 'low';
      return 'minimal';
    },

    getRiskText(percentage) {
      if (percentage >= 75) return '高风险';
      if (percentage >= 50) return '中风险';
      if (percentage >= 25) return '低风险';
      return '极低风险';
    },

    // 从文本获取风险等级
    getRiskLevelFromText(text) {
      if (!text) return 'low';

      const lowerText = text.toLowerCase();
      if (lowerText.includes('高') || lowerText.includes('重度') || lowerText.includes('high')) return 'high';
      if (lowerText.includes('中') || lowerText.includes('medium')) return 'medium';
      if (lowerText.includes('低') || lowerText.includes('轻度') || lowerText.includes('low')) return 'low';
      return 'minimal';
    },

    // 获取优先级文本
    getPriorityText(priority) {
      if (!priority) return '一般';

      const lowerPriority = priority.toLowerCase();
      if (lowerPriority === 'high') return '高优先级';
      if (lowerPriority === 'medium') return '中优先级';
      if (lowerPriority === 'low') return '低优先级';
      return '一般';
    },

    // 显示错误消息
    showErrorMessage(message) {
      console.error('❌ 错误:', message);
      // 使用简单的alert显示错误，也可以替换为更好的UI组件
      alert(message);
    },

    // 获取分类文本
    getCategoryText(category) {
      if (!category) return '一般建议';

      const lowerCategory = category.toLowerCase();
      if (lowerCategory === 'immediate') return '立即行动';
      if (lowerCategory === 'short-term') return '短期建议';
      if (lowerCategory === 'long-term') return '长期建议';
      return '一般建议';
    },

    // 搜索处理
    handleSearch(query) {
      console.log('搜索:', query);
    },

    // 通知相关
    showNotifications() {
      console.log('显示通知');
    },

    showMessages() {
      console.log('显示消息');
    },

    showSettings() {
      console.log('显示设置');
    },

    showUserMenu() {
      console.log('显示用户菜单');
    },

    // 导航控制
    expandNav() {
      this.navExpanded = true;
    },

    collapseNav() {
      this.navExpanded = false;
    },

    // 选择量表
    selectScale(scaleType) {
      this.currentScale = scaleType;
      this.scaleTitle = this.scaleQuestions[scaleType].title;
      this.scaleDescription = this.scaleQuestions[scaleType].description;

      // 显示输入方式选择页面
      this.showScaleSelectionPage = false;
      this.showInputMethodSelection = true;

      // 重置所有状态
      this.showResult = false;
      this.totalScore = 0;
      this.attentionScore = 0;
      this.hyperactivityScore = 0;
      this.oppositionScore = null;
      this.interpretationText = '请完成量表评测以查看结果解读。';
      this.riskLevel = '';
      this.isSubmitting = false;

      // 重置语音相关状态
      this.currentVoiceQuestionIndex = 0;
      this.isSpeaking = false;
      this.isListening = false;
      this.voiceRecognitionResult = '';
      this.assistantState = 'idle';
      this.assistantStatusText = '您好！我是您的AI助手，准备开始评估';
      this.riskAlert.show = false;
    },

    // 选择输入方式
    selectInputMethod(method) {
      this.inputMethod = method;
      this.showInputMethodSelection = false;

      // 记录量表开始时间
      this.scaleStartTime = new Date().toISOString();
      console.log('📝 量表开始时间已记录:', this.scaleStartTime);
      console.log('🎤 选择的输入方式:', method);

      this.initializeAnswers();

      // 如果选择语音模式，初始化语音功能
      if (method === 'voice') {
        this.initializeVoiceFeatures();
        // 延迟自动朗读第一题
        this.$nextTick(() => {
          setTimeout(() => {
            this.readCurrentQuestion();
          }, 1500);
        });
      }
    },

    // 返回输入方式选择页面
    goBackToInputMethodSelection() {
      this.showInputMethodSelection = true;
      this.showResult = false;

      // 停止语音功能
      this.stopAllVoiceFeatures();
    },

    // 初始化答案数组
    initializeAnswers() {
      let totalQuestions = 0;

      if (this.currentScale === 'cbcl') {
        // CBCL量表：内化问题 + 外化问题
        if (this.currentScaleData.internalizing && this.currentScaleData.externalizing) {
          totalQuestions = this.currentScaleData.internalizing.length + this.currentScaleData.externalizing.length;
        }
      } else if (this.currentScale === 'social-living-skills') {
        // 社会生活能力量表：根据选择的年龄组
        if (this.selectedAgeGroup && this.currentScaleData.ageGroups) {
          totalQuestions = this.currentScaleData.ageGroups[this.selectedAgeGroup]?.length || 0;
        }
      } else if (this.currentScaleData.questions) {
        // 通用量表
        totalQuestions = this.currentScaleData.questions.length;
      } else if (this.currentScaleData.attentionQuestions) {
        // ADHD量表
        totalQuestions = this.currentScaleData.attentionQuestions.length +
                        this.currentScaleData.hyperactivityQuestions.length;
      }

      this.answers = new Array(totalQuestions).fill(null);
    },

    // 获取特殊选项的分数（用于儿童注意力水平测评量表）
    getSpecialOptionScore(questionIndex, optionIndex) {
      // 儿童注意力水平测评量表的特殊评分规则
      if (this.currentScale === 'attention-level') {
        // 根据题目和选项确定分数
        const scoreMap = {
          0: [1, 3, 2], 1: [1, 2, 3], 2: [1, 3, 2], 3: [1, 3, 2], 4: [2, 1, 3],
          5: [1, 2, 3], 6: [1, 3, 2], 7: [1, 3, 2], 8: [2, 3, 1], 9: [3, 1, 2],
          10: [2, 3, 1], 11: [1, 3, 2], 12: [2, 3, 1], 13: [2, 1, 3], 14: [2, 1, 3]
        };
        return scoreMap[questionIndex] ? scoreMap[questionIndex][optionIndex] : optionIndex;
      }
      return optionIndex;
    },

    // 更新答案
    updateAnswer(category, questionIndex, value) {
      let answerIndex;

      if (category === 'general') {
        // 通用问题格式
        answerIndex = questionIndex;
      } else if (category === 'attention') {
        answerIndex = questionIndex;
      } else if (category === 'hyperactivity') {
        answerIndex = this.currentScaleData.attentionQuestions.length + questionIndex;
      } else if (category === 'opposition') {
        answerIndex = this.currentScaleData.attentionQuestions.length +
                     this.currentScaleData.hyperactivityQuestions.length + questionIndex;
      }

      // Vue 3中直接赋值即可，不需要使用$set
      this.answers[answerIndex] = parseInt(value);
    },

    // 提交量表
    async submitScale() {
      if (!this.isScaleComplete) {
        alert('请完成所有问题后再提交');
        return;
      }

      // 记录开始时间（如果没有记录的话）
      if (!this.scaleStartTime) {
        this.scaleStartTime = new Date().toISOString();
      }

      // 显示提交中状态
      this.isSubmitting = true;

      try {
        // 显示详细信息收集弹窗
        this.showDetailInfoDialog = true;
        this.isSubmitting = false; // 重置提交状态，等待用户选择

      } catch (error) {
        console.error('❌ 量表提交失败:', error);
        this.showSubmitStatus('量表提交失败，请重试', 'error');
        this.isSubmitting = false;
      }
    },

    // 处理详细信息收集的选择
    async handleDetailInfoChoice(hasDetailInfo, detailInfo = null) {
      this.showDetailInfoDialog = false;
      this.isSubmitting = true;

      // 记录是否包含详细信息
      console.log(`用户${hasDetailInfo ? '提供' : '跳过'}了详细信息收集`);

      try {
        // 准备用户详细信息
        this.userDetailInfo = detailInfo;

        // 直接进行传统分析
        this.performTraditionalAnalysis();

        // 同步量表结果到RuoYi后端（直接发送给医生）
        const endTime = new Date().toISOString();
        const syncResult = await this.syncScaleDataToRuoYi(endTime);

        // 不显示详细分析结果给患者，只显示提交状态
        if (syncResult && syncResult.success) {
          this.showSubmitStatus('量表提交成功！分析结果已发送给医生，医生将及时查看并联系您', 'success');
          // 显示简化的完成界面
          this.showSimpleCompletionMessage();
        } else {
          this.showSubmitStatus('量表提交失败，请稍后重试', 'error');
        }

      } catch (error) {
        console.error('❌ 量表提交失败:', error);
        this.showSubmitStatus('量表提交失败，请重试', 'error');
      } finally {
        this.isSubmitting = false;
      }
    },

    // 同步量表数据到RuoYi后端
    async syncScaleDataToRuoYi(endTime) {
      let scaleData = null; // 在函数作用域声明变量

      try {
        console.log('🚀 开始同步量表数据到RuoYi后端...');

        // 检查localStorage中的所有相关数据
        console.log('🔍 localStorage检查:');
        console.log('- currentUser:', localStorage.getItem('currentUser'));
        console.log('- userInfo:', localStorage.getItem('userInfo'));
        console.log('- 所有localStorage键:', Object.keys(localStorage));

        const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
        console.log('📱 当前用户信息:', currentUser);
        console.log('📱 用户信息字段检查:', {
          userId: currentUser.userId,
          username: currentUser.username,
          patientName: currentUser.patientName,
          age: currentUser.age,
          gender: currentUser.gender,
          patientId: currentUser.patientId
        });

        // 🔥 为医生端生成专业分析数据（不在前端显示）
        const doctorAnalysisData = this.generateDoctorAnalysisData();

        // 获取用户信息，确保使用有效的用户ID（不生成时间戳ID）
        const validUserId = currentUser.userId && !currentUser.userId.startsWith('user_') 
          ? currentUser.userId 
          : currentUser.patientId;
          
        if (!validUserId) {
          console.error('❌ 无有效用户ID，无法提交测试数据');
          this.showErrorMessage('用户信息异常，请重新登录后再试');
          this.$router.push('/login');
          return;
        }
        
        const finalUserId = validUserId;
        const finalUsername = currentUser.username || currentUser.patientName || '未知用户';
        const finalAge = parseInt(currentUser.age) || 0;
        
        // 转换性别格式 - 支持多种格式
        let finalGender = currentUser.gender || '未知';
        if (finalGender === 'male' || finalGender === '0') {
          finalGender = '男';
        } else if (finalGender === 'female' || finalGender === '1') {
          finalGender = '女';
        } else if (!['男', '女', '未知', '0', '1', '2'].includes(finalGender)) {
          finalGender = '未知';
        }
        
        const finalScaleName = this.currentScaleData?.title || this.scaleTitle || 'CDI-2抑郁量表';

        console.log('🔍 最终用户信息:', {
          userId: finalUserId,
          username: finalUsername,
          age: finalAge,
          gender: finalGender,
          scaleName: finalScaleName
        });

        // 🔥 构建简化的量表数据（只包含医生端需要的字段）
        scaleData = {
          // 用户信息 - 确保有效的用户数据
          userId: String(finalUserId),
          username: String(finalUsername),
          age: Number(finalAge),
          gender: String(finalGender),

          // 量表信息
          scaleName: String(finalScaleName),
          scaleType: String(this.currentScale || 'cdi-2'),

          // 分数信息
          totalScore: Number(this.totalScore || 0),
          maxTotalScore: Number(this.maxTotalScore || 54), // CDI-2默认满分54分

          // 时间信息
          startTime: this.scaleStartTime || new Date().toISOString(),
          endTime: endTime,

          // 🔥 医生端专业分析数据（患者端不显示）
          severityLevel: doctorAnalysisData.severityLevel || '轻度',
          analysisMethod: doctorAnalysisData.analysisMethod || 'traditional',
          aiConfidence: doctorAnalysisData.aiConfidence || 0.8,
          professionalAdvice: doctorAnalysisData.professionalAdvice || '建议进行专业评估',
          analysisDetails: doctorAnalysisData.analysisDetails || JSON.stringify({
            scores: { totalScore: this.totalScore, maxScore: this.maxTotalScore },
            risk: this.riskLevel || '中等',
            recommendations: ['建议咨询专业医生', '关注情绪变化']
          }),
          modelUsed: doctorAnalysisData.modelUsed || 'traditional-analysis',

          // 详细量表数据 - 添加完整的答题信息
          scaleData: JSON.stringify({
            answers: this.answers || [],
            questions: this.getAllQuestions() || [],
            completionTime: new Date().toISOString(),
            inputMethod: this.inputMethod || 'manual'
          })
        };

        console.log('📤 构建的量表数据:', scaleData);

        // 验证数据完整性
        const validationResult = this.validateScaleData(scaleData);
        if (!validationResult.isValid) {
          console.warn('⚠️ 量表数据验证失败:', validationResult.errors);
          this.showSubmitStatus('数据验证失败：' + validationResult.errors.join(', '), 'warning');
          return { success: false, message: '数据验证失败' };
        }

        console.log('📤 准备发送的量表数据:', scaleData);
        console.log('📊 数据类型检查:', this.checkDataTypes(scaleData));

        const result = await this.submitScaleToRuoYi(scaleData);

        if (result.success) {
          this.showSubmitStatus('量表提交成功！数据已同步到医生端系统', 'success');
          console.log('✅ 量表数据已成功发送到RuoYi后端');

          // 记录成功的同步
          this.recordSyncSuccess(scaleData, result.data);
        } else {
          this.showSubmitStatus('量表提交失败，请稍后重试', 'error');
          console.warn('⚠️ 发送到RuoYi失败:', result.message);

          // 记录失败的同步，用于后续重试
          this.recordSyncFailure(scaleData, result.error);
        }

        return result;
      } catch (error) {
        console.error('❌ 发送量表数据到RuoYi时出错:', error);
        this.showSubmitStatus('量表提交失败，请检查网络连接后重试', 'error');

        // 记录异常（只有在scaleData存在时才记录）
        if (scaleData) {
          this.recordSyncFailure(scaleData, error.message);
        }
        return { success: false, message: error.message };
      }
    },

    // 提交量表数据到RuoYi后端
    async submitScaleToRuoYi(scaleData) {
      try {
        console.log('📊 使用统一API提交量表数据...');

        // 使用优化的AI分析提交方法
        const result = await submitScaleWithAIAnalysis(scaleData);

        if (result.success) {
          console.log('✅ 量表数据提交成功:', result);

          // 如果AI分析已完成，显示通知
          if (result.aiAnalysisStatus === 'completed' && result.data.aiAnalysis) {
            console.log('🤖 AI分析已完成');
            this.showNotification('AI分析完成', 'AI医生已完成分析，请前往个人中心查看详细报告', 'success');
          } else {
            console.log('⏳ AI分析正在后台进行中...');
            this.showNotification('提交成功', 'AI医生正在分析您的量表结果，请稍后在个人中心查看详细报告', 'info');
          }

          return {
            success: true,
            data: result.data,
            message: '量表数据已成功发送到医生端系统，AI分析' +
                    (result.aiAnalysisStatus === 'completed' ? '已完成' : '正在进行中')
          };
        } else {
          console.log('❌ 量表数据提交失败:', result);
          return {
            success: false,
            error: result.error,
            message: '发送到医生端系统失败，请稍后重试'
          };
        }
      } catch (error) {
        console.error('❌ 提交量表数据异常:', error);

        return {
          success: false,
          error: error.message,
          message: '发送到医生端系统失败，请检查网络连接后重试'
        };
      }
    },

    // 显示通知消息
    showNotification(title, message, type = 'info') {
      console.log(`📢 ${type.toUpperCase()}: ${title} - ${message}`);

      // 这里可以集成具体的通知组件，比如Element Plus的ElNotification
      // 目前使用简单的alert作为示例
      if (type === 'success') {
        alert(`✅ ${title}\n${message}`);
      } else if (type === 'error') {
        alert(`❌ ${title}\n${message}`);
      } else {
        alert(`ℹ️ ${title}\n${message}`);
      }
    },

    // 显示简化的完成消息（不显示详细分析结果）
    showSimpleCompletionMessage() {
      // 显示简化的完成界面，包含患者友好的结果
      this.showResult = true; // 显示简化的结果界面

      // 显示成功消息
      this.showSubmitStatus('量表评测完成！分析结果已发送给医生，医生将根据您的情况及时联系您。', 'success');

      // 滚动到结果区域
      this.$nextTick(() => {
        const resultSection = document.querySelector('.result-section');
        if (resultSection) {
          resultSection.scrollIntoView({ behavior: 'smooth' });
        }
      });
    },

    // 数据验证方法（简化版）
    validateScaleData(data) {
      const errors = [];

      // 必填字段验证
      if (!data.userId || data.userId === 'anonymous') {
        errors.push('用户ID不能为空或匿名');
      }

      if (!data.username) {
        errors.push('用户名不能为空');
      }

      if (!data.scaleName || data.scaleName === '未知量表') {
        errors.push('量表名称不能为空');
      }

      // 数值字段验证
      if (typeof data.totalScore !== 'number' || data.totalScore < 0) {
        errors.push('总分必须为非负数');
      }

      if (typeof data.maxTotalScore !== 'number' || data.maxTotalScore <= 0) {
        errors.push('量表满分必须为正数');
      }

      // 可选字段验证
      if (data.age !== null && data.age !== undefined &&
          (typeof data.age !== 'number' || data.age < 0 || data.age > 150)) {
        errors.push('年龄必须为0-150之间的有效数字');
      }

      if (data.gender && !['男', '女', '未知', '0', '1', '2'].includes(data.gender)) {
        errors.push('性别字段格式不正确');
      }

      return {
        isValid: errors.length === 0,
        errors: errors
      };
    },

    // 数据类型检查
    checkDataTypes(data) {
      return {
        userId: typeof data.userId,
        username: typeof data.username,
        age: typeof data.age,
        gender: typeof data.gender,
        scaleName: typeof data.scaleName,
        scaleType: typeof data.scaleType,
        totalScore: typeof data.totalScore,
        maxTotalScore: typeof data.maxTotalScore,
        severityLevel: typeof data.severityLevel,
        analysisMethod: typeof data.analysisMethod,
        aiConfidence: typeof data.aiConfidence,
        professionalAdvice: typeof data.professionalAdvice,
        analysisDetails: typeof data.analysisDetails,
        modelUsed: typeof data.modelUsed,
        startTime: typeof data.startTime,
        endTime: typeof data.endTime,
        duration: typeof data.duration,
        riskLevel: typeof data.riskLevel,
        createdAt: typeof data.createdAt
      };
    },

    // 格式化日期为RuoYi后端格式（现在由axios.js中的formatters处理）
    // formatDateForRuoYi方法已移至axios.js的formatters中统一处理

    // 记录同步成功
    recordSyncSuccess(data, response) {
      const syncRecord = {
        timestamp: new Date().toISOString(),
        status: 'success',
        data: data,
        response: response,
        userId: data.userId
      };

      // 保存到本地存储用于统计
      const syncHistory = JSON.parse(localStorage.getItem('scaleSyncHistory') || '[]');
      syncHistory.unshift(syncRecord);

      // 只保留最近50条记录
      if (syncHistory.length > 50) {
        syncHistory.splice(50);
      }

      localStorage.setItem('scaleSyncHistory', JSON.stringify(syncHistory));
      console.log('📝 同步成功记录已保存');
    },

    // 记录同步失败
    recordSyncFailure(data, error) {
      const syncRecord = {
        timestamp: new Date().toISOString(),
        status: 'failed',
        data: data,
        error: error,
        userId: data.userId,
        retryCount: 0
      };

      // 保存到本地存储用于重试
      const failedSyncs = JSON.parse(localStorage.getItem('failedScaleSyncs') || '[]');
      failedSyncs.unshift(syncRecord);

      // 只保留最近20条失败记录
      if (failedSyncs.length > 20) {
        failedSyncs.splice(20);
      }

      localStorage.setItem('failedScaleSyncs', JSON.stringify(failedSyncs));
      console.log('📝 同步失败记录已保存，可用于后续重试');
    },

    // 重试失败的同步
    async retryFailedSyncs() {
      const failedSyncs = JSON.parse(localStorage.getItem('failedScaleSyncs') || '[]');
      const retryableItems = failedSyncs.filter(item => item.retryCount < 3);

      if (retryableItems.length === 0) {
        console.log('📝 没有需要重试的同步记录');
        return;
      }

      console.log(`🔄 开始重试 ${retryableItems.length} 条失败的同步记录`);

      for (const item of retryableItems) {
        try {
          const result = await this.submitScaleToRuoYi(item.data);

          if (result.success) {
            // 重试成功，从失败列表中移除
            const index = failedSyncs.findIndex(sync => sync.timestamp === item.timestamp);
            if (index > -1) {
              failedSyncs.splice(index, 1);
            }

            // 记录成功
            this.recordSyncSuccess(item.data, result.data);
            console.log(`✅ 重试成功: ${item.timestamp}`);
          } else {
            // 重试失败，增加重试次数
            item.retryCount++;
            console.log(`❌ 重试失败: ${item.timestamp}, 重试次数: ${item.retryCount}`);
          }
        } catch (error) {
          item.retryCount++;
          console.error(`❌ 重试异常: ${item.timestamp}`, error);
        }
      }

      // 更新失败记录
      localStorage.setItem('failedScaleSyncs', JSON.stringify(failedSyncs));
    },

    // 🚫 已禁用：保存量表结果到本地存储
    // saveScaleResultToLocal(endTime) {
    //   // 本地保存功能已移除，所有数据直接提交到医生端系统
    // },

    // 获取设备信息
    getDeviceInfo() {
      return {
        userAgent: navigator.userAgent,
        platformInfo: navigator.userAgentData?.platform || 'unknown',
        language: navigator.language,
        screenResolution: `${screen.width}x${screen.height}`,
        colorDepth: screen.colorDepth,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
        timestamp: new Date().toISOString()
      };
    },

    // 显示提交状态
    showSubmitStatus(message, type = 'info') {
      // 创建状态提示元素
      const statusDiv = document.createElement('div');
      statusDiv.className = `submit-status ${type}`;
      statusDiv.innerHTML = `
        <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
        ${message}
      `;

      // 添加样式
      statusDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        border-radius: 6px;
        color: white;
        font-weight: 500;
        z-index: 10000;
        animation: slideInRight 0.3s ease-out;
        background-color: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#f59e0b'};
      `;

      document.body.appendChild(statusDiv);

      // 3秒后自动移除
      setTimeout(() => {
        if (statusDiv.parentNode) {
          statusDiv.style.animation = 'slideOutRight 0.3s ease-in';
          setTimeout(() => {
            document.body.removeChild(statusDiv);
          }, 300);
        }
      }, 3000);
    },







    // 传统分析方法（备用）
    performTraditionalAnalysis() {
      console.log('📊 使用传统分析方法');

      // 计算总分（考虑特殊评分规则）
      this.totalScore = this.calculateTotalScore();

      // 计算分项分数
      this.calculateSubScores();

      // 更新风险等级和生成解释
      this.updateRiskLevel();
      this.generateTraditionalInterpretation();
    },

    // 计算总分（处理特殊评分规则）
    calculateTotalScore() {
      let totalScore = 0;
      const scaleData = this.currentScaleData;

      // 检查是否有反向计分项目
      if (scaleData.reverseScoreItems) {
        this.answers.forEach((score, index) => {
          if (scaleData.reverseScoreItems.includes(index)) {
            // 反向计分：1->4, 2->3, 3->2, 4->1
            totalScore += (4 - score);
          } else {
            totalScore += score;
          }
        });
      } else {
        // 普通计分
        totalScore = this.answers.reduce((sum, score) => sum + score, 0);
      }

      // SDS和SAS需要乘以1.25并取整
      if (this.currentScale === 'sds' || this.currentScale === 'sas') {
        totalScore = Math.round(totalScore * 1.25);
      }

      return totalScore;
    },



    // 计算分项分数
    calculateSubScores() {
      if (this.currentScaleData.questions) {
        // 通用量表 - 没有分项
        this.attentionScore = null;
        this.hyperactivityScore = null;
        this.oppositionScore = null;
      } else if (this.currentScaleData.attentionQuestions) {
        // ADHD量表 - 计算分项分数
        const attentionLength = this.currentScaleData.attentionQuestions.length;
        const hyperactivityLength = this.currentScaleData.hyperactivityQuestions.length;

        this.attentionScore = this.answers.slice(0, attentionLength).reduce((sum, score) => sum + score, 0);
        this.hyperactivityScore = this.answers.slice(attentionLength, attentionLength + hyperactivityLength)
                                             .reduce((sum, score) => sum + score, 0);

        // 如果有对立违抗问题
        if (this.currentScaleData.oppositionQuestions) {
          const oppositionStart = attentionLength + hyperactivityLength;
          this.oppositionScore = this.answers.slice(oppositionStart).reduce((sum, score) => sum + score, 0);
        } else {
          this.oppositionScore = null;
        }
      }
    },



    // 更新风险等级
    updateRiskLevel() {
      const percentage = this.totalScorePercentage;
      this.riskLevel = this.getRiskText(percentage);
      console.log('📊 风险等级已更新:', this.riskLevel, '(百分比:', percentage + '%)');
    },

    // 传统解释生成方法（患者端显示）
    generateTraditionalInterpretation() {
      let interpretation = '📊 传统分析结果\n\n';
      const scaleType = this.currentScaleData.type;

      interpretation += `总分：${this.totalScore} 分\n`;
      if (this.attentionScore !== null) {
        interpretation += `注意力缺陷分数：${this.attentionScore} 分\n`;
      }
      if (this.hyperactivityScore !== null) {
        interpretation += `多动/冲动分数：${this.hyperactivityScore} 分\n`;
      }
      interpretation += '\n';

      switch (scaleType) {
        case 'depression':
          if (this.totalScore >= 15) {
            interpretation += '评测结果显示存在明显的抑郁症状。建议尽快咨询专业心理医生进行进一步评估和治疗。';
          } else if (this.totalScore >= 10) {
            interpretation += '评测结果显示存在轻度到中度的抑郁症状。建议关注情绪变化，考虑寻求专业帮助。';
          } else if (this.totalScore >= 5) {
            interpretation += '评测结果显示存在轻微的抑郁症状。建议保持积极的生活方式，注意情绪调节。';
          } else {
            interpretation += '评测结果显示抑郁症状较轻微。继续保持良好的心理状态和生活习惯。';
          }
          break;

        case 'anxiety':
          if (this.totalScore >= 15) {
            interpretation += '评测结果显示存在明显的焦虑症状。建议咨询专业心理医生进行进一步评估和治疗。';
          } else if (this.totalScore >= 10) {
            interpretation += '评测结果显示存在中度的焦虑症状。建议学习放松技巧，必要时寻求专业帮助。';
          } else if (this.totalScore >= 5) {
            interpretation += '评测结果显示存在轻度的焦虑症状。建议注意压力管理和情绪调节。';
          } else {
            interpretation += '评测结果显示焦虑症状较轻微。继续保持良好的心理状态。';
          }
          break;

        case 'adhd':
          if (this.attentionScore >= 14 || this.hyperactivityScore >= 14) {
            interpretation += '评测结果显示存在明显的ADHD症状。建议咨询专业医生进行进一步评估和诊断。';
          } else if (this.attentionScore >= 10 || this.hyperactivityScore >= 10) {
            interpretation += '评测结果显示存在一定程度的ADHD症状。建议关注行为表现，必要时咨询专业人士。';
          } else {
            interpretation += '评测结果显示ADHD症状较轻微。继续保持良好的生活和学习习惯。';
          }
          break;

        case 'sleep':
          if (this.totalScore >= 15) {
            interpretation += '评测结果显示存在明显的睡眠问题。建议咨询专业医生进行睡眠评估和治疗。';
          } else if (this.totalScore >= 10) {
            interpretation += '评测结果显示存在中度的睡眠问题。建议改善睡眠环境和习惯。';
          } else if (this.totalScore >= 5) {
            interpretation += '评测结果显示存在轻度的睡眠问题。建议注意睡眠卫生。';
          } else {
            interpretation += '评测结果显示睡眠质量良好。继续保持良好的睡眠习惯。';
          }
          break;

        case 'attention':
          // 儿童注意力水平测评量表特殊评分
          if (this.totalScore >= 35) {
            interpretation += '🎉 孩子的注意力非常棒！注意力水平优秀，能够很好地维持专注状态。';
          } else if (this.totalScore >= 25) {
            interpretation += '👍 孩子的注意力基本上能够维持日常学习和生活的需要，表现良好。';
          } else {
            interpretation += '⚠️ 警钟已经敲响了，孩子的注意力急待提高！建议采取针对性的注意力训练措施。';
          }
          break;

        case 'sensory':
          // 感觉统合能力发展评定量表特殊评分（分数越低越有问题）
          if (this.totalScore < 30) {
            interpretation += '⚠️ 评测结果显示存在严重的感觉统合失调问题。建议尽快咨询专业的感觉统合治疗师进行评估和干预。';
          } else if (this.totalScore < 40) {
            interpretation += '⚠️ 评测结果显示存在轻度的感觉统合失调问题。建议关注孩子的感觉统合发展，考虑进行相关训练。';
          } else {
            interpretation += '✅ 评测结果显示感觉统合能力发展正常。继续保持良好的感觉统合发展环境。';
          }
          break;

        default:
          // 处理SDS和SAS的特殊解释
          if (this.currentScale === 'sds') {
            // SDS抑郁自评量表解释（分界值53分）
            if (this.totalScore >= 72) {
              interpretation += '⚠️ 评测结果显示存在重度抑郁症状。强烈建议立即寻求专业心理医生或精神科医生的帮助。';
            } else if (this.totalScore >= 63) {
              interpretation += '⚠️ 评测结果显示存在中度抑郁症状。建议咨询专业心理医生进行进一步评估和治疗。';
            } else if (this.totalScore >= 53) {
              interpretation += '⚠️ 评测结果显示存在轻度抑郁症状。建议关注情绪变化，考虑寻求专业心理咨询。';
            } else {
              interpretation += '✅ 评测结果显示抑郁症状在正常范围内。继续保持良好的心理状态和生活习惯。';
            }
          } else if (this.currentScale === 'sas') {
            // SAS焦虑自评量表解释（分界值50分）
            if (this.totalScore >= 69) {
              interpretation += '⚠️ 评测结果显示存在重度焦虑症状。强烈建议立即寻求专业心理医生或精神科医生的帮助。';
            } else if (this.totalScore >= 60) {
              interpretation += '⚠️ 评测结果显示存在中度焦虑症状。建议咨询专业心理医生进行进一步评估和治疗。';
            } else if (this.totalScore >= 50) {
              interpretation += '⚠️ 评测结果显示存在轻度焦虑症状。建议学习放松技巧，注意压力管理。';
            } else {
              interpretation += '✅ 评测结果显示焦虑症状在正常范围内。继续保持良好的心理状态。';
            }
          } else if (this.currentScale === 'madrs') {
            // MADRS蒙哥马利抑郁评定量表解释（总分0-60分）
            if (this.totalScore >= 35) {
              interpretation += '⚠️ 评测结果显示存在严重抑郁症状。强烈建议立即寻求专业精神科医生的治疗。';
            } else if (this.totalScore >= 20) {
              interpretation += '⚠️ 评测结果显示存在中度抑郁症状。建议咨询专业医生进行进一步评估和治疗。';
            } else if (this.totalScore >= 7) {
              interpretation += '⚠️ 评测结果显示存在轻度抑郁症状。建议关注情绪变化，考虑寻求专业咨询。';
            } else {
              interpretation += '✅ 评测结果显示抑郁症状在正常范围内。';
            }
          } else if (this.currentScale === 'spai-c') {
            // SPAI-C社交恐惧量表解释
            const maxScore = this.currentScaleData.questions.length * 3; // 最高分
            const percentage = (this.totalScore / maxScore) * 100;
            if (percentage >= 70) {
              interpretation += '⚠️ 评测结果显示存在明显的社交恐惧症状。建议咨询儿童心理专家进行评估和干预。';
            } else if (percentage >= 50) {
              interpretation += '⚠️ 评测结果显示存在中度的社交焦虑。建议关注孩子的社交发展，考虑社交技能训练。';
            } else if (percentage >= 30) {
              interpretation += '⚠️ 评测结果显示存在轻度的社交焦虑。建议鼓励孩子参与社交活动。';
            } else {
              interpretation += '✅ 评测结果显示社交恐惧症状在正常范围内。';
            }
          } else if (this.currentScale === 'cries-13') {
            // CRIES-13创伤事件量表解释
            if (this.totalScore >= 30) {
              interpretation += '⚠️ 评测结果显示存在明显的创伤后应激症状。强烈建议寻求专业创伤治疗师的帮助。';
            } else if (this.totalScore >= 17) {
              interpretation += '⚠️ 评测结果显示可能存在创伤后应激症状。建议咨询专业心理医生进行评估。';
            } else {
              interpretation += '✅ 评测结果显示创伤后应激症状在可接受范围内。';
            }
          } else if (this.currentScale === 'aces') {
            // ACEs童年不良经历量表解释
            if (this.totalScore >= 4) {
              interpretation += '⚠️ 评测结果显示童年不良经历较多。建议寻求专业心理咨询，关注长期健康影响。';
            } else if (this.totalScore >= 2) {
              interpretation += '⚠️ 评测结果显示存在一定的童年不良经历。建议关注心理健康，必要时寻求专业支持。';
            } else if (this.totalScore >= 1) {
              interpretation += '评测结果显示存在少量童年不良经历。建议保持健康的生活方式和积极的心态。';
            } else {
              interpretation += '✅ 评测结果显示童年经历相对良好。';
            }
          } else if (this.currentScale === 'social-anxiety') {
            // 儿童社交焦虑量表解释
            const maxScore = this.currentScaleData.questions.length * (this.currentScaleData.optionLabels.length - 1);
            const percentage = (this.totalScore / maxScore) * 100;
            if (percentage >= 70) {
              interpretation += '⚠️ 评测结果显示存在严重的社交焦虑症状。强烈建议咨询儿童心理专家进行专业评估和干预治疗。';
            } else if (percentage >= 50) {
              interpretation += '⚠️ 评测结果显示存在中度的社交焦虑症状。建议进行社交技能训练，必要时寻求专业帮助。';
            } else if (percentage >= 30) {
              interpretation += '⚠️ 评测结果显示存在轻度的社交焦虑症状。建议鼓励孩子参与适当的社交活动，增强自信心。';
            } else {
              interpretation += '✅ 评测结果显示社交焦虑症状在正常范围内。继续保持良好的社交发展。';
            }
          } else if (this.currentScale === 'cbcl') {
            // CBCL儿童行为量表解释
            const internalizingScore = this.calculateInternalizingScore();
            const externalizingScore = this.calculateExternalizingScore();

            interpretation += `内化问题分数：${internalizingScore} 分\n`;
            interpretation += `外化问题分数：${externalizingScore} 分\n\n`;

            if (internalizingScore >= 20 || externalizingScore >= 20) {
              interpretation += '⚠️ 评测结果显示存在明显的行为问题。强烈建议咨询儿童心理专家或发育行为儿科医生进行专业评估。';
            } else if (internalizingScore >= 15 || externalizingScore >= 15) {
              interpretation += '⚠️ 评测结果显示存在中度的行为问题。建议关注孩子的行为表现，考虑行为管理策略。';
            } else if (internalizingScore >= 10 || externalizingScore >= 10) {
              interpretation += '⚠️ 评测结果显示存在轻度的行为问题。建议加强正面管教和情绪支持。';
            } else {
              interpretation += '✅ 评测结果显示行为问题在正常范围内。继续保持良好的教养方式。';
            }
          } else if (this.currentScale === 'social-living-skills') {
            // 社会生活能力量表解释
            const ageGroup = this.getSelectedAgeGroup();
            const maxScore = this.getCurrentAgeGroupQuestions().length * (this.currentScaleData.optionLabels.length - 1);
            const percentage = (this.totalScore / maxScore) * 100;

            interpretation += `年龄组：${ageGroup}\n\n`;

            if (percentage >= 80) {
              interpretation += '🎉 评测结果显示孩子的社会生活能力发展优秀，各项技能掌握良好。';
            } else if (percentage >= 60) {
              interpretation += '👍 评测结果显示孩子的社会生活能力发展良好，大部分技能已掌握。';
            } else if (percentage >= 40) {
              interpretation += '⚠️ 评测结果显示孩子的社会生活能力发展需要加强，建议针对性训练。';
            } else {
              interpretation += '⚠️ 评测结果显示孩子的社会生活能力发展明显滞后，建议咨询专业的发育评估师。';
            }
          } else if (this.currentScale === 'abc' || this.currentScale === 'asrs-autism') {
            // 自闭症相关量表解释
            const maxScore = this.currentScaleData.questions.length * (this.currentScaleData.optionLabels.length - 1);
            const percentage = (this.totalScore / maxScore) * 100;
            if (percentage >= 70) {
              interpretation += '⚠️ 评测结果显示存在明显的自闭症谱系障碍相关症状。强烈建议咨询专业的发育行为儿科医生或自闭症专家。';
            } else if (percentage >= 50) {
              interpretation += '⚠️ 评测结果显示存在一定的自闭症谱系障碍相关症状。建议进行专业评估。';
            } else if (percentage >= 30) {
              interpretation += '评测结果显示存在轻度的相关症状。建议关注孩子的发育情况。';
            } else {
              interpretation += '✅ 评测结果显示相关症状在正常范围内。';
            }
          } else {
            interpretation += '评测已完成，请参考分数结果。';
          }
          break;
      }

      interpretation += '\n\n⚠️ 注意：本量表仅供参考，不能替代专业医学诊断。如有疑虑，请咨询专业医疗机构。';

      this.interpretationText = interpretation;
    },

    // 计算CBCL内化问题分数
    calculateInternalizingScore() {
      if (this.currentScale !== 'cbcl' || !this.currentScaleData.internalizing) {
        return 0;
      }

      let score = 0;
      this.currentScaleData.internalizing.forEach((question, index) => {
        const answer = this.answers[index];
        if (answer !== undefined && answer !== null) {
          score += parseInt(answer);
        }
      });
      return score;
    },

    // 计算CBCL外化问题分数
    calculateExternalizingScore() {
      if (this.currentScale !== 'cbcl' || !this.currentScaleData.externalizing) {
        return 0;
      }

      let score = 0;
      const startIndex = this.currentScaleData.internalizing ? this.currentScaleData.internalizing.length : 0;
      this.currentScaleData.externalizing.forEach((question, index) => {
        const answer = this.answers[startIndex + index];
        if (answer !== undefined && answer !== null) {
          score += parseInt(answer);
        }
      });
      return score;
    },

    // 获取选择的年龄组（用于社会生活能力量表）
    getSelectedAgeGroup() {
      if (this.currentScale !== 'social-living-skills') {
        return '';
      }

      // 这里可以根据用户选择或其他逻辑来确定年龄组
      // 暂时返回默认值，实际使用时需要根据具体需求调整
      return '6-8岁';
    },

    // 获取当前年龄组的问题列表
    getCurrentAgeGroupQuestions() {
      if (this.currentScale !== 'social-living-skills' || !this.currentScaleData.ageGroups) {
        return [];
      }

      const ageGroup = this.getSelectedAgeGroup();
      return this.currentScaleData.ageGroups[ageGroup] || [];
    },

    // 选择年龄组
    selectAgeGroup(ageGroup) {
      this.selectedAgeGroup = ageGroup;
      // 重新初始化答案数组
      this.initializeAnswers();
    },

    // 从AI结果生成专业建议
    generateProfessionalAdviceFromAI(result) {
      const advice = [];

      // 添加临床评估
      advice.push('【临床评估】');
      advice.push(`严重程度：${result.severityLevel || '待评估'}`);
      advice.push(`风险等级：${result.riskLevel || '中等风险'}`);
      advice.push(`总分：${result.totalScore || this.totalScore}分`);

      if (this.attentionScore || this.hyperactivityScore) {
        advice.push(`注意力缺陷分数：${this.attentionScore}分`);
        advice.push(`多动/冲动分数：${this.hyperactivityScore}分`);
      }

      advice.push('');

      // 添加关键发现
      if (result.keyFindings && result.keyFindings.length > 0) {
        advice.push('【关键发现】');
        result.keyFindings.forEach((finding, index) => {
          advice.push(`${index + 1}. ${finding}`);
        });
        advice.push('');
      }

      // 添加专业建议
      if (result.recommendations && result.recommendations.length > 0) {
        advice.push('【专业建议】');
        result.recommendations.forEach((rec, index) => {
          advice.push(`${index + 1}. ${rec}`);
        });
        advice.push('');
      }

      // 添加警示信号
      if (result.warningSignals && result.warningSignals.length > 0) {
        advice.push('【需要关注的症状】');
        result.warningSignals.forEach((signal, index) => {
          advice.push(`${index + 1}. ${signal}`);
        });
        advice.push('');
      }

      // 添加后续步骤
      if (result.nextSteps && result.nextSteps.length > 0) {
        advice.push('【后续建议】');
        result.nextSteps.forEach((step, index) => {
          advice.push(`${index + 1}. ${step}`);
        });
        advice.push('');
      }

      // 添加置信度和免责声明
      if (result.confidence) {
        advice.push(`【分析置信度】${Math.round(result.confidence * 100)}%`);
      }

      if (result.disclaimer) {
        advice.push('');
        advice.push('【重要提醒】');
        advice.push(result.disclaimer);
      }

      return advice.join('\n');
    },

    // 生成传统专业建议
    generateTraditionalProfessionalAdvice() {
      const advice = [];

      advice.push('【传统评估建议】');
      advice.push(`总分：${this.totalScore}分`);
      advice.push(`严重程度：${this.severityLevel || this.getSeverityFromScore()}`);
      advice.push(`风险等级：${this.riskLevel}`);

      if (this.attentionScore || this.hyperactivityScore) {
        advice.push(`注意力缺陷分数：${this.attentionScore}分`);
        advice.push(`多动/冲动分数：${this.hyperactivityScore}分`);
      }

      advice.push('');
      advice.push('【专业建议】');

      const severity = this.severityLevel || this.getSeverityFromScore();
      switch (severity) {
        case '重度':
          advice.push('1. 建议立即寻求专业医疗评估');
          advice.push('2. 考虑药物治疗和行为干预');
          advice.push('3. 建立家校联合管理计划');
          advice.push('4. 定期复查和疗效评估');
          break;
        case '中度':
          advice.push('1. 建议专业医疗咨询评估');
          advice.push('2. 优先考虑行为干预训练');
          advice.push('3. 加强环境支持和调整');
          advice.push('4. 监测症状变化趋势');
          break;
        case '轻度':
          advice.push('1. 建议心理咨询和指导');
          advice.push('2. 实施行为管理策略');
          advice.push('3. 优化学习和生活环境');
          advice.push('4. 定期观察和评估');
          break;
        default:
          advice.push('1. 维持良好的生活习惯');
          advice.push('2. 继续关注心理健康');
          advice.push('3. 定期进行健康筛查');
          advice.push('4. 如有疑虑及时咨询专业人士');
      }

      advice.push('');
      advice.push('【重要提醒】');
      advice.push('本评估结果仅供参考，不能替代专业医学诊断。如有疑虑，请咨询专业医疗机构。');

      return advice.join('\n');
    },

    // 根据分数获取严重程度（传统方法备用）
    getSeverityFromScore() {
      const scaleType = this.currentScaleData.type;
      const totalScore = this.totalScore;

      switch (scaleType) {
        case 'depression':
          if (totalScore >= 15) return '重度';
          if (totalScore >= 10) return '中度';
          if (totalScore >= 5) return '轻度';
          return '正常';

        case 'anxiety':
          if (totalScore >= 15) return '重度';
          if (totalScore >= 10) return '中度';
          if (totalScore >= 5) return '轻度';
          return '正常';

        case 'adhd':
          if (this.attentionScore >= 14 || this.hyperactivityScore >= 14) return '重度';
          if (this.attentionScore >= 10 || this.hyperactivityScore >= 10) return '中度';
          if (this.attentionScore >= 6 || this.hyperactivityScore >= 6) return '轻度';
          return '正常';

        case 'sleep':
          if (totalScore >= 15) return '重度';
          if (totalScore >= 10) return '中度';
          if (totalScore >= 5) return '轻度';
          return '正常';

        case 'attention':
          // 儿童注意力水平测评量表（分数越高越好）
          if (totalScore >= 35) return '优秀';
          if (totalScore >= 25) return '良好';
          return '需要提高';

        case 'sensory':
          // 感觉统合能力发展评定量表（分数越低越有问题）
          if (totalScore < 30) return '严重失调';
          if (totalScore < 40) return '轻度失调';
          return '正常';

        case 'behavior':
          // 行为问题评估（如CBCL）
          if (this.currentScale === 'cbcl') {
            const internalizingScore = this.calculateInternalizingScore();
            const externalizingScore = this.calculateExternalizingScore();
            if (internalizingScore >= 20 || externalizingScore >= 20) return '重度';
            if (internalizingScore >= 15 || externalizingScore >= 15) return '中度';
            if (internalizingScore >= 10 || externalizingScore >= 10) return '轻度';
            return '正常';
          }
          // 其他行为量表的通用评估
          if (totalScore >= 15) return '重度';
          if (totalScore >= 10) return '中度';
          if (totalScore >= 5) return '轻度';
          return '正常';

        case 'development':
          // 发展能力评估（如社会生活能力量表）
          if (this.currentScale === 'social-living-skills') {
            const maxScore = this.getCurrentAgeGroupQuestions().length * (this.currentScaleData.optionLabels.length - 1);
            const percentage = maxScore > 0 ? (totalScore / maxScore) * 100 : 0;
            if (percentage >= 80) return '优秀';
            if (percentage >= 60) return '良好';
            if (percentage >= 40) return '需要加强';
            return '明显滞后';
          }
          // 其他发展量表的通用评估
          if (totalScore >= 15) return '优秀';
          if (totalScore >= 10) return '良好';
          if (totalScore >= 5) return '一般';
          return '需要关注';

        default: {
          // 处理SDS和SAS的特殊评估
          if (this.currentScale === 'sds') {
            // SDS抑郁自评量表
            if (totalScore >= 72) return '重度';
            if (totalScore >= 63) return '中度';
            if (totalScore >= 53) return '轻度';
            return '正常';
          } else if (this.currentScale === 'sas') {
            // SAS焦虑自评量表
            if (totalScore >= 69) return '重度';
            if (totalScore >= 60) return '中度';
            if (totalScore >= 50) return '轻度';
            return '正常';
          } else if (this.currentScale === 'madrs') {
            // MADRS蒙哥马利抑郁评定量表
            if (totalScore >= 35) return '严重';
            if (totalScore >= 20) return '中度';
            if (totalScore >= 7) return '轻度';
            return '正常';
          } else if (this.currentScale === 'cries-13') {
            // CRIES-13创伤事件量表
            if (totalScore >= 30) return '明显症状';
            if (totalScore >= 17) return '可能症状';
            return '正常';
          } else if (this.currentScale === 'aces') {
            // ACEs童年不良经历量表
            if (totalScore >= 4) return '高风险';
            if (totalScore >= 2) return '中等风险';
            if (totalScore >= 1) return '低风险';
            return '正常';
          } else if (this.currentScale === 'social-anxiety') {
            // 儿童社交焦虑量表
            const maxScore = this.currentScaleData.questions.length * (this.currentScaleData.optionLabels.length - 1);
            const percentage = maxScore > 0 ? (totalScore / maxScore) * 100 : 0;
            if (percentage >= 70) return '严重焦虑';
            if (percentage >= 50) return '中度焦虑';
            if (percentage >= 30) return '轻度焦虑';
            return '正常';
          } else {
            // 通用百分比评估（包括SPAI-C、ABC、ASRS-autism等）
            const maxScore = this.maxTotalScore;
            const percentage = maxScore > 0 ? (totalScore / maxScore) * 100 : 0;
            if (percentage >= 70) return '重度';
            if (percentage >= 50) return '中度';
            if (percentage >= 30) return '轻度';
            return '正常';
          }
        }
      }
    },



    // 🔥 为医生端生成专业分析数据（患者端不显示）
    generateDoctorAnalysisData() {
      // 使用传统分析结果
      const analysisMethod = 'traditional';
      const aiConfidence = null;
      const severityLevel = this.getSeverityFromScore();
      const modelUsed = null;
      const professionalAdvice = this.generateTraditionalDoctorAdvice();
      // 🔥 生成每道题的详细得分信息
      const questionScores = this.generateQuestionScoresForDoctor();
      console.log('🔍 生成的题目得分数据:', questionScores);

      const analysisDetails = JSON.stringify({
        analysisType: 'traditional',
        totalScore: this.totalScore,
        attentionScore: this.attentionScore,
        hyperactivityScore: this.hyperactivityScore,
        riskLevel: this.riskLevel,
        scaleType: this.currentScaleData.type,
        userDetailInfo: this.userDetailInfo || null,

        // 🔥 添加每道题的详细得分信息
        questionScores: questionScores,

        // 维度得分详情
        dimensionScores: {
          totalScore: this.totalScore,
          maxTotalScore: this.maxTotalScore,
          totalScorePercentage: this.totalScorePercentage,
          attentionScore: this.attentionScore,
          maxAttentionScore: this.maxAttentionScore,
          attentionScorePercentage: this.attentionScorePercentage,
          hyperactivityScore: this.hyperactivityScore,
          maxHyperactivityScore: this.maxHyperactivityScore,
          hyperactivityScorePercentage: this.hyperactivityScorePercentage,
          oppositionScore: this.oppositionScore,
          maxOppositionScore: this.maxOppositionScore,
          oppositionScorePercentage: this.oppositionScorePercentage
        }
      });

      console.log('📤 发送给后端的分析详情:', analysisDetails);

      console.log('🩺 医生端分析数据生成完成:', {
        analysisMethod,
        severityLevel,
        professionalAdviceLength: professionalAdvice ? professionalAdvice.length : 0,
        aiConfidence,
        analysisDetailsLength: analysisDetails ? analysisDetails.length : 0,
        hasAIResult: !!this.aiAnalysisResult
      });

      return {
        analysisMethod,
        aiConfidence,
        severityLevel,
        professionalAdvice,
        analysisDetails,
        modelUsed
      };
    },

    // 生成传统医生建议
    generateTraditionalDoctorAdvice() {
      const advice = [];
      const totalScore = this.totalScore || 0;
      const maxScore = this.maxTotalScore || 54;
      const percentage = maxScore > 0 ? Math.round((totalScore / maxScore) * 100) : 0;
      
      advice.push('【传统评估建议】');
      advice.push(`总分：${totalScore}/${maxScore}分 (${percentage}%)`);
      advice.push(`严重程度：${this.getSeverityFromScore()}`);
      advice.push(`风险等级：${this.riskLevel || '中等风险'}`);
      advice.push('');
      
      if (percentage >= 70) {
        advice.push('【高风险建议】');
        advice.push('• 建议立即安排专业心理评估');
        advice.push('• 考虑转介到专科医院进行详细诊断');
        advice.push('• 制定个性化治疗计划');
        advice.push('• 定期随访监测病情变化');
      } else if (percentage >= 50) {
        advice.push('【中等风险建议】');
        advice.push('• 建议预约专业心理咨询');
        advice.push('• 进行深入的临床评估');
        advice.push('• 考虑心理干预措施');
        advice.push('• 关注日常行为变化');
      } else if (percentage >= 30) {
        advice.push('【低风险建议】');
        advice.push('• 建议定期关注情绪状态');
        advice.push('• 可考虑心理健康教育');
        advice.push('• 必要时寻求专业指导');
      } else {
        advice.push('【正常范围建议】');
        advice.push('• 当前状态良好');
        advice.push('• 建议保持健康的生活方式');
        advice.push('• 定期进行心理健康检查');
      }
      
      advice.push('');
      advice.push('【重要提醒】');
      advice.push('本评估结果仅供参考，不能替代专业医学诊断。');
      
      return advice.join('\n');
    },

    // 🔥 为医生端生成每道题的详细得分信息
    generateQuestionScoresForDoctor() {
      const questionScores = [];

      if (this.currentScaleData.questions) {
        // 普通量表处理
        this.currentScaleData.questions.forEach((question, index) => {
          questionScores.push({
            questionNumber: index + 1,
            questionText: question,
            score: this.answers[index] || 0,
            maxScore: 3, // 假设最高分为3
            symptomDimension: this.getSymptomDimension(index),
            clinicalSignificance: this.getClinicalSignificance(index, this.answers[index] || 0)
          });
        });
      } else if (this.currentScaleData.attentionQuestions && this.currentScaleData.hyperactivityQuestions) {
        // ADHD量表特殊处理
        let questionIndex = 0;

        // 注意力缺陷题目
        this.currentScaleData.attentionQuestions.forEach((question) => {
          questionScores.push({
            questionNumber: questionIndex + 1,
            questionText: question.text || question,
            score: this.answers[questionIndex] || 0,
            maxScore: 3,
            symptomDimension: '注意力缺陷',
            clinicalSignificance: this.getClinicalSignificance(questionIndex, this.answers[questionIndex] || 0)
          });
          questionIndex++;
        });

        // 多动/冲动题目
        this.currentScaleData.hyperactivityQuestions.forEach((question) => {
          questionScores.push({
            questionNumber: questionIndex + 1,
            questionText: question.text || question,
            score: this.answers[questionIndex] || 0,
            maxScore: 3,
            symptomDimension: '多动/冲动',
            clinicalSignificance: this.getClinicalSignificance(questionIndex, this.answers[questionIndex] || 0)
          });
          questionIndex++;
        });

        // 对立违抗题目（如果有）
        if (this.currentScaleData.oppositionQuestions) {
          this.currentScaleData.oppositionQuestions.forEach((question) => {
            questionScores.push({
              questionNumber: questionIndex + 1,
              questionText: question.text || question,
              score: this.answers[questionIndex] || 0,
              maxScore: 3,
              symptomDimension: '对立违抗',
              clinicalSignificance: this.getClinicalSignificance(questionIndex, this.answers[questionIndex] || 0)
            });
            questionIndex++;
          });
        }
      }

      return questionScores;
    },

    // 获取所有问题（统一格式）
    getAllQuestions() {
      const questions = [];
      
      if (this.currentScaleData?.questions) {
        // 通用量表格式
        questions.push(...this.currentScaleData.questions.map((q, index) => ({
          text: q,
          index: index,
          category: 'general',
          maxScore: 3
        })));
      } else if (this.currentScaleData?.attentionQuestions) {
        // ADHD量表格式
        questions.push(
          ...this.currentScaleData.attentionQuestions.map((q, index) => ({
            text: q.text || q,
            index: index,
            category: 'attention',
            maxScore: 3
          })),
          ...this.currentScaleData.hyperactivityQuestions.map((q, index) => ({
            text: q.text || q,
            index: index + this.currentScaleData.attentionQuestions.length,
            category: 'hyperactivity',
            maxScore: 3
          }))
        );
        
        if (this.currentScaleData.oppositionQuestions) {
          questions.push(...this.currentScaleData.oppositionQuestions.map((q, index) => ({
            text: q.text || q,
            index: index + this.currentScaleData.attentionQuestions.length + this.currentScaleData.hyperactivityQuestions.length,
            category: 'opposition',
            maxScore: 3
          })));
        }
      }
      
      return questions;
    },

    // 获取症状维度
    getSymptomDimension(questionIndex) {
      const scaleType = this.currentScaleData.type;

      if (scaleType === 'adhd') {
        const attentionCount = this.currentScaleData.attentionQuestions?.length || 0;
        const hyperactivityCount = this.currentScaleData.hyperactivityQuestions?.length || 0;

        if (questionIndex < attentionCount) {
          return '注意力缺陷';
        } else if (questionIndex < attentionCount + hyperactivityCount) {
          return '多动/冲动';
        } else {
          return '对立违抗';
        }
      } else if (scaleType === 'depression') {
        return '抑郁症状';
      } else if (scaleType === 'anxiety') {
        return '焦虑症状';
      } else {
        return '心理健康';
      }
    },

    // 获取临床意义
    getClinicalSignificance(questionIndex, score) {
      // 移除未使用的参数 dimension
      // questionIndex 参数保留以备将来可能的扩展使用
      if (score === 0) {
        return '无明显症状表现';
      } else if (score === 1) {
        return '轻度症状，偶尔出现';
      } else if (score === 2) {
        return '中度症状，经常出现，需要关注';
      } else if (score === 3) {
        return '重度症状，频繁出现，需要重点关注和干预';
      }

      return '症状程度待评估';
    },

    // 获取风险等级样式类
    getRiskLevelClass(riskLevel) {
      if (riskLevel.includes('高风险') || riskLevel.includes('重度')) {
        return 'risk-high';
      } else if (riskLevel.includes('中等风险') || riskLevel.includes('中度')) {
        return 'risk-medium';
      } else if (riskLevel.includes('低风险') || riskLevel.includes('轻度')) {
        return 'risk-low';
      } else {
        return 'risk-normal';
      }
    },

    // 获取风险描述
    getRiskDescription(riskLevel) {
      if (riskLevel.includes('高风险') || riskLevel.includes('重度')) {
        return '建议尽快咨询专业医生进行评估';
      } else if (riskLevel.includes('中等风险') || riskLevel.includes('中度')) {
        return '建议关注相关症状，必要时寻求专业帮助';
      } else if (riskLevel.includes('低风险') || riskLevel.includes('轻度')) {
        return '症状较轻，建议保持观察';
      } else {
        return '继续保持良好状态';
      }
    },

    // 格式化完成时间
    formatCompletionTime() {
      return new Date().toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    // 获取总题目数量
    getTotalQuestionCount() {
      if (this.currentScaleData.questions) {
        return this.currentScaleData.questions.length;
      } else if (this.currentScaleData.attentionQuestions && this.currentScaleData.hyperactivityQuestions) {
        let count = this.currentScaleData.attentionQuestions.length + this.currentScaleData.hyperactivityQuestions.length;
        if (this.currentScaleData.oppositionQuestions) {
          count += this.currentScaleData.oppositionQuestions.length;
        }
        return count;
      }
      return 0;
    },

    // 获取填写者信息
    getFillerInfo() {
      const scaleTitle = this.currentScaleData.title;
      if (scaleTitle && (scaleTitle.includes('6-12岁') || scaleTitle.includes('6-18岁'))) {
        return '家长代填';
      } else {
        return '本人填写';
      }
    },

    // 🔥 生成详细的答案分析（医生专用）
    generateDetailedAnswerAnalysis() {
      const analysis = {
        attentionQuestions: [],
        hyperactivityQuestions: [],
        oppositionQuestions: [],
        overallPattern: ''
      };

      // 分析注意力缺陷题目
      if (this.currentScaleData.attentionQuestions && this.attentionAnswers) {
        this.currentScaleData.attentionQuestions.forEach((question, index) => {
          const answer = this.attentionAnswers[index];
          const score = answer || 0;
          analysis.attentionQuestions.push({
            questionNumber: index + 1,
            questionText: question,
            userAnswer: this.getAnswerText(score),
            score: score,
            riskLevel: this.getQuestionRiskLevel(score),
            clinicalSignificance: this.getQuestionClinicalSignificance('attention', index, score),
            contributionToTotal: ((score / this.maxAttentionScore) * 100).toFixed(1) + '%'
          });
        });
      }

      // 分析多动/冲动题目
      if (this.currentScaleData.hyperactivityQuestions && this.hyperactivityAnswers) {
        this.currentScaleData.hyperactivityQuestions.forEach((question, index) => {
          const answer = this.hyperactivityAnswers[index];
          const score = answer || 0;
          analysis.hyperactivityQuestions.push({
            questionNumber: this.currentScaleData.attentionQuestions.length + index + 1,
            questionText: question,
            userAnswer: this.getAnswerText(score),
            score: score,
            riskLevel: this.getQuestionRiskLevel(score),
            clinicalSignificance: this.getQuestionClinicalSignificance('hyperactivity', index, score),
            contributionToTotal: ((score / this.maxHyperactivityScore) * 100).toFixed(1) + '%'
          });
        });
      }

      // 分析对立违抗题目（如果有）
      if (this.currentScaleData.oppositionQuestions && this.oppositionAnswers) {
        this.currentScaleData.oppositionQuestions.forEach((question, index) => {
          const answer = this.oppositionAnswers[index];
          const score = answer || 0;
          analysis.oppositionQuestions.push({
            questionNumber: (this.currentScaleData.attentionQuestions?.length || 0) +
                           (this.currentScaleData.hyperactivityQuestions?.length || 0) + index + 1,
            questionText: question,
            userAnswer: this.getAnswerText(score),
            score: score,
            riskLevel: this.getQuestionRiskLevel(score),
            clinicalSignificance: this.getQuestionClinicalSignificance('opposition', index, score),
            contributionToTotal: ((score / this.maxOppositionScore) * 100).toFixed(1) + '%'
          });
        });
      }

      // 整体模式分析
      analysis.overallPattern = this.generateOverallPattern();

      return analysis;
    },

    // 🔥 生成家庭背景与症状关联分析
    generateFamilySymptomCorrelation() {
      if (!this.userDetailInfo) {
        return {
          hasData: false,
          message: '未收集到家庭背景信息'
        };
      }

      const correlation = {
        hasData: true,
        familyStructureImpact: '',
        medicalHistoryCorrelation: '',
        behavioralPatternAnalysis: '',
        academicPerformanceCorrelation: '',
        environmentalFactors: '',
        riskFactorAnalysis: '',
        protectiveFactorAnalysis: ''
      };

      // 家庭结构影响分析
      if (this.userDetailInfo.familyStructure) {
        correlation.familyStructureImpact = this.analyzeFamilyStructureImpact(
          this.userDetailInfo.familyStructure,
          this.totalScore,
          this.attentionScore,
          this.hyperactivityScore
        );
      }

      // 医疗史关联分析
      if (this.userDetailInfo.medicalHistory) {
        correlation.medicalHistoryCorrelation = this.analyzeMedicalHistoryCorrelation(
          this.userDetailInfo.medicalHistory,
          this.totalScore
        );
      }

      // 行为模式分析
      if (this.userDetailInfo.behaviorPatterns) {
        correlation.behavioralPatternAnalysis = this.analyzeBehaviorPatterns(
          this.userDetailInfo.behaviorPatterns,
          this.hyperactivityScore,
          this.oppositionScore
        );
      }

      // 学业表现关联
      if (this.userDetailInfo.academicPerformance) {
        correlation.academicPerformanceCorrelation = this.analyzeAcademicCorrelation(
          this.userDetailInfo.academicPerformance,
          this.attentionScore
        );
      }

      return correlation;
    },

    // 🔥 生成风险评估详情
    generateRiskAssessmentDetails() {
      const riskDetails = {
        overallRiskLevel: this.getRiskLevel(this.totalScorePercentage),
        riskFactors: [],
        protectiveFactors: [],
        immediateRisks: [],
        longTermRisks: [],
        interventionPriority: '',
        monitoringRecommendations: []
      };

      // 基于分数的风险因素
      if (this.attentionScore > this.maxAttentionScore * 0.7) {
        riskDetails.riskFactors.push({
          factor: '注意力缺陷严重',
          score: this.attentionScore,
          maxScore: this.maxAttentionScore,
          percentage: this.attentionScorePercentage,
          clinicalImplication: '可能影响学习和日常功能',
          supportingQuestions: this.getHighScoreQuestions('attention')
        });
      }

      if (this.hyperactivityScore > this.maxHyperactivityScore * 0.7) {
        riskDetails.riskFactors.push({
          factor: '多动/冲动行为突出',
          score: this.hyperactivityScore,
          maxScore: this.maxHyperactivityScore,
          percentage: this.hyperactivityScorePercentage,
          clinicalImplication: '可能影响社交和行为控制',
          supportingQuestions: this.getHighScoreQuestions('hyperactivity')
        });
      }

      // 基于家庭背景的风险评估
      if (this.userDetailInfo) {
        const familyRiskFactors = this.assessFamilyRiskFactors(this.userDetailInfo);
        const familyProtectiveFactors = this.assessFamilyProtectiveFactors(this.userDetailInfo);
        riskDetails.riskFactors.push(...familyRiskFactors);
        riskDetails.protectiveFactors.push(...familyProtectiveFactors);
      }

      // 干预优先级
      riskDetails.interventionPriority = this.determineInterventionPriority();

      return riskDetails;
    },

    // 辅助方法：获取答案文本
    getAnswerText(score) {
      const labels = ['从不', '偶尔', '经常', '总是'];
      return labels[score] || '未知';
    },

    // 辅助方法：获取题目风险等级
    getQuestionRiskLevel(score) {
      if (score >= 3) return '高风险';
      if (score >= 2) return '中风险';
      if (score >= 1) return '低风险';
      return '无风险';
    },

    // 辅助方法：获取题目临床意义
    getQuestionClinicalSignificance(category, index, score) {
      const significanceMap = {
        attention: [
          '注意力持续时间', '任务完成能力', '细节关注度', '组织能力',
          '指令遵循', '任务切换', '专注力维持', '分心程度', '记忆力'
        ],
        hyperactivity: [
          '活动水平', '冲动控制', '等待能力', '社交行为',
          '情绪调节', '行为抑制', '自我控制', '规则遵守', '冲动决策'
        ],
        opposition: [
          '权威态度', '规则遵守', '合作程度', '情绪爆发',
          '对抗行为', '愤怒管理', '社交冲突', '挫折耐受', '行为问题'
        ]
      };

      const significance = significanceMap[category]?.[index] || '行为表现';
      const severity = score >= 3 ? '严重影响' : score >= 2 ? '中度影响' : score >= 1 ? '轻度影响' : '无明显影响';

      return `${significance}：${severity}`;
    },

    // 辅助方法：生成整体模式分析
    generateOverallPattern() {
      const patterns = [];

      if (this.attentionScorePercentage > 70) {
        patterns.push('注意力缺陷模式突出，可能影响学习和工作效率');
      }

      if (this.hyperactivityScorePercentage > 70) {
        patterns.push('多动/冲动模式明显，可能影响社交和行为控制');
      }

      if (this.oppositionScorePercentage > 70) {
        patterns.push('对立违抗倾向显著，可能影响权威关系和规则遵守');
      }

      if (patterns.length === 0) {
        return '各维度表现相对正常，无明显异常模式';
      }

      return patterns.join('；');
    },

    // 辅助方法：分析家庭结构影响
    analyzeFamilyStructureImpact(familyStructure, totalScore, attentionScore, hyperactivityScore) {
      let analysis = `家庭结构：${familyStructure}。`;

      if (familyStructure.includes('单亲') && totalScore > 30) {
        analysis += '单亲家庭环境可能增加了注意力和行为问题的风险，建议加强家庭支持系统。';
      } else if (familyStructure.includes('重组') && hyperactivityScore > 15) {
        analysis += '重组家庭的适应过程可能影响行为表现，需要关注家庭关系稳定性。';
      } else if (familyStructure.includes('完整') && totalScore < 20) {
        analysis += '完整家庭结构为良好的行为发展提供了稳定基础。';
      }

      return analysis;
    },

    // 辅助方法：分析医疗史关联
    analyzeMedicalHistoryCorrelation(medicalHistory, totalScore) {
      let analysis = `医疗史信息：${medicalHistory}。`;

      if (medicalHistory.includes('ADHD') && totalScore > 25) {
        analysis += '家族ADHD史与当前高分表现一致，提示遗传因素的重要作用。';
      } else if (medicalHistory.includes('抑郁') || medicalHistory.includes('焦虑')) {
        analysis += '家族精神健康史需要关注，可能存在共病风险。';
      } else if (medicalHistory.includes('无') && totalScore > 30) {
        analysis += '无家族史但症状明显，可能与环境因素或其他原因相关。';
      }

      return analysis;
    },

    // 辅助方法：评估家庭风险因素
    assessFamilyRiskFactors(userDetailInfo) {
      const riskFactors = [];

      if (userDetailInfo.familyStructure?.includes('单亲')) {
        riskFactors.push({
          factor: '单亲家庭结构',
          impact: '可能缺乏充分的监督和支持',
          recommendation: '建议寻求额外的家庭支持资源'
        });
      }

      if (userDetailInfo.medicalHistory?.includes('ADHD')) {
        riskFactors.push({
          factor: '家族ADHD史',
          impact: '遗传风险因素',
          recommendation: '需要密切监测和早期干预'
        });
      }

      return riskFactors;
    },

    // 辅助方法：评估家庭保护因素
    assessFamilyProtectiveFactors(userDetailInfo) {
      const protectiveFactors = [];

      if (userDetailInfo.familyStructure?.includes('完整')) {
        protectiveFactors.push({
          factor: '完整家庭结构',
          benefit: '提供稳定的支持环境',
          enhancement: '继续维持良好的家庭关系'
        });
      }

      return protectiveFactors;
    },

    // 辅助方法：确定干预优先级
    determineInterventionPriority() {
      if (this.totalScorePercentage > 80) {
        return '紧急 - 需要立即专业评估和干预';
      } else if (this.totalScorePercentage > 60) {
        return '高优先级 - 建议尽快寻求专业帮助';
      } else if (this.totalScorePercentage > 40) {
        return '中等优先级 - 建议定期监测和支持';
      } else {
        return '低优先级 - 继续观察和预防性措施';
      }
    },

    // 辅助方法：获取高分题目
    getHighScoreQuestions(category) {
      const questions = [];
      const threshold = 2; // 分数>=2的题目

      if (category === 'attention' && this.attentionAnswers) {
        this.attentionAnswers.forEach((score, index) => {
          if (score >= threshold) {
            questions.push({
              number: index + 1,
              score: score,
              text: this.currentScaleData.attentionQuestions?.[index] || ''
            });
          }
        });
      } else if (category === 'hyperactivity' && this.hyperactivityAnswers) {
        this.hyperactivityAnswers.forEach((score, index) => {
          if (score >= threshold) {
            questions.push({
              number: (this.currentScaleData.attentionQuestions?.length || 0) + index + 1,
              score: score,
              text: this.currentScaleData.hyperactivityQuestions?.[index] || ''
            });
          }
        });
      }

      return questions;
    },

    // 获取注意力维度临床意义
    getAttentionClinicalMeaning() {
      if (this.attentionScorePercentage > 80) {
        return '严重注意力缺陷，显著影响学习和日常功能';
      } else if (this.attentionScorePercentage > 60) {
        return '中度注意力问题，需要干预支持';
      } else if (this.attentionScorePercentage > 40) {
        return '轻度注意力困难，建议监测';
      } else {
        return '注意力表现正常';
      }
    },

    // 获取多动/冲动维度临床意义
    getHyperactivityClinicalMeaning() {
      if (this.hyperactivityScorePercentage > 80) {
        return '严重多动/冲动行为，显著影响社交和行为控制';
      } else if (this.hyperactivityScorePercentage > 60) {
        return '中度多动/冲动问题，需要行为干预';
      } else if (this.hyperactivityScorePercentage > 40) {
        return '轻度多动/冲动倾向，建议行为指导';
      } else {
        return '多动/冲动表现正常';
      }
    },

    // 获取对立违抗维度临床意义
    getOppositionClinicalMeaning() {
      if (this.oppositionScorePercentage > 80) {
        return '严重对立违抗行为，显著影响权威关系';
      } else if (this.oppositionScorePercentage > 60) {
        return '中度对立违抗问题，需要行为管理';
      } else if (this.oppositionScorePercentage > 40) {
        return '轻度对立违抗倾向，建议规则训练';
      } else {
        return '对立违抗表现正常';
      }
    },

    // 获取医生端高分题目详细信息
    getHighScoreQuestionsForDoctor() {
      const questions = [];
      const threshold = 2;

      // 注意力题目
      if (this.attentionAnswers) {
        this.attentionAnswers.forEach((score, index) => {
          if (score >= threshold) {
            questions.push({
              number: index + 1,
              score: score,
              text: this.currentScaleData.attentionQuestions?.[index] || '',
              category: '注意力缺陷',
              clinicalSignificance: this.getQuestionClinicalSignificance('attention', index, score),
              riskLevel: this.getQuestionRiskLevel(score),
              interventionSuggestion: this.getInterventionSuggestion('attention', score)
            });
          }
        });
      }

      // 多动/冲动题目
      if (this.hyperactivityAnswers) {
        this.hyperactivityAnswers.forEach((score, index) => {
          if (score >= threshold) {
            questions.push({
              number: (this.currentScaleData.attentionQuestions?.length || 0) + index + 1,
              score: score,
              text: this.currentScaleData.hyperactivityQuestions?.[index] || '',
              category: '多动/冲动',
              clinicalSignificance: this.getQuestionClinicalSignificance('hyperactivity', index, score),
              riskLevel: this.getQuestionRiskLevel(score),
              interventionSuggestion: this.getInterventionSuggestion('hyperactivity', score)
            });
          }
        });
      }

      // 对立违抗题目
      if (this.oppositionAnswers) {
        this.oppositionAnswers.forEach((score, index) => {
          if (score >= threshold) {
            questions.push({
              number: (this.currentScaleData.attentionQuestions?.length || 0) +
                     (this.currentScaleData.hyperactivityQuestions?.length || 0) + index + 1,
              score: score,
              text: this.currentScaleData.oppositionQuestions?.[index] || '',
              category: '对立违抗',
              clinicalSignificance: this.getQuestionClinicalSignificance('opposition', index, score),
              riskLevel: this.getQuestionRiskLevel(score),
              interventionSuggestion: this.getInterventionSuggestion('opposition', score)
            });
          }
        });
      }

      return questions.sort((a, b) => b.score - a.score); // 按分数降序排列
    },

    // 获取干预建议
    getInterventionSuggestion(category, score) {
      const suggestions = {
        attention: {
          3: '建议认知行为治疗，注意力训练，可能需要药物评估',
          2: '建议注意力训练，环境调整，定期监测',
          1: '建议行为指导，环境优化'
        },
        hyperactivity: {
          3: '建议行为治疗，冲动控制训练，可能需要药物评估',
          2: '建议行为管理，自控力训练，家庭支持',
          1: '建议行为指导，规则建立'
        },
        opposition: {
          3: '建议家庭治疗，行为管理，冲突解决训练',
          2: '建议亲子关系改善，规则训练，情绪管理',
          1: '建议沟通技巧训练，正面管教'
        }
      };

      return suggestions[category]?.[score] || '建议专业评估';
    },

    // 生成家庭症状分析文本
    generateFamilySymptomAnalysisText() {
      const analysis = [];

      if (this.userDetailInfo?.familyStructure) {
        analysis.push(this.analyzeFamilyStructureImpact(
          this.userDetailInfo.familyStructure,
          this.totalScore,
          this.attentionScore,
          this.hyperactivityScore
        ));
      }

      if (this.userDetailInfo?.medicalHistory) {
        analysis.push(this.analyzeMedicalHistoryCorrelation(
          this.userDetailInfo.medicalHistory,
          this.totalScore
        ));
      }

      return analysis.join(' ') || '家庭背景信息不足，建议补充收集';
    },

    // 建议优先级分类
    prioritizeRecommendations(recommendations) {
      const prioritized = {
        urgent: [],
        important: [],
        general: []
      };

      recommendations.forEach(rec => {
        const recText = typeof rec === 'string' ? rec : rec.recommendation || rec;

        if (recText.includes('紧急') || recText.includes('立即') || recText.includes('马上')) {
          prioritized.urgent.push(recText);
        } else if (recText.includes('重要') || recText.includes('尽快') || recText.includes('及时')) {
          prioritized.important.push(recText);
        } else {
          prioritized.general.push(recText);
        }
      });

      return prioritized;
    },

    // 🔥 生成详细临床表现分析（基于具体题目）
    generateDetailedClinicalAnalysis() {
      const analysis = [];

      // 注意力缺陷症状分析
      analysis.push('注意力缺陷症状表现：');
      if (this.attentionAnswers && this.currentScaleData.attentionQuestions) {
        const attentionSymptoms = this.analyzeAttentionSymptoms();
        if (attentionSymptoms.length > 0) {
          attentionSymptoms.forEach(symptom => {
            analysis.push(`  • ${symptom.symptom}（基于第${symptom.questions.join('、')}题，平均分：${symptom.averageScore}）`);
            analysis.push(`    临床意义：${symptom.clinicalMeaning}`);
            analysis.push(`    具体表现：${symptom.manifestations.join('；')}`);
          });
        } else {
          analysis.push('  • 注意力表现基本正常');
        }
      }
      analysis.push('');

      // 多动/冲动症状分析
      analysis.push('多动/冲动症状表现：');
      if (this.hyperactivityAnswers && this.currentScaleData.hyperactivityQuestions) {
        const hyperactivitySymptoms = this.analyzeHyperactivitySymptoms();
        if (hyperactivitySymptoms.length > 0) {
          hyperactivitySymptoms.forEach(symptom => {
            analysis.push(`  • ${symptom.symptom}（基于第${symptom.questions.join('、')}题，平均分：${symptom.averageScore}）`);
            analysis.push(`    临床意义：${symptom.clinicalMeaning}`);
            analysis.push(`    具体表现：${symptom.manifestations.join('；')}`);
          });
        } else {
          analysis.push('  • 多动/冲动表现基本正常');
        }
      }
      analysis.push('');

      // 对立违抗症状分析（如果有）
      if (this.oppositionAnswers && this.currentScaleData.oppositionQuestions) {
        analysis.push('对立违抗症状表现：');
        const oppositionSymptoms = this.analyzeOppositionSymptoms();
        if (oppositionSymptoms.length > 0) {
          oppositionSymptoms.forEach(symptom => {
            analysis.push(`  • ${symptom.symptom}（基于第${symptom.questions.join('、')}题，平均分：${symptom.averageScore}）`);
            analysis.push(`    临床意义：${symptom.clinicalMeaning}`);
            analysis.push(`    具体表现：${symptom.manifestations.join('；')}`);
          });
        } else {
          analysis.push('  • 对立违抗表现基本正常');
        }
        analysis.push('');
      }

      return analysis.join('\n');
    },

    // 🔥 分析注意力症状
    analyzeAttentionSymptoms() {
      const symptoms = [];
      if (!this.attentionAnswers || !this.currentScaleData.attentionQuestions) return symptoms;

      // 持续注意力问题（通常是前几题）
      const sustainedAttentionQuestions = [0, 1, 2]; // 假设前3题是持续注意力
      const sustainedAttentionScores = sustainedAttentionQuestions
        .filter(i => i < this.attentionAnswers.length)
        .map(i => this.attentionAnswers[i]);

      if (sustainedAttentionScores.length > 0) {
        const avgScore = sustainedAttentionScores.reduce((a, b) => a + b, 0) / sustainedAttentionScores.length;
        if (avgScore >= 2) {
          symptoms.push({
            symptom: '持续注意力缺陷',
            questions: sustainedAttentionQuestions.map(i => i + 1),
            averageScore: avgScore.toFixed(1),
            clinicalMeaning: avgScore >= 2.5 ? '严重持续注意力问题' : '中度持续注意力问题',
            manifestations: [
              '难以长时间专注于任务',
              '容易被外界干扰',
              '无法完成需要持续注意力的活动'
            ]
          });
        }
      }

      // 选择性注意力问题（中间题目）
      const selectiveAttentionQuestions = [3, 4, 5]; // 假设中间题目是选择性注意力
      const selectiveAttentionScores = selectiveAttentionQuestions
        .filter(i => i < this.attentionAnswers.length)
        .map(i => this.attentionAnswers[i]);

      if (selectiveAttentionScores.length > 0) {
        const avgScore = selectiveAttentionScores.reduce((a, b) => a + b, 0) / selectiveAttentionScores.length;
        if (avgScore >= 2) {
          symptoms.push({
            symptom: '选择性注意力缺陷',
            questions: selectiveAttentionQuestions.map(i => i + 1),
            averageScore: avgScore.toFixed(1),
            clinicalMeaning: avgScore >= 2.5 ? '严重选择性注意力问题' : '中度选择性注意力问题',
            manifestations: [
              '难以筛选重要信息',
              '容易注意到无关细节',
              '在嘈杂环境中难以集中注意力'
            ]
          });
        }
      }

      // 执行注意力问题（后面题目）
      const executiveAttentionQuestions = [6, 7, 8]; // 假设后面题目是执行注意力
      const executiveAttentionScores = executiveAttentionQuestions
        .filter(i => i < this.attentionAnswers.length)
        .map(i => this.attentionAnswers[i]);

      if (executiveAttentionScores.length > 0) {
        const avgScore = executiveAttentionScores.reduce((a, b) => a + b, 0) / executiveAttentionScores.length;
        if (avgScore >= 2) {
          symptoms.push({
            symptom: '执行注意力缺陷',
            questions: executiveAttentionQuestions.map(i => i + 1),
            averageScore: avgScore.toFixed(1),
            clinicalMeaning: avgScore >= 2.5 ? '严重执行注意力问题' : '中度执行注意力问题',
            manifestations: [
              '计划和组织能力差',
              '难以监控自己的行为',
              '任务切换困难'
            ]
          });
        }
      }

      return symptoms;
    },

    // 🔥 分析多动/冲动症状
    analyzeHyperactivitySymptoms() {
      const symptoms = [];
      if (!this.hyperactivityAnswers || !this.currentScaleData.hyperactivityQuestions) return symptoms;

      // 运动性多动（前几题）
      const motorHyperactivityQuestions = [0, 1, 2];
      const motorHyperactivityScores = motorHyperactivityQuestions
        .filter(i => i < this.hyperactivityAnswers.length)
        .map(i => this.hyperactivityAnswers[i]);

      if (motorHyperactivityScores.length > 0) {
        const avgScore = motorHyperactivityScores.reduce((a, b) => a + b, 0) / motorHyperactivityScores.length;
        if (avgScore >= 2) {
          symptoms.push({
            symptom: '运动性多动',
            questions: motorHyperactivityQuestions.map(i => (this.currentScaleData.attentionQuestions?.length || 0) + i + 1),
            averageScore: avgScore.toFixed(1),
            clinicalMeaning: avgScore >= 2.5 ? '严重运动性多动' : '中度运动性多动',
            manifestations: [
              '难以安静坐着',
              '手脚不停地动',
              '在不合适的场合跑来跑去'
            ]
          });
        }
      }

      // 言语冲动（中间题目）
      const verbalImpulsivityQuestions = [3, 4, 5];
      const verbalImpulsivityScores = verbalImpulsivityQuestions
        .filter(i => i < this.hyperactivityAnswers.length)
        .map(i => this.hyperactivityAnswers[i]);

      if (verbalImpulsivityScores.length > 0) {
        const avgScore = verbalImpulsivityScores.reduce((a, b) => a + b, 0) / verbalImpulsivityScores.length;
        if (avgScore >= 2) {
          symptoms.push({
            symptom: '言语冲动',
            questions: verbalImpulsivityQuestions.map(i => (this.currentScaleData.attentionQuestions?.length || 0) + i + 1),
            averageScore: avgScore.toFixed(1),
            clinicalMeaning: avgScore >= 2.5 ? '严重言语冲动' : '中度言语冲动',
            manifestations: [
              '打断别人说话',
              '抢答问题',
              '难以等待轮到自己'
            ]
          });
        }
      }

      // 行为冲动（后面题目）
      const behavioralImpulsivityQuestions = [6, 7, 8];
      const behavioralImpulsivityScores = behavioralImpulsivityQuestions
        .filter(i => i < this.hyperactivityAnswers.length)
        .map(i => this.hyperactivityAnswers[i]);

      if (behavioralImpulsivityScores.length > 0) {
        const avgScore = behavioralImpulsivityScores.reduce((a, b) => a + b, 0) / behavioralImpulsivityScores.length;
        if (avgScore >= 2) {
          symptoms.push({
            symptom: '行为冲动',
            questions: behavioralImpulsivityQuestions.map(i => (this.currentScaleData.attentionQuestions?.length || 0) + i + 1),
            averageScore: avgScore.toFixed(1),
            clinicalMeaning: avgScore >= 2.5 ? '严重行为冲动' : '中度行为冲动',
            manifestations: [
              '做事不考虑后果',
              '难以控制冲动行为',
              '容易发生意外或冲突'
            ]
          });
        }
      }

      return symptoms;
    },

    // 🔥 分析对立违抗症状
    analyzeOppositionSymptoms() {
      const symptoms = [];
      if (!this.oppositionAnswers || !this.currentScaleData.oppositionQuestions) return symptoms;

      // 权威对抗（前几题）
      const authorityOppositionQuestions = [0, 1, 2];
      const authorityOppositionScores = authorityOppositionQuestions
        .filter(i => i < this.oppositionAnswers.length)
        .map(i => this.oppositionAnswers[i]);

      if (authorityOppositionScores.length > 0) {
        const avgScore = authorityOppositionScores.reduce((a, b) => a + b, 0) / authorityOppositionScores.length;
        if (avgScore >= 2) {
          symptoms.push({
            symptom: '权威对抗行为',
            questions: authorityOppositionQuestions.map(i =>
              (this.currentScaleData.attentionQuestions?.length || 0) +
              (this.currentScaleData.hyperactivityQuestions?.length || 0) + i + 1
            ),
            averageScore: avgScore.toFixed(1),
            clinicalMeaning: avgScore >= 2.5 ? '严重权威对抗' : '中度权威对抗',
            manifestations: [
              '经常与成人争论',
              '拒绝遵守规则',
              '故意违抗权威指令'
            ]
          });
        }
      }

      return symptoms;
    },

    // 🔥 生成症状严重程度评估
    generateSeverityAssessment() {
      const assessment = [];

      // 总体严重程度
      assessment.push(`总体严重程度：${this.getOverallSeverity()}`);
      assessment.push(`评估依据：总分${this.totalScore}分，占比${this.totalScorePercentage}%`);
      assessment.push('');

      // 各维度严重程度详细分析
      if (this.attentionScore !== null) {
        assessment.push(`注意力缺陷严重程度：${this.getAttentionSeverity()}`);
        assessment.push(`  评估依据：注意力维度${this.attentionScore}分，占比${this.attentionScorePercentage}%`);
        assessment.push(`  关键题目：${this.getKeyAttentionQuestions()}`);
        assessment.push(`  临床表现：${this.getAttentionClinicalPresentation()}`);
        assessment.push('');
      }

      if (this.hyperactivityScore !== null) {
        assessment.push(`多动/冲动严重程度：${this.getHyperactivitySeverity()}`);
        assessment.push(`  评估依据：多动/冲动维度${this.hyperactivityScore}分，占比${this.hyperactivityScorePercentage}%`);
        assessment.push(`  关键题目：${this.getKeyHyperactivityQuestions()}`);
        assessment.push(`  临床表现：${this.getHyperactivityClinicalPresentation()}`);
        assessment.push('');
      }

      if (this.oppositionScore !== null) {
        assessment.push(`对立违抗严重程度：${this.getOppositionSeverity()}`);
        assessment.push(`  评估依据：对立违抗维度${this.oppositionScore}分，占比${this.oppositionScorePercentage}%`);
        assessment.push(`  关键题目：${this.getKeyOppositionQuestions()}`);
        assessment.push(`  临床表现：${this.getOppositionClinicalPresentation()}`);
        assessment.push('');
      }

      return assessment.join('\n');
    },

    // 🔥 生成共病风险评估
    generateComorbidityRiskAssessment() {
      const assessment = [];

      assessment.push('基于当前症状表现和家庭背景，评估以下共病风险：');
      assessment.push('');

      // 抑郁症风险
      const depressionRisk = this.assessDepressionRisk();
      assessment.push(`抑郁症风险：${depressionRisk.level}`);
      assessment.push(`  评估依据：${depressionRisk.evidence}`);
      assessment.push(`  支持题目：${depressionRisk.supportingQuestions}`);
      assessment.push('');

      // 焦虑症风险
      const anxietyRisk = this.assessAnxietyRisk();
      assessment.push(`焦虑症风险：${anxietyRisk.level}`);
      assessment.push(`  评估依据：${anxietyRisk.evidence}`);
      assessment.push(`  支持题目：${anxietyRisk.supportingQuestions}`);
      assessment.push('');

      // 学习障碍风险
      const learningDisorderRisk = this.assessLearningDisorderRisk();
      assessment.push(`学习障碍风险：${learningDisorderRisk.level}`);
      assessment.push(`  评估依据：${learningDisorderRisk.evidence}`);
      assessment.push(`  支持题目：${learningDisorderRisk.supportingQuestions}`);
      assessment.push('');

      // 品行障碍风险
      if (this.oppositionScore !== null) {
        const conductDisorderRisk = this.assessConductDisorderRisk();
        assessment.push(`品行障碍风险：${conductDisorderRisk.level}`);
        assessment.push(`  评估依据：${conductDisorderRisk.evidence}`);
        assessment.push(`  支持题目：${conductDisorderRisk.supportingQuestions}`);
        assessment.push('');
      }

      return assessment.join('\n');
    },

    // 🔥 生成功能损害评估
    generateFunctionalImpairmentAssessment() {
      const assessment = [];

      assessment.push('基于症状表现评估各领域功能损害程度：');
      assessment.push('');

      // 学业功能损害
      const academicImpairment = this.assessAcademicImpairment();
      assessment.push(`学业功能损害：${academicImpairment.level}`);
      assessment.push(`  主要表现：${academicImpairment.manifestations.join('；')}`);
      assessment.push(`  支持题目：${academicImpairment.supportingQuestions}`);
      assessment.push(`  干预建议：${academicImpairment.interventions.join('；')}`);
      assessment.push('');

      // 社交功能损害
      const socialImpairment = this.assessSocialImpairment();
      assessment.push(`社交功能损害：${socialImpairment.level}`);
      assessment.push(`  主要表现：${socialImpairment.manifestations.join('；')}`);
      assessment.push(`  支持题目：${socialImpairment.supportingQuestions}`);
      assessment.push(`  干预建议：${socialImpairment.interventions.join('；')}`);
      assessment.push('');

      // 家庭功能损害
      const familyImpairment = this.assessFamilyImpairment();
      assessment.push(`家庭功能损害：${familyImpairment.level}`);
      assessment.push(`  主要表现：${familyImpairment.manifestations.join('；')}`);
      assessment.push(`  支持题目：${familyImpairment.supportingQuestions}`);
      assessment.push(`  干预建议：${familyImpairment.interventions.join('；')}`);
      assessment.push('');

      return assessment.join('\n');
    },

    // 辅助方法：获取总体严重程度
    getOverallSeverity() {
      if (this.totalScorePercentage >= 80) return '极重度';
      if (this.totalScorePercentage >= 65) return '重度';
      if (this.totalScorePercentage >= 50) return '中度';
      if (this.totalScorePercentage >= 35) return '轻度';
      return '正常范围';
    },

    // 辅助方法：获取注意力严重程度
    getAttentionSeverity() {
      if (this.attentionScorePercentage >= 80) return '极重度注意力缺陷';
      if (this.attentionScorePercentage >= 65) return '重度注意力缺陷';
      if (this.attentionScorePercentage >= 50) return '中度注意力缺陷';
      if (this.attentionScorePercentage >= 35) return '轻度注意力缺陷';
      return '注意力正常';
    },

    // 辅助方法：获取多动/冲动严重程度
    getHyperactivitySeverity() {
      if (this.hyperactivityScorePercentage >= 80) return '极重度多动/冲动';
      if (this.hyperactivityScorePercentage >= 65) return '重度多动/冲动';
      if (this.hyperactivityScorePercentage >= 50) return '中度多动/冲动';
      if (this.hyperactivityScorePercentage >= 35) return '轻度多动/冲动';
      return '多动/冲动正常';
    },

    // 辅助方法：获取对立违抗严重程度
    getOppositionSeverity() {
      if (this.oppositionScorePercentage >= 80) return '极重度对立违抗';
      if (this.oppositionScorePercentage >= 65) return '重度对立违抗';
      if (this.oppositionScorePercentage >= 50) return '中度对立违抗';
      if (this.oppositionScorePercentage >= 35) return '轻度对立违抗';
      return '对立违抗正常';
    },

    // 辅助方法：获取关键注意力题目
    getKeyAttentionQuestions() {
      if (!this.attentionAnswers) return '无数据';
      const keyQuestions = [];
      this.attentionAnswers.forEach((score, index) => {
        if (score >= 2) {
          keyQuestions.push(`第${index + 1}题(${score}分)`);
        }
      });
      return keyQuestions.length > 0 ? keyQuestions.join('、') : '无高分题目';
    },

    // 辅助方法：获取关键多动/冲动题目
    getKeyHyperactivityQuestions() {
      if (!this.hyperactivityAnswers) return '无数据';
      const keyQuestions = [];
      const startNumber = (this.currentScaleData.attentionQuestions?.length || 0) + 1;
      this.hyperactivityAnswers.forEach((score, index) => {
        if (score >= 2) {
          keyQuestions.push(`第${startNumber + index}题(${score}分)`);
        }
      });
      return keyQuestions.length > 0 ? keyQuestions.join('、') : '无高分题目';
    },

    // 辅助方法：获取关键对立违抗题目
    getKeyOppositionQuestions() {
      if (!this.oppositionAnswers) return '无数据';
      const keyQuestions = [];
      const startNumber = (this.currentScaleData.attentionQuestions?.length || 0) +
                         (this.currentScaleData.hyperactivityQuestions?.length || 0) + 1;
      this.oppositionAnswers.forEach((score, index) => {
        if (score >= 2) {
          keyQuestions.push(`第${startNumber + index}题(${score}分)`);
        }
      });
      return keyQuestions.length > 0 ? keyQuestions.join('、') : '无高分题目';
    },

    // 辅助方法：获取注意力临床表现
    getAttentionClinicalPresentation() {
      const presentations = [];
      if (this.attentionScorePercentage >= 70) {
        presentations.push('持续注意力严重受损');
      }
      if (this.attentionScorePercentage >= 60) {
        presentations.push('任务完成困难');
      }
      if (this.attentionScorePercentage >= 50) {
        presentations.push('容易分心');
      }
      return presentations.length > 0 ? presentations.join('、') : '表现基本正常';
    },

    // 辅助方法：获取多动/冲动临床表现
    getHyperactivityClinicalPresentation() {
      const presentations = [];
      if (this.hyperactivityScorePercentage >= 70) {
        presentations.push('严重多动行为');
      }
      if (this.hyperactivityScorePercentage >= 60) {
        presentations.push('冲动控制困难');
      }
      if (this.hyperactivityScorePercentage >= 50) {
        presentations.push('坐立不安');
      }
      return presentations.length > 0 ? presentations.join('、') : '表现基本正常';
    },

    // 辅助方法：获取对立违抗临床表现
    getOppositionClinicalPresentation() {
      const presentations = [];
      if (this.oppositionScorePercentage >= 70) {
        presentations.push('严重对抗行为');
      }
      if (this.oppositionScorePercentage >= 60) {
        presentations.push('规则违抗');
      }
      if (this.oppositionScorePercentage >= 50) {
        presentations.push('权威挑战');
      }
      return presentations.length > 0 ? presentations.join('、') : '表现基本正常';
    },

    // 辅助方法：评估抑郁症风险
    assessDepressionRisk() {
      let riskLevel = '低风险';
      let evidence = [];
      let supportingQuestions = [];

      // 基于注意力问题评估抑郁风险
      if (this.attentionScorePercentage >= 70) {
        evidence.push('严重注意力问题可能导致学业困难和自信心下降');
        riskLevel = '中等风险';
      }

      // 基于家庭背景评估
      if (this.userDetailInfo?.medicalHistory?.includes('抑郁')) {
        evidence.push('家族抑郁史增加风险');
        riskLevel = '高风险';
      }

      // 基于功能损害评估
      if (this.totalScorePercentage >= 65) {
        evidence.push('严重功能损害可能引发情绪问题');
        supportingQuestions.push('总分过高提示多重困难');
      }

      return {
        level: riskLevel,
        evidence: evidence.join('；') || '当前症状表现未显示明显抑郁风险指标',
        supportingQuestions: supportingQuestions.join('；') || '基于整体评估'
      };
    },

    // 辅助方法：评估焦虑症风险
    assessAnxietyRisk() {
      let riskLevel = '低风险';
      let evidence = [];
      let supportingQuestions = [];

      // 基于多动/冲动评估焦虑风险
      if (this.hyperactivityScorePercentage >= 60) {
        evidence.push('冲动行为可能掩盖内在焦虑');
        riskLevel = '中等风险';
      }

      // 基于注意力问题评估
      if (this.attentionScorePercentage >= 65) {
        evidence.push('注意力问题可能与焦虑相关');
        supportingQuestions.push('注意力维度高分');
      }

      return {
        level: riskLevel,
        evidence: evidence.join('；') || '当前症状表现未显示明显焦虑风险指标',
        supportingQuestions: supportingQuestions.join('；') || '基于整体评估'
      };
    },

    // 辅助方法：评估学习障碍风险
    assessLearningDisorderRisk() {
      let riskLevel = '低风险';
      let evidence = [];
      let supportingQuestions = [];

      // 基于注意力问题评估学习障碍风险
      if (this.attentionScorePercentage >= 70) {
        evidence.push('严重注意力缺陷显著影响学习能力');
        riskLevel = '高风险';
        supportingQuestions.push('注意力维度得分过高');
      }

      // 基于家庭背景评估
      if (this.userDetailInfo?.academicPerformance?.includes('困难')) {
        evidence.push('已有学业表现困难');
        riskLevel = '高风险';
      }

      return {
        level: riskLevel,
        evidence: evidence.join('；') || '当前症状表现未显示明显学习障碍风险',
        supportingQuestions: supportingQuestions.join('；') || '基于整体评估'
      };
    },

    // 辅助方法：评估品行障碍风险
    assessConductDisorderRisk() {
      let riskLevel = '低风险';
      let evidence = [];
      let supportingQuestions = [];

      // 基于对立违抗评估品行障碍风险
      if (this.oppositionScorePercentage >= 70) {
        evidence.push('严重对立违抗行为可能发展为品行障碍');
        riskLevel = '中等风险';
        supportingQuestions.push('对立违抗维度高分');
      }

      // 基于多动/冲动评估
      if (this.hyperactivityScorePercentage >= 75) {
        evidence.push('严重冲动行为增加反社会行为风险');
        riskLevel = '中等风险';
      }

      return {
        level: riskLevel,
        evidence: evidence.join('；') || '当前症状表现未显示明显品行障碍风险',
        supportingQuestions: supportingQuestions.join('；') || '基于整体评估'
      };
    },

    // 辅助方法：评估学业功能损害
    assessAcademicImpairment() {
      let level = '无明显损害';
      let manifestations = [];
      let supportingQuestions = [];
      let interventions = [];

      if (this.attentionScorePercentage >= 60) {
        level = '中度损害';
        manifestations.push('注意力不集中影响课堂学习');
        supportingQuestions.push(this.getKeyAttentionQuestions());
        interventions.push('注意力训练', '学习环境调整');
      }

      if (this.attentionScorePercentage >= 75) {
        level = '重度损害';
        manifestations.push('无法完成学习任务');
        interventions.push('个性化教育计划', '可能需要特殊教育支持');
      }

      return {
        level,
        manifestations: manifestations.length > 0 ? manifestations : ['学业表现基本正常'],
        supportingQuestions: supportingQuestions.join('；') || '基于整体评估',
        interventions: interventions.length > 0 ? interventions : ['继续观察']
      };
    },

    // 辅助方法：评估社交功能损害
    assessSocialImpairment() {
      let level = '无明显损害';
      let manifestations = [];
      let supportingQuestions = [];
      let interventions = [];

      if (this.hyperactivityScorePercentage >= 60) {
        level = '中度损害';
        manifestations.push('冲动行为影响同伴关系');
        supportingQuestions.push(this.getKeyHyperactivityQuestions());
        interventions.push('社交技能训练', '冲动控制训练');
      }

      if (this.oppositionScorePercentage >= 60) {
        level = '重度损害';
        manifestations.push('对立行为导致社交冲突');
        supportingQuestions.push(this.getKeyOppositionQuestions());
        interventions.push('行为治疗', '冲突解决训练');
      }

      return {
        level,
        manifestations: manifestations.length > 0 ? manifestations : ['社交表现基本正常'],
        supportingQuestions: supportingQuestions.join('；') || '基于整体评估',
        interventions: interventions.length > 0 ? interventions : ['继续观察']
      };
    },

    // 辅助方法：评估家庭功能损害
    assessFamilyImpairment() {
      let level = '无明显损害';
      let manifestations = [];
      let supportingQuestions = [];
      let interventions = [];

      if (this.totalScorePercentage >= 60) {
        level = '中度损害';
        manifestations.push('行为问题增加家庭压力');
        supportingQuestions.push('总分较高');
        interventions.push('家长教育', '家庭支持');
      }

      if (this.oppositionScorePercentage >= 65) {
        level = '重度损害';
        manifestations.push('对立行为严重影响家庭和谐');
        supportingQuestions.push(this.getKeyOppositionQuestions());
        interventions.push('家庭治疗', '亲子关系改善');
      }

      return {
        level,
        manifestations: manifestations.length > 0 ? manifestations : ['家庭功能基本正常'],
        supportingQuestions: supportingQuestions.join('；') || '基于整体评估',
        interventions: interventions.length > 0 ? interventions : ['继续观察']
      };
    },

    // 🔥 生成医生专业建议（基于AI结果）
    generateDoctorProfessionalAdvice(aiResult) {
      const advice = [];

      advice.push('【AI智能分析 - 医生专用详细报告】');
      advice.push(`分析模型：${aiResult.modelUsed || 'qwen2.5:14b'}`);
      advice.push(`分析时间：${new Date().toLocaleString()}`);
      advice.push(`置信度：${Math.round((aiResult.confidence || 0) * 100)}%`);
      advice.push('');

      // 详细维度得分分析
      advice.push('【详细维度得分分析】');
      advice.push(`总分：${this.totalScore}/${this.maxTotalScore}分 (${this.totalScorePercentage}%)`);
      advice.push(`风险等级：${this.getRiskText(this.totalScorePercentage)}`);
      advice.push('');

      if (this.attentionScore !== null) {
        advice.push(`注意力缺陷维度：${this.attentionScore}/${this.maxAttentionScore}分 (${this.attentionScorePercentage}%)`);
        advice.push(`  - 风险等级：${this.getRiskText(this.attentionScorePercentage)}`);
        advice.push(`  - 临床意义：${this.getAttentionClinicalMeaning()}`);
      }

      if (this.hyperactivityScore !== null) {
        advice.push(`多动/冲动维度：${this.hyperactivityScore}/${this.maxHyperactivityScore}分 (${this.hyperactivityScorePercentage}%)`);
        advice.push(`  - 风险等级：${this.getRiskText(this.hyperactivityScorePercentage)}`);
        advice.push(`  - 临床意义：${this.getHyperactivityClinicalMeaning()}`);
      }

      if (this.oppositionScore !== null) {
        advice.push(`对立违抗维度：${this.oppositionScore}/${this.maxOppositionScore}分 (${this.oppositionScorePercentage}%)`);
        advice.push(`  - 风险等级：${this.getRiskText(this.oppositionScorePercentage)}`);
        advice.push(`  - 临床意义：${this.getOppositionClinicalMeaning()}`);
      }
      advice.push('');

      // 高分题目详细分析
      advice.push('【高分题目详细分析】');
      const highScoreQuestions = this.getHighScoreQuestionsForDoctor();
      if (highScoreQuestions.length > 0) {
        highScoreQuestions.forEach(q => {
          advice.push(`第${q.number}题 (${q.score}分)：${q.text}`);
          advice.push(`  - 临床意义：${q.clinicalSignificance}`);
          advice.push(`  - 风险指标：${q.riskLevel}`);
          advice.push(`  - 干预建议：${q.interventionSuggestion}`);
        });
      } else {
        advice.push('无明显高分题目');
      }
      advice.push('');

      // 家庭背景与症状关联分析
      if (this.userDetailInfo) {
        advice.push('【家庭背景与症状关联分析】');
        advice.push(`家庭结构：${this.userDetailInfo.familyStructure || '未提供'}`);
        advice.push(`医疗史：${this.userDetailInfo.medicalHistory || '未提供'}`);
        advice.push(`行为模式：${this.userDetailInfo.behaviorPatterns || '未提供'}`);
        advice.push(`学业表现：${this.userDetailInfo.academicPerformance || '未提供'}`);
        advice.push('');

        advice.push('关联分析结论：');
        advice.push(this.generateFamilySymptomAnalysisText());
        advice.push('');
      }

      // AI关键发现（基于具体题目）
      if (aiResult.keyFindings && aiResult.keyFindings.length > 0) {
        advice.push('【AI关键发现（含支持证据）】');
        aiResult.keyFindings.forEach((finding, index) => {
          advice.push(`${index + 1}. ${finding}`);
          // 如果有题目分析，添加支持证据
          if (aiResult.questionAnalysis && aiResult.questionAnalysis.length > 0) {
            const supportingQuestions = aiResult.questionAnalysis
              .filter(qa => qa.analysis && qa.analysis.includes(finding.substring(0, 10)))
              .map(qa => `第${qa.questionNumber}题`)
              .join('、');
            if (supportingQuestions) {
              advice.push(`   支持证据：${supportingQuestions}`);
            }
          }
        });
        advice.push('');
      }

      // 基于证据的结论详述
      if (aiResult.evidenceBasedConclusions && aiResult.evidenceBasedConclusions.length > 0) {
        advice.push('【基于证据的详细结论】');
        aiResult.evidenceBasedConclusions.forEach((conclusion, index) => {
          advice.push(`${index + 1}. 结论：${conclusion.conclusion}`);
          advice.push(`   支持题目：第${conclusion.supportingQuestions.join('、')}题`);
          advice.push(`   推理依据：${conclusion.reasoning}`);
          if (conclusion.familyFactors) {
            advice.push(`   家庭因素：${conclusion.familyFactors}`);
          }
          advice.push('');
        });
      }

      // 专业医疗建议（分优先级）
      if (aiResult.recommendations && aiResult.recommendations.length > 0) {
        advice.push('【分级专业医疗建议】');
        const prioritizedRecs = this.prioritizeRecommendations(aiResult.recommendations);

        advice.push('紧急建议：');
        prioritizedRecs.urgent.forEach(rec => advice.push(`  • ${rec}`));
        advice.push('');

        advice.push('重要建议：');
        prioritizedRecs.important.forEach(rec => advice.push(`  • ${rec}`));
        advice.push('');

        advice.push('一般建议：');
        prioritizedRecs.general.forEach(rec => advice.push(`  • ${rec}`));
        advice.push('');
      }

      // 需要关注的症状（含具体表现）
      if (aiResult.warningSignals && aiResult.warningSignals.length > 0) {
        advice.push('【需要重点关注的症状（含具体表现）】');
        aiResult.warningSignals.forEach((signal, index) => {
          if (typeof signal === 'object') {
            advice.push(`${index + 1}. ${signal.signal} (严重程度：${signal.severity})`);
            advice.push(`   基于题目：第${signal.basedOnQuestions.join('、')}题`);
            advice.push(`   需要行动：${signal.actionRequired}`);
          } else {
            advice.push(`${index + 1}. ${signal}`);
          }
        });
        advice.push('');
      }

      // 后续医疗步骤（详细时间线）
      if (aiResult.nextSteps && aiResult.nextSteps.length > 0) {
        advice.push('【建议的后续医疗步骤（含时间线）】');
        aiResult.nextSteps.forEach((step, index) => {
          advice.push(`${index + 1}. ${step}`);
        });
        advice.push('');
      }

      // 详细临床表现分析（基于具体题目）
      advice.push('【详细临床表现分析】');
      advice.push(this.generateDetailedClinicalAnalysis());
      advice.push('');

      // 症状严重程度评估（基于题目组合）
      advice.push('【症状严重程度评估】');
      advice.push(this.generateSeverityAssessment());
      advice.push('');

      // 共病风险评估
      advice.push('【共病风险评估】');
      advice.push(this.generateComorbidityRiskAssessment());
      advice.push('');

      // 功能损害评估
      advice.push('【功能损害评估】');
      advice.push(this.generateFunctionalImpairmentAssessment());
      advice.push('');

      // AI详细解读
      if (aiResult.interpretation) {
        advice.push('【AI详细解读】');
        advice.push(aiResult.interpretation);
        advice.push('');
      }

      // 医疗免责声明
      advice.push('【医疗免责声明】');
      advice.push('本AI分析结果仅供医疗专业人员参考，不能替代临床诊断。');
      advice.push('请结合患者的临床表现、病史和其他检查结果进行综合评估。');
      advice.push('如有疑问，建议进行进一步的专业评估和检查。');

      return advice.join('\n');
    },

    // 获取严重程度描述
    getSeverityDescription(severity) {
      switch (severity) {
        case '正常':
          return '评估结果在正常范围内，继续保持良好状态';
        case '轻度':
          return '存在轻度症状，建议关注并采取预防措施';
        case '中度':
          return '存在中度症状，建议寻求专业指导';
        case '重度':
          return '存在重度症状，强烈建议专业医疗评估';
        default:
          return '评估完成，请参考专业建议';
      }
    },

    // 返回量表选择页面
    goBackToScaleSelection() {
      this.showScaleSelectionPage = true;
      this.showInputMethodSelection = false;
      this.showResult = false;
      this.currentScale = '';
      this.answers = [];
      this.inputMethod = 'text';

      // 停止语音功能
      this.stopAllVoiceFeatures();
    },

    // 初始化语音功能
    initializeVoiceFeatures() {
      // 初始化语音合成
      if ('speechSynthesis' in window) {
        this.speechSynthesis = window.speechSynthesis;
      } else {
        this.showMessage('您的浏览器不支持语音合成功能', 'error');
      }

      // 初始化语音识别
      if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        try {
          const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
          this.speechRecognition = new SpeechRecognition();

          this.speechRecognition.continuous = false;
          this.speechRecognition.interimResults = false;
          this.speechRecognition.lang = 'zh-CN';

          this.speechRecognition.onresult = (event) => {
            const transcript = event.results[0][0].transcript;
            console.log('🎤 语音识别结果:', transcript);
            this.voiceRecognitionResult = transcript;
            this.isListening = false;
            this.assistantState = 'thinking';
            this.assistantStatusText = '正在分析您的回答...';

            // 风险检测
            this.performRiskDetection(transcript);
          };

          this.speechRecognition.onerror = (event) => {
            console.error('语音识别错误:', event);
            this.isListening = false;
            this.assistantState = 'idle';
            this.assistantStatusText = '语音识别失败，请重试';

            let errorMessage = '语音识别失败，请重试';
            if (event && event.error) {
              switch (event.error) {
                case 'no-speech':
                  errorMessage = '没有检测到语音，请重试';
                  break;
                case 'audio-capture':
                  errorMessage = '无法访问麦克风，请检查权限';
                  break;
                case 'not-allowed':
                  errorMessage = '麦克风权限被拒绝，请允许访问麦克风';
                  break;
                case 'network':
                  errorMessage = '网络错误，请检查网络连接';
                  break;
                default:
                  errorMessage = `语音识别失败: ${event.error}`;
              }
            }

            this.showMessage(errorMessage, 'error');
          };

          this.speechRecognition.onend = () => {
            this.isListening = false;
            if (this.assistantState === 'listening') {
              this.assistantState = 'idle';
              this.assistantStatusText = '请点击按钮开始回答';
            }
          };

        } catch (error) {
          console.error('初始化语音识别失败:', error);
          this.showMessage('初始化语音识别失败，请刷新页面重试', 'error');
        }
      } else {
        this.showMessage('您的浏览器不支持语音识别功能，请使用Chrome浏览器', 'error');
      }
    },

    // 停止所有语音功能
    stopAllVoiceFeatures() {
      // 停止语音合成
      if (this.speechSynthesis) {
        this.speechSynthesis.cancel();
        this.isSpeaking = false;
      }

      // 停止语音识别
      if (this.speechRecognition && this.isListening) {
        this.speechRecognition.stop();
        this.isListening = false;
      }

      // 重置状态
      this.assistantState = 'idle';
      this.assistantStatusText = '您好！我是您的AI助手，准备开始评估';
      this.voiceRecognitionResult = '';
      this.riskAlert.show = false;
    },

    // 获取当前语音问题
    getCurrentVoiceQuestion() {
      const currentScaleData = this.scaleQuestions[this.currentScale];

      if (currentScaleData.questions) {
        // 普通量表
        return currentScaleData.questions[this.currentVoiceQuestionIndex] || '';
      } else if (currentScaleData.attentionQuestions) {
        // ADHD量表
        const attentionCount = currentScaleData.attentionQuestions.length;
        const hyperactivityCount = currentScaleData.hyperactivityQuestions.length;
        const oppositionCount = currentScaleData.oppositionQuestions ? currentScaleData.oppositionQuestions.length : 0;

        if (this.currentVoiceQuestionIndex < attentionCount) {
          return currentScaleData.attentionQuestions[this.currentVoiceQuestionIndex];
        } else if (this.currentVoiceQuestionIndex < attentionCount + hyperactivityCount) {
          return currentScaleData.hyperactivityQuestions[this.currentVoiceQuestionIndex - attentionCount];
        } else if (this.currentVoiceQuestionIndex < attentionCount + hyperactivityCount + oppositionCount) {
          return currentScaleData.oppositionQuestions[this.currentVoiceQuestionIndex - attentionCount - hyperactivityCount];
        }
      }

      return '';
    },

    // 朗读当前题目
    readCurrentQuestion() {
      if (!this.speechSynthesis) return;

      const questionText = this.getCurrentVoiceQuestion();
      if (!questionText) return;

      // 停止当前朗读
      this.speechSynthesis.cancel();

      this.assistantState = 'speaking';
      this.assistantStatusText = '正在为您朗读题目...';

      const utterance = new SpeechSynthesisUtterance(questionText);
      utterance.lang = 'zh-CN';
      utterance.rate = 0.8; // 稍慢的语速
      utterance.pitch = 1.1; // 稍高的音调，更友好

      utterance.onstart = () => {
        this.isSpeaking = true;
      };

      utterance.onend = () => {
        this.isSpeaking = false;
        this.assistantState = 'idle';
        this.assistantStatusText = '请点击语音按钮回答问题';
      };

      utterance.onerror = () => {
        this.isSpeaking = false;
        this.assistantState = 'idle';
        this.assistantStatusText = '语音朗读失败，请重试';
        this.showMessage('语音朗读失败', 'error');
      };

      this.speechSynthesis.speak(utterance);
    },

    // 切换语音录音状态
    toggleVoiceRecording() {
      if (this.isListening) {
        this.stopVoiceRecording();
      } else {
        this.startVoiceRecording();
      }
    },

    // 开始语音录音
    startVoiceRecording() {
      if (!this.speechRecognition) {
        this.showMessage('语音识别未初始化，请刷新页面重试', 'error');
        return;
      }

      this.voiceRecognitionResult = '';
      this.isListening = true;
      this.assistantState = 'listening';
      this.assistantStatusText = '正在听您说话，请开始回答...';

      try {
        this.speechRecognition.start();
        this.showMessage('请开始说话...', 'info');
      } catch (error) {
        console.error('启动语音识别失败:', error);
        this.isListening = false;
        this.assistantState = 'idle';
        this.assistantStatusText = '启动失败，请重试';

        let errorMessage = '启动语音识别失败，请重试';
        if (error && error.message) {
          errorMessage = error.message;
        }

        this.showMessage(errorMessage, 'error');
      }
    },

    // 停止语音录音
    stopVoiceRecording() {
      if (this.speechRecognition && this.isListening) {
        this.speechRecognition.stop();
      }
    },

    // 确认语音答案
    confirmVoiceAnswer() {
      if (!this.voiceRecognitionResult) return;

      // 智能匹配答案选项
      const selectedOption = this.matchVoiceToOption(this.voiceRecognitionResult);

      // 保存答案
      this.saveVoiceAnswer(selectedOption);

      // 更新助手状态
      this.assistantState = 'idle';
      this.assistantStatusText = '答案已保存，准备下一题...';

      // 进入下一题
      setTimeout(() => {
        this.nextVoiceQuestion();
      }, 1000);
    },

    // 重新录音
    retryVoiceInput() {
      this.voiceRecognitionResult = '';
      this.assistantState = 'idle';
      this.assistantStatusText = '请重新回答这个问题';
    },

    // 智能匹配语音到选项
    matchVoiceToOption(voiceText) {
      const text = voiceText.toLowerCase();

      // 根据关键词匹配选项
      if (text.includes('没有') || text.includes('不') || text.includes('否') || text.includes('从不')) {
        return 0;
      } else if (text.includes('有一点') || text.includes('偶尔') || text.includes('轻微') || text.includes('很少')) {
        return 1;
      } else if (text.includes('不少') || text.includes('经常') || text.includes('中等') || text.includes('有时')) {
        return 2;
      } else if (text.includes('很多') || text.includes('总是') || text.includes('严重') || text.includes('非常')) {
        return 3;
      }

      // 默认返回中等选项
      return 1;
    },

    // 保存语音答案
    saveVoiceAnswer(optionValue) {
      // 保存到answers数组 (Vue 3中直接赋值即可)
      this.answers[this.currentVoiceQuestionIndex] = optionValue;

      console.log('💾 保存语音答案:', {
        questionIndex: this.currentVoiceQuestionIndex,
        voiceInput: this.voiceRecognitionResult,
        selectedOption: optionValue
      });
    },

    // 下一题
    nextVoiceQuestion() {
      const totalQuestions = this.getTotalQuestionCount();

      if (this.currentVoiceQuestionIndex < totalQuestions - 1) {
        this.currentVoiceQuestionIndex++;
        this.voiceRecognitionResult = '';
        this.assistantStatusText = '准备下一题...';

        // 自动朗读下一题
        setTimeout(() => {
          this.readCurrentQuestion();
        }, 1500);
      } else {
        // 所有题目完成，询问是否提供额外信息
        this.askForAdditionalInfo();
      }
    },

    // 询问额外信息
    askForAdditionalInfo() {
      this.assistantState = 'idle';
      this.assistantStatusText = '恭喜您完成了所有问题！';

      this.$confirm(
        '您已完成所有问题的回答。是否愿意提供一些额外的信息来帮助我们更好地分析您的情况？',
        '额外信息收集',
        {
          confirmButtonText: '愿意提供',
          cancelButtonText: '跳过',
          type: 'info'
        }
      ).then(() => {
        // 用户同意提供额外信息
        this.collectAdditionalInfo();
      }).catch(() => {
        // 用户拒绝，直接提交
        this.submitVoiceScale();
      });
    },

    // 收集额外信息
    collectAdditionalInfo() {
      // 这里可以实现额外信息收集界面
      // 暂时直接提交
      this.submitVoiceScale();
    },

    // 提交语音量表
    submitVoiceScale() {
      // 计算分数
      this.calculateScores();

      // 显示结果
      this.showResult = true;
      this.inputMethod = 'text'; // 切换到文字模式显示结果

      // 提交数据
      this.submitScaleData();
    },

    // 风险检测
    performRiskDetection(text) {
      const lowerText = text.toLowerCase();

      // 检测红色警报关键词
      const redKeywords = this.riskKeywords.red.filter(keyword => lowerText.includes(keyword));
      if (redKeywords.length > 0) {
        this.triggerRiskAlert('red', redKeywords, text);
        return;
      }

      // 检测橙色警报关键词
      const orangeKeywords = this.riskKeywords.orange.filter(keyword => lowerText.includes(keyword));
      if (orangeKeywords.length > 0) {
        this.triggerRiskAlert('orange', orangeKeywords, text);
        return;
      }

      // 检测黄色警报关键词
      const yellowKeywords = this.riskKeywords.yellow.filter(keyword => lowerText.includes(keyword));
      if (yellowKeywords.length > 0) {
        this.triggerRiskAlert('yellow', yellowKeywords, text);
        return;
      }
    },

    // 触发风险警报
    triggerRiskAlert(level, keywords, originalText) {
      console.warn(`🚨 检测到${level}级风险信号:`, keywords);

      // 立即停止所有语音功能
      this.stopAllVoiceFeatures();

      // 设置警报信息
      this.riskAlert.show = true;
      this.riskAlert.level = level;
      this.riskAlert.detectedText = originalText;

      switch (level) {
        case 'red':
          this.riskAlert.title = '🚨 高危风险警报';
          this.riskAlert.message = '系统检测到您可能正在经历严重的心理危机。您的生命很宝贵，请立即寻求专业帮助。';
          break;
        case 'orange':
          this.riskAlert.title = '⚠️ 中度风险警报';
          this.riskAlert.message = '系统检测到您可能有自我伤害的倾向。建议您尽快联系心理健康专业人士。';
          break;
        case 'yellow':
          this.riskAlert.title = '💛 轻度关注提醒';
          this.riskAlert.message = '系统检测到您可能正在经历一些情绪困扰。建议关注心理健康状态。';
          break;
      }

      // 发送风险警报到后端
      this.sendRiskAlert(level, keywords, originalText);
    },

    // 继续评估（警报后）
    continueAfterAlert() {
      this.riskAlert.show = false;
      this.assistantState = 'idle';
      this.assistantStatusText = '请继续回答当前问题';
    },

    // 停止评估（警报后）
    stopAfterAlert() {
      this.riskAlert.show = false;
      this.goBackToScaleSelection();
    },

    // 发送风险警报到后端
    async sendRiskAlert(level, keywords, originalText) {
      try {
        const alertData = {
          userId: this.currentUser.userId || 'anonymous',
          username: this.currentUser.username || 'anonymous',
          scaleName: this.scaleTitle,
          riskLevel: level,
          riskKeywords: keywords,
          originalText: originalText,
          detectionTime: new Date().toISOString(),
          currentQuestionIndex: this.currentVoiceQuestionIndex,
          inputMethod: this.inputMethod
        };

        console.log('📤 发送风险警报数据:', alertData);

        // TODO: 实现后端API调用
        // await this.$http.post('/api/risk-alert', alertData);

      } catch (error) {
        console.error('发送风险警报失败:', error);
      }
    },

    // 安全的消息显示方法
    showMessage(message, type = 'info') {
      try {
        if (this.$message && typeof this.$message[type] === 'function') {
          this.$message[type](message);
        } else {
          // 降级到控制台输出
          console.log(`[${type.toUpperCase()}] ${message}`);

          // 或者使用原生alert作为备选
          if (type === 'error') {
            console.error(message);
          } else {
            console.info(message);
          }
        }
      } catch (error) {
        console.error('显示消息失败:', error);
        console.log(`[${type.toUpperCase()}] ${message}`);
      }
    },













    // 保存结果
    saveResult() {
      const result = {
        scaleType: this.currentScale,
        scaleTitle: this.currentScaleData.title,
        totalScore: this.totalScore,
        attentionScore: this.attentionScore,
        hyperactivityScore: this.hyperactivityScore,
        interpretation: this.interpretationText,
        timestamp: new Date().toISOString()
      };

      // 保存到本地存储
      const savedResults = JSON.parse(localStorage.getItem('adhdScaleResults') || '[]');
      savedResults.push(result);
      localStorage.setItem('adhdScaleResults', JSON.stringify(savedResults));

      alert('结果已保存！');
    },

    // 打印结果
    printResult() {
      window.print();
    },

    // 重新测试
    retakeTest() {
      this.showResult = false;
      this.initializeAnswers();

      // 滚动到问题区域
      this.$nextTick(() => {
        const questionsSection = document.querySelector('.questions-section');
        if (questionsSection) {
          questionsSection.scrollIntoView({ behavior: 'smooth' });
        }
      });
    }
  }
}
</script>

<style scoped>
.adhd-scale-page {
  background-color: #f5f7fa;
  min-height: 100vh;
}

.content-wrapper {
  display: flex;
  min-height: calc(100vh - 60px);
}

.main-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 量表主容器 */
.scale-main-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.scale-main-container:hover {
  box-shadow: 0 12px 28px rgba(71, 118, 230, 0.15);
}

/* 量表容器 */
.scale-container {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  overflow: visible;
}

.area-header {
  padding: 18px 25px;
  background: linear-gradient(to right, #e8efff, #f8f9fc);
  border-bottom: 1px solid #e3e6f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.area-header h3 {
  font-size: 20px;
  color: #3a5bbd;
  display: flex;
  align-items: center;
}

.area-header h3 i {
  margin-right: 12px;
  font-size: 22px;
  color: #4e73df;
}

.back-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s;
}

.back-btn:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

/* 输入方式选择页面样式 */
.input-method-selection-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.method-selection-content {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.selection-description {
  text-align: center;
  margin-bottom: 40px;
}

.selection-description h4 {
  font-size: 24px;
  color: #2c3e50;
  margin-bottom: 12px;
  font-weight: 600;
}

.selection-description p {
  color: #7f8c8d;
  font-size: 16px;
  line-height: 1.6;
}

.method-cards-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-top: 40px;
}

.method-card {
  background: white;
  border: 3px solid #e9ecef;
  border-radius: 20px;
  padding: 32px;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.method-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 16px 48px rgba(0, 0, 0, 0.15);
}

.voice-method-card:hover {
  border-color: #e74c3c;
  box-shadow: 0 16px 48px rgba(231, 76, 60, 0.2);
}

.text-method-card:hover {
  border-color: #3498db;
  box-shadow: 0 16px 48px rgba(52, 152, 219, 0.2);
}

.card-header {
  text-align: center;
  margin-bottom: 24px;
}

.card-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  transition: all 0.3s ease;
}

.voice-icon {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.text-icon {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.card-icon i {
  font-size: 36px;
  color: white;
}

.method-card:hover .card-icon {
  transform: scale(1.1) rotate(5deg);
}

.card-header h4 {
  font-size: 20px;
  color: #2c3e50;
  margin: 0;
  font-weight: 600;
}

.card-content {
  flex: 1;
  margin-bottom: 24px;
}

.card-description {
  color: #7f8c8d;
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 24px;
  text-align: center;
}

.card-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #27ae60;
  font-size: 14px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.feature-item i {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.method-card:hover .feature-item {
  background: #e8f5e8;
  transform: translateX(4px);
}

.card-footer {
  margin-top: auto;
}

.select-btn {
  width: 100%;
  padding: 16px 24px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 16px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.voice-select-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.text-select-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.method-card:hover .select-btn {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

/* 语音识别界面样式 */
.voice-interface-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 20px;
}

.voice-interface-content {
  background: white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* AI助手区域 */
.ai-assistant-section {
  text-align: center;
  margin-bottom: 40px;
}

.assistant-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.assistant-face {
  width: 140px;
  height: 140px;
  position: relative;
  transition: all 0.3s ease;
}

.assistant-face.speaking {
  animation: speaking 0.6s ease-in-out infinite alternate;
}

.assistant-face.listening {
  animation: listening 1.5s ease-in-out infinite;
}

.assistant-face.thinking {
  animation: thinking 1s ease-in-out infinite;
}

@keyframes speaking {
  0% { transform: scale(1); }
  100% { transform: scale(1.05); }
}

@keyframes listening {
  0% { transform: scale(1); box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4); }
  50% { transform: scale(1.02); box-shadow: 0 0 0 20px rgba(52, 152, 219, 0); }
  100% { transform: scale(1); box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
}

@keyframes thinking {
  0% { transform: rotate(-2deg); }
  50% { transform: rotate(2deg); }
  100% { transform: rotate(-2deg); }
}

.face-circle {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #ffd93d 0%, #ff6b35 100%);
  border-radius: 50%;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 32px rgba(255, 107, 53, 0.3);
}

.eyes {
  display: flex;
  gap: 24px;
  margin-bottom: 12px;
}

.eye {
  width: 18px;
  height: 18px;
  background: white;
  border-radius: 50%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pupil {
  width: 10px;
  height: 10px;
  background: #2c3e50;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.assistant-face.speaking .pupil {
  transform: scale(1.2);
}

.assistant-face.listening .pupil {
  animation: pupilMove 2s ease-in-out infinite;
}

@keyframes pupilMove {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-2px); }
  75% { transform: translateX(2px); }
}

.mouth {
  width: 28px;
  height: 14px;
  border: 3px solid #2c3e50;
  border-top: none;
  border-radius: 0 0 28px 28px;
  transition: all 0.3s ease;
}

.mouth.speaking {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 3px solid #2c3e50;
  animation: mouthSpeak 0.4s ease-in-out infinite alternate;
}

.mouth.listening {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 3px solid #2c3e50;
  background: #2c3e50;
}

@keyframes mouthSpeak {
  0% { transform: scaleY(1); }
  100% { transform: scaleY(0.6); }
}

.cheeks {
  position: absolute;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.cheek {
  position: absolute;
  width: 16px;
  height: 16px;
  background: rgba(255, 182, 193, 0.6);
  border-radius: 50%;
  top: 50%;
  transform: translateY(-50%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.left-cheek {
  left: 15px;
}

.right-cheek {
  right: 15px;
}

.assistant-face.speaking .cheek {
  opacity: 1;
}

.assistant-status {
  text-align: center;
}

.status-text {
  font-size: 16px;
  color: #7f8c8d;
  font-weight: 500;
  margin-bottom: 8px;
}

.status-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin: 0 auto;
  transition: all 0.3s ease;
}

.status-indicator.idle {
  background: #95a5a6;
}

.status-indicator.speaking {
  background: #e74c3c;
  animation: pulse 1s ease-in-out infinite;
}

.status-indicator.listening {
  background: #3498db;
  animation: pulse 1s ease-in-out infinite;
}

.status-indicator.thinking {
  background: #f39c12;
  animation: pulse 1s ease-in-out infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

/* 题目显示区域 */
.question-display-section {
  margin-bottom: 40px;
}

.question-progress {
  margin-bottom: 24px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.current-question {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.total-questions {
  font-size: 14px;
  color: #7f8c8d;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #ecf0f1;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #3498db, #2980b9);
  border-radius: 4px;
  transition: width 0.5s ease;
}

.question-content {
  background: #f8f9fa;
  border-radius: 16px;
  padding: 32px;
  border-left: 6px solid #3498db;
}

.question-text {
  font-size: 18px;
  color: #2c3e50;
  line-height: 1.6;
  text-align: center;
  font-weight: 500;
}

/* 语音控制区域 */
.voice-controls-section {
  text-align: center;
}

.control-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 32px;
}

.control-btn {
  padding: 16px 32px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  min-width: 160px;
  justify-content: center;
}

.read-question-btn {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
}

.read-question-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #229954, #1e8449);
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(39, 174, 96, 0.3);
}

.voice-input-btn {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
  color: white;
}

.voice-input-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #c0392b, #a93226);
  transform: translateY(-3px);
  box-shadow: 0 8px 24px rgba(231, 76, 60, 0.3);
}

.voice-input-btn.recording {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  animation: recordingPulse 1.5s ease-in-out infinite;
}

@keyframes recordingPulse {
  0% { box-shadow: 0 0 0 0 rgba(243, 156, 18, 0.7); }
  70% { box-shadow: 0 0 0 15px rgba(243, 156, 18, 0); }
  100% { box-shadow: 0 0 0 0 rgba(243, 156, 18, 0); }
}

.control-btn:disabled {
  background: #bdc3c7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.control-btn i {
  font-size: 18px;
}

/* 语音识别结果 */
.recognition-result {
  background: linear-gradient(135deg, #e8f5e8, #d5f4e6);
  border: 2px solid #27ae60;
  border-radius: 16px;
  padding: 24px;
  margin-top: 24px;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #27ae60;
  font-weight: 600;
}

.result-text {
  font-size: 16px;
  color: #2c3e50;
  margin-bottom: 20px;
  text-align: center;
  font-style: italic;
  padding: 16px;
  background: white;
  border-radius: 12px;
  border: 1px solid #d5f4e6;
}

.result-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.result-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.confirm-btn {
  background: linear-gradient(135deg, #27ae60, #229954);
  color: white;
}

.confirm-btn:hover {
  background: linear-gradient(135deg, #229954, #1e8449);
  transform: translateY(-2px);
}

.retry-btn {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
}

.retry-btn:hover {
  background: linear-gradient(135deg, #e67e22, #d35400);
  transform: translateY(-2px);
}

/* 风险警报样式 */
.risk-alert-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.risk-alert-modal {
  background: white;
  border-radius: 20px;
  padding: 40px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  position: relative;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from { opacity: 0; transform: translateY(50px); }
  to { opacity: 1; transform: translateY(0); }
}

.alert-yellow {
  border-left: 8px solid #f39c12;
}

.alert-orange {
  border-left: 8px solid #e67e22;
}

.alert-red {
  border-left: 8px solid #e74c3c;
}

.alert-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #ecf0f1;
}

.alert-yellow .alert-header i {
  color: #f39c12;
  font-size: 32px;
}

.alert-orange .alert-header i {
  color: #e67e22;
  font-size: 32px;
}

.alert-red .alert-header i {
  color: #e74c3c;
  font-size: 32px;
  animation: shake 0.5s ease-in-out infinite;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.alert-header h3 {
  margin: 0;
  font-size: 24px;
  color: #2c3e50;
  font-weight: 700;
}

.alert-content {
  margin-bottom: 32px;
}

.alert-message {
  font-size: 16px;
  color: #2c3e50;
  line-height: 1.6;
  margin-bottom: 20px;
}

.detected-content {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 24px;
  border-left: 4px solid #e74c3c;
}

.detected-content strong {
  color: #2c3e50;
  display: block;
  margin-bottom: 8px;
}

.risk-text {
  color: #e74c3c;
  font-style: italic;
  font-weight: 500;
}

.emergency-contacts {
  background: linear-gradient(135deg, #e8f5e8, #d5f4e6);
  border-radius: 12px;
  padding: 20px;
  border: 2px solid #27ae60;
}

.emergency-contacts h4 {
  margin: 0 0 16px 0;
  color: #27ae60;
  font-size: 18px;
  font-weight: 600;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  color: #2c3e50;
  font-weight: 500;
}

.contact-item i {
  color: #27ae60;
  font-size: 16px;
  width: 20px;
}

.alert-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
}

.alert-btn {
  padding: 16px 32px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  min-width: 140px;
  justify-content: center;
}

.continue-btn {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;
}

.continue-btn:hover {
  background: linear-gradient(135deg, #2980b9, #1f4e79);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(52, 152, 219, 0.3);
}

.stop-btn {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  color: white;
}

.stop-btn:hover {
  background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(149, 165, 166, 0.3);
}

/* 年龄选择页面 */
.age-selection-content {
  padding: 40px;
  text-align: center;
}

.age-intro {
  margin-bottom: 40px;
}

.age-intro h2 {
  color: #4e73df;
  margin-bottom: 20px;
  font-size: 28px;
}

.age-intro p {
  color: #7e7e7e;
  font-size: 16px;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.age-cards {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
  max-width: 800px;
  margin: 0 auto;
}

.age-card {
  background: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  border: 2px solid transparent;
}

.age-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border-color: #4e73df;
}

.age-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #4e73df, #224abe);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.age-icon i {
  color: white;
  font-size: 36px;
}

.age-card h3 {
  color: #4e4e4e;
  margin-bottom: 10px;
  font-size: 24px;
}

.age-card > p {
  color: #4e73df;
  font-weight: 600;
  margin-bottom: 15px;
  font-size: 18px;
}

.age-description p {
  color: #7e7e7e;
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: 25px;
}

.select-age-btn {
  background: linear-gradient(to right, #4e73df, #3a5bbd);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 10px rgba(71, 118, 230, 0.2);
}

.select-age-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(71, 118, 230, 0.3);
}

/* 量表内容 */
.scale-content {
  padding: 25px;
}

.scale-intro {
  background: #f8f9ff;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 30px;
  border-left: 4px solid #4e73df;
}

.scale-intro h4 {
  color: #4e73df;
  margin-bottom: 10px;
  font-size: 18px;
}

.scale-intro p {
  color: #4e4e4e;
  line-height: 1.6;
}

/* 问题组 */
.question-group {
  margin-bottom: 40px;
}

.question-group h4 {
  color: #4e73df;
  margin-bottom: 20px;
  font-size: 18px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e8efff;
}

.question-item {
  background: white;
  border: 1px solid #e3e6f0;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 15px;
  transition: all 0.3s;
}

.question-item:hover {
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  border-color: #4e73df;
}

.question-text {
  color: #4e4e4e;
  font-weight: 500;
  margin-bottom: 15px;
  line-height: 1.5;
}

.question-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
}

.option-label {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  padding: 10px;
  border-radius: 8px;
  transition: all 0.3s;
  border: 2px solid transparent;
}

.option-label:hover {
  background: #f8f9ff;
  border-color: #e8efff;
}

.option-label input[type="radio"] {
  margin-bottom: 8px;
  transform: scale(1.2);
  accent-color: #4e73df;
}

.option-text {
  font-size: 14px;
  color: #4e4e4e;
  text-align: center;
}

/* 特殊选项样式（用于儿童注意力水平测评量表等） */
.option-label.special-option {
  flex-direction: row;
  align-items: flex-start;
  text-align: left;
  padding: 15px;
  border: 1px solid #e3e6f0;
  margin-bottom: 10px;
  grid-column: 1 / -1; /* 占满整行 */
}

.option-label.special-option:hover {
  border-color: #4e73df;
  background-color: #f8f9fc;
}

.option-label.special-option input[type="radio"] {
  margin-right: 12px;
  margin-bottom: 0;
  margin-top: 2px;
}

.option-label.special-option .option-text {
  line-height: 1.4;
  text-align: left;
  font-size: 15px;
}

/* 提交区域 */
.submit-section {
  text-align: center;
  margin-top: 40px;
  padding: 30px;
  background: #f8f9fc;
  border-radius: 10px;
}

.submit-btn {
  background: linear-gradient(to right, #1cc88a, #17a673);
  color: white;
  border: none;
  padding: 15px 40px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 10px rgba(28, 200, 138, 0.2);
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 auto;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(28, 200, 138, 0.3);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.completion-hint {
  color: #f6c23e;
  margin-top: 15px;
  font-size: 14px;
}

.submitting-hint {
  color: #3498db;
  font-size: 14px;
  margin-top: 15px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.submit-btn .fa-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* 结果区域 */
.result-section {
  background: #f8f9ff;
  border-radius: 15px;
  padding: 30px;
  margin-bottom: 30px;
  border: 2px solid #e8efff;
}

/* 患者版结果样式 */
.patient-result-summary {
  text-align: center;
}

.completion-status {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #4CAF50, #45a049);
  border-radius: 12px;
  color: white;
}

.status-icon {
  font-size: 3rem;
  margin-right: 20px;
}

.status-text h3 {
  margin: 0 0 10px 0;
  font-size: 1.8rem;
}

.status-text p {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.basic-info {
  background: white;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 25px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-weight: 600;
  color: #666;
}

.info-value {
  color: #333;
}

.status-success {
  color: #4CAF50;
  font-weight: 600;
}

.next-steps {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  text-align: left;
}

.next-steps h4 {
  color: #4a6bdf;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.next-steps h4 i {
  margin-right: 10px;
}

.next-steps ul {
  list-style: none;
  padding: 0;
}

.next-steps li {
  padding: 10px 0;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f0f0f0;
}

.next-steps li:last-child {
  border-bottom: none;
}

.next-steps li i {
  margin-right: 12px;
  color: #4CAF50;
  width: 16px;
}

.patient-friendly-info {
  margin-top: 25px;
}

.info-card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.info-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  color: #4a6bdf;
}

.info-header i {
  margin-right: 10px;
  font-size: 1.2rem;
}

.info-header h4 {
  margin: 0;
}

.info-content p {
  margin-bottom: 15px;
  color: #666;
  line-height: 1.6;
}

.info-content ul {
  list-style: none;
  padding: 0;
}

.info-content li {
  padding: 8px 0;
  display: flex;
  align-items: center;
  color: #555;
}

.info-content li i {
  margin-right: 10px;
  color: #4CAF50;
  width: 16px;
}



/* 详细AI分析样式 */
.detailed-ai-analysis {
  margin-top: 30px;
  border-top: 2px dashed #e0e7ff;
  padding-top: 25px;
}

.detailed-ai-analysis h4 {
  font-size: 20px;
  color: #4a6bdf;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.detailed-ai-analysis h4 i {
  margin-right: 10px;
}

.detailed-ai-analysis h5 {
  font-size: 18px;
  color: #5a6bdf;
  margin: 20px 0 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e7ff;
}

.analysis-section {
  margin-bottom: 30px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
}

/* 题目分析网格 */
.question-analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.question-analysis-item {
  border-radius: 10px;
  padding: 15px;
  background: #f9faff;
  border-left: 4px solid #ccc;
}

.question-analysis-item.risk-high {
  border-left-color: #ff6b6b;
  background: #fff8f8;
}

.question-analysis-item.risk-medium {
  border-left-color: #ffc107;
  background: #fffbf0;
}

.question-analysis-item.risk-low {
  border-left-color: #4dabf7;
  background: #f0f9ff;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.question-number {
  font-weight: bold;
  font-size: 16px;
  color: #555;
}

.risk-badge {
  padding: 3px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  background: #aaa;
}

.risk-badge.risk-high {
  background: #ff6b6b;
}

.risk-badge.risk-medium {
  background: #ffc107;
  color: #333;
}

.risk-badge.risk-low {
  background: #4dabf7;
}

.question-content p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

.question-text {
  font-weight: 500;
}

/* 基于证据的结论 */
.evidence-conclusion {
  background: #f5f7ff;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 15px;
}

.conclusion-header h6 {
  font-size: 16px;
  color: #4a5568;
  margin: 0 0 10px;
}

.conclusion-details p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 家庭背景分析 */
.family-analysis-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
}

.family-factor {
  background: #f0f4ff;
  border-radius: 10px;
  padding: 15px;
}

.family-factor h6 {
  font-size: 15px;
  color: #4a5568;
  margin: 0 0 10px;
}

.family-factor p {
  font-size: 14px;
  line-height: 1.5;
  margin: 0;
}

/* 详细建议 */
.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.recommendation-item {
  background: #f5f9ff;
  border-radius: 10px;
  padding: 15px;
  border-left: 4px solid #4dabf7;
}

.recommendation-item.priority-high {
  border-left-color: #ff6b6b;
  background: #fff8f8;
}

.recommendation-item.priority-medium {
  border-left-color: #ffc107;
  background: #fffbf0;
}

.rec-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.rec-category {
  font-weight: 500;
  color: #4a5568;
}

.rec-priority {
  padding: 3px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  background: #4dabf7;
}

.rec-priority.priority-high {
  background: #ff6b6b;
}

.rec-priority.priority-medium {
  background: #ffc107;
  color: #333;
}

.rec-content p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

/* 预警信号 */
.warning-signals-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.warning-item {
  background: #f5f9ff;
  border-radius: 10px;
  padding: 15px;
  border-left: 4px solid #4dabf7;
}

.warning-item.severity-重度,
.warning-item.severity-高度 {
  border-left-color: #ff6b6b;
  background: #fff8f8;
}

.warning-item.severity-中度 {
  border-left-color: #ffc107;
  background: #fffbf0;
}

.warning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.warning-signal {
  font-weight: 500;
  color: #4a5568;
}

.warning-severity {
  padding: 3px 8px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  color: white;
  background: #4dabf7;
}

.warning-severity.severity-重度,
.warning-severity.severity-高度 {
  background: #ff6b6b;
}

.warning-severity.severity-中度 {
  background: #ffc107;
  color: #333;
}

.warning-details p {
  margin: 8px 0;
  font-size: 14px;
  line-height: 1.5;
}

.result-header h3 {
  color: #4e73df;
  margin-bottom: 25px;
  font-size: 24px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.score-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.score-item {
  background: white;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid #e3e6f0;
  transition: all 0.3s ease;
}

.score-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.score-item.total-score {
  border: 2px solid #4e73df;
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

.score-label {
  color: #7e7e7e;
  font-size: 14px;
  margin-bottom: 10px;
  font-weight: 500;
}

.score-value {
  color: #4e73df;
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 8px;
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.current-score {
  color: #4e73df;
  font-size: 28px;
}

.score-separator {
  color: #7e7e7e;
  font-size: 20px;
  font-weight: 400;
}

.max-score {
  color: #a8a8a8;
  font-size: 20px;
  font-weight: 500;
}

.score-percentage {
  color: #28a745;
  font-size: 12px;
  font-weight: 600;
  background: #f8fff9;
  padding: 4px 8px;
  border-radius: 12px;
  display: inline-block;
  border: 1px solid #d4edda;
  margin-bottom: 8px;
}

.score-progress {
  width: 100%;
  height: 8px;
  background-color: #f1f3f4;
  border-radius: 4px;
  overflow: hidden;
  margin-top: 8px;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #4e73df 0%, #36b9cc 100%);
  border-radius: 4px;
  transition: width 0.8s ease-in-out;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* 风险等级样式 */
.score-percentage.risk-minimal {
  color: #28a745;
  background: #f8fff9;
  border-color: #d4edda;
}

.score-percentage.risk-low {
  color: #ffc107;
  background: #fffdf5;
  border-color: #ffeaa7;
}

.score-percentage.risk-medium {
  color: #fd7e14;
  background: #fff8f5;
  border-color: #ffd19a;
}

.score-percentage.risk-high {
  color: #dc3545;
  background: #fff5f5;
  border-color: #f5c6cb;
}

.progress-bar.risk-minimal {
  background: linear-gradient(90deg, #28a745 0%, #20c997 100%);
}

.progress-bar.risk-low {
  background: linear-gradient(90deg, #ffc107 0%, #ffdb4d 100%);
}

.progress-bar.risk-medium {
  background: linear-gradient(90deg, #fd7e14 0%, #ff922b 100%);
}

.progress-bar.risk-high {
  background: linear-gradient(90deg, #dc3545 0%, #e55353 100%);
}

.interpretation {
  background: white;
  padding: 25px;
  border-radius: 10px;
  margin-bottom: 25px;
  border-left: 4px solid #4e73df;
}

.interpretation h4 {
  color: #4e73df;
  margin-bottom: 15px;
  font-size: 18px;
}

.interpretation p {
  color: #4e4e4e;
  line-height: 1.6;
  white-space: pre-line;
}

.result-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.primary-btn, .secondary-btn, .info-btn {
  padding: 12px 25px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.primary-btn {
  background: linear-gradient(to right, #4e73df, #3a5bbd);
  color: white;
  box-shadow: 0 4px 10px rgba(71, 118, 230, 0.2);
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(71, 118, 230, 0.3);
}

.secondary-btn {
  background: #6c757d;
  color: white;
}

.secondary-btn:hover {
  background: #5a6268;
  transform: translateY(-2px);
}

.info-btn {
  background: #36b9cc;
  color: white;
}

.info-btn:hover {
  background: #2c9faf;
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .age-cards {
    grid-template-columns: 1fr;
    max-width: 500px;
  }

  .question-options {
    grid-template-columns: repeat(2, 1fr);
  }

  .score-summary {
    grid-template-columns: repeat(2, 1fr);
  }

  .score-value {
    font-size: 20px;
  }

  .current-score {
    font-size: 24px;
  }

  .score-separator {
    font-size: 18px;
  }

  .max-score {
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .main-area {
    padding: 15px;
  }

  .age-selection-content {
    padding: 20px;
  }

  .scale-content {
    padding: 15px;
  }

  .question-options {
    grid-template-columns: 1fr;
  }

  .score-summary {
    grid-template-columns: 1fr;
  }

  .score-value {
    font-size: 18px;
    flex-direction: column;
    gap: 2px;
  }

  .current-score {
    font-size: 22px;
  }

  .score-separator {
    font-size: 16px;
  }

  .max-score {
    font-size: 16px;
  }

  .score-percentage {
    font-size: 11px;
    padding: 3px 6px;
  }

  .result-actions {
    flex-direction: column;
    align-items: center;
  }

  .area-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .category-section {
    margin-bottom: 20px;
  }

  .scale-cards {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .scale-card {
    padding: 15px;
  }
}

/* 量表选择页面样式 */
.scale-selection-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 30px;
}

.scale-selection-content h2 {
  text-align: center;
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 28px;
  font-weight: 600;
}

.scale-selection-content .subtitle {
  text-align: center;
  color: #7f8c8d;
  margin-bottom: 40px;
  font-size: 16px;
  line-height: 1.6;
}

.category-section {
  margin-bottom: 40px;
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  border-left: 4px solid #3498db;
}

.category-section h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-section h3 i {
  color: #3498db;
  font-size: 22px;
}

.scale-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.scale-card {
  background: white;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.scale-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  border-color: #3498db;
}

.scale-icon {
  text-align: center;
  margin-bottom: 15px;
}

.scale-icon i {
  font-size: 32px;
  color: #3498db;
}

.scale-card h4 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 16px;
  font-weight: 600;
  text-align: center;
}

/* AI状态指示器样式 */
.ai-status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  margin-left: auto;
}

.ai-analyzing {
  color: #f39c12;
  font-weight: 500;
}

.ai-completed {
  color: #27ae60;
  font-weight: 500;
}

.ai-available {
  color: #3498db;
  font-weight: 500;
}

.ai-unavailable {
  color: #95a5a6;
  font-weight: 500;
}

.analyzing-hint {
  color: #f39c12;
  font-size: 14px;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-status-hint {
  font-size: 12px;
  margin-top: 8px;
  opacity: 0.8;
}

.ai-ready {
  color: #27ae60;
}

.ai-fallback {
  color: #95a5a6;
}

.result-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.scale-card p {
  color: #7f8c8d;
  font-size: 14px;
  text-align: center;
  margin: 0;
}

/* 患者端结果显示样式优化 */
.interpretation {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  border-left: 4px solid #007bff;
}

.interpretation h4 {
  color: #495057;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.interpretation p {
  line-height: 1.6;
  color: #495057;
  white-space: pre-wrap;
}

/* 优化后的结果解读样式 */
.interpretation-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.score-summary, .risk-assessment, .detailed-interpretation, .completion-info {
  background: white;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.score-summary h5, .risk-assessment h5, .detailed-interpretation h5, .completion-info h5 {
  color: #495057;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
}

.score-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.score-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
}

.score-item:last-child {
  border-bottom: none;
}

.score-label {
  font-weight: 500;
  color: #6c757d;
  min-width: 100px;
}

.score-value {
  font-weight: 600;
  color: #495057;
  font-size: 16px;
}

.score-percentage {
  color: #6c757d;
  font-size: 14px;
}

.risk-level-display {
  display: flex;
  align-items: center;
  gap: 12px;
}

.risk-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-weight: 600;
  font-size: 14px;
}

.risk-badge.risk-high {
  background: #dc3545;
  color: white;
}

.risk-badge.risk-medium {
  background: #ffc107;
  color: #212529;
}

.risk-badge.risk-low {
  background: #28a745;
  color: white;
}

.risk-badge.risk-normal {
  background: #6c757d;
  color: white;
}

.risk-description {
  color: #6c757d;
  font-size: 14px;
}

.completion-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.completion-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #6c757d;
  font-size: 14px;
}

.completion-item i {
  width: 16px;
  color: #007bff;
}

/* 详细信息收集弹窗样式 */
.detail-info-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.detail-info-dialog {
  background: white;
  border-radius: 15px;
  padding: 0;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.dialog-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  border-radius: 15px 15px 0 0;
  text-align: center;
}

.dialog-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.dialog-content {
  padding: 25px;
  line-height: 1.6;
}

.dialog-content p {
  margin-bottom: 15px;
  color: #555;
}

.dialog-content ul {
  margin: 15px 0;
  padding-left: 20px;
}

.dialog-content li {
  margin-bottom: 8px;
  color: #666;
}

.detail-info-form {
  margin-top: 20px;
  border-top: 1px solid #eee;
  padding-top: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-group textarea {
  min-height: 80px;
  resize: vertical;
}

.dialog-footer {
  padding: 20px 25px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  gap: 15px;
}

.btn-skip {
  padding: 12px 24px;
  background: #f8f9fa;
  color: #6c757d;
  border: 2px solid #dee2e6;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-skip:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.btn-primary {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
}

/* 新量表样式 */
.cbcl-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #667eea;
}

.section-title {
  color: #667eea;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e1e5e9;
}

.age-group-selection {
  margin-bottom: 30px;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  border: 2px solid #e1e5e9;
}

.age-group-selection h5 {
  color: #333;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
}

.age-group-options {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.age-group-label {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  background: #f8f9fa;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.age-group-label:hover {
  background: #e9ecef;
  border-color: #667eea;
}

.age-group-label input[type="radio"] {
  margin-right: 8px;
}

.age-group-label input[type="radio"]:checked + span {
  color: #667eea;
  font-weight: 600;
}

.age-group-questions {
  margin-top: 20px;
}

.age-group-questions .section-title {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 15px 20px;
  border-radius: 8px;
  border: none;
  margin-bottom: 20px;
}
</style>
