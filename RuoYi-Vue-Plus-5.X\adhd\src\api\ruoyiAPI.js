/**
 * RuoYi后端API统一服务 - 使用Axios重构
 */

import { request, formatters } from './axios.js'

// 用户注册API
export async function registerUser(userData) {
  console.log('👤 注册用户到RuoYi后端...')
  console.log('📤 原始注册数据:', userData)

  // 使用formatters确保数据类型正确，匹配SysPatient实体类
  const formattedData = {
    userId: formatters.ensureString(userData.userId || userData.username || 'user_' + Date.now()), // 🔥 必需字段
    patientName: formatters.ensureString(userData.username || userData.patientName), // 患者姓名
    age: formatters.ensureNumber(userData.age, null),
    gender: userData.gender === '男' ? '0' : userData.gender === '女' ? '1' : '2', // 转换为数字格式
    idCard: formatters.ensureString(userData.idCard), // 🔥 身份证字段
    phoneNumber: formatters.ensureString(userData.phone || userData.phoneNumber), // 手机号码
    address: formatters.ensureString(userData.address),
    emergencyContact: formatters.ensureString(userData.emergencyContact),
    emergencyPhone: formatters.ensureString(userData.emergencyPhone),
    status: '0' // 默认状态为正常
    // 其他字段由后端自动生成或处理
  }

  console.log('📤 格式化后的注册数据:', formattedData)

  try {
    const result = await request.post('/system/patient/register', formattedData)
    console.log('📥 注册响应结果:', result)
    return result
  } catch (error) {
    console.error('❌ 注册请求异常:', error)
    return {
      success: false,
      message: error.message || '注册失败',
      error
    }
  }
}

// 用户登录验证API
export async function validateUser(identifier, password) { // eslint-disable-line no-unused-vars
  console.log('🔐 验证用户登录...')
  console.log('🔍 登录标识符:', identifier)

  try {
    // 查询用户信息列表 - 5.X版本的响应格式
    const result = await request.get('/system/patient/list', {
      pageNum: 1,
      pageSize: 100 // 增加查询数量
    })

    console.log('📥 患者列表查询结果:', result)

    // 处理axios响应拦截器包装后的数据
    let users = []
    if (result && result.success) {
      // axios拦截器包装后的格式
      const responseData = result.data
      if (responseData && responseData.rows) {
        users = responseData.rows
      } else if (Array.isArray(responseData)) {
        users = responseData
      }
    } else if (result && result.rows) {
      // 直接的RuoYi响应格式
      users = result.rows
    }

    console.log('👥 找到患者数量:', users.length)

    if (users.length === 0) {
      console.log('⚠️ 患者列表为空，可能是新系统或数据未初始化')
      return {
        success: false,
        message: '系统中暂无患者数据，请先注册',
        isEmpty: true
      }
    }

    // 查找匹配的用户
    const user = users.find(u => {
      const matches = (
        u.phoneNumber === identifier ||
        u.userId === identifier ||
        u.patientName === identifier ||
        u.patientId?.toString() === identifier
      )
      const isActive = u.status === '0' || u.status === 0 // 正常状态

      console.log('🔍 检查用户:', {
        patientName: u.patientName,
        phoneNumber: u.phoneNumber,
        userId: u.userId,
        patientId: u.patientId,
        status: u.status,
        matches,
        isActive
      })

      return matches && isActive
    })

    if (user) {
      console.log('✅ 找到匹配用户:', user.patientName)
      // 简化验证：患者端暂时不验证密码，只验证用户存在且状态正常
      return {
        success: true,
        user: user,
        message: '登录成功'
      }
    } else {
      console.log('❌ 未找到匹配的用户')
      return {
        success: false,
        message: '用户不存在或已被禁用，请检查登录信息'
      }
    }
  } catch (error) {
    console.error('❌ 登录验证异常:', error)
    return {
      success: false,
      message: error.message || '登录验证失败，请检查网络连接'
    }
  }
}

// 提交量表数据API
export async function submitScaleData(scaleData) {
  console.log('📊 提交量表数据到RuoYi后端...')
  console.log('📤 原始量表数据:', scaleData)

  // 使用formatters确保数据类型和格式正确，匹配ScaleTestRecords实体类
  const formattedData = {
    userId: formatters.ensureString(scaleData.userId),
    username: formatters.ensureString(scaleData.username),
    age: formatters.ensureNumber(scaleData.age, null),
    gender: scaleData.gender === '男' ? '0' : scaleData.gender === '女' ? '1' : '2', // 🔥 格式化性别字段为数字
    scaleName: formatters.ensureString(scaleData.scaleName),

    // 🔥 分项分数 - 使用更明确的字段名
    psychologicalScore: formatters.ensureNumber(scaleData.attentionScore || scaleData.psychologicalScore, 0), // 注意力缺陷分数
    behavioralScore: formatters.ensureNumber(scaleData.hyperactivityScore || scaleData.behavioralScore, 0),   // 多动/冲动分数
    totalScore: formatters.ensureNumber(scaleData.totalScore, 0), // 总分

    // 🔥 新增：AI分析相关字段
    analysisMethod: formatters.ensureString(scaleData.analysisMethod || 'traditional'), // AI分析 或 传统分析
    aiConfidence: formatters.ensureNumber(scaleData.aiConfidence, null), // AI分析置信度
    severityLevel: formatters.ensureString(scaleData.severityLevel), // 严重程度

    // 时间和风险信息
    startTime: formatters.formatDateForISO(scaleData.startTime),
    endTime: formatters.formatDateForISO(scaleData.endTime),
    duration: formatters.ensureNumber(scaleData.duration, 0),
    riskLevel: formatters.formatRiskLevel(scaleData.riskLevel), // 🔥 格式化风险等级字段

    // 🔥 新增：详细分析结果（JSON格式存储）
    analysisDetails: scaleData.analysisDetails || (scaleData.aiAnalysisResult ? JSON.stringify({
      keyFindings: scaleData.aiAnalysisResult.keyFindings || [],
      recommendations: scaleData.aiAnalysisResult.recommendations || [],
      warningSignals: scaleData.aiAnalysisResult.warningSignals || [],
      nextSteps: scaleData.aiAnalysisResult.nextSteps || [],
      interpretation: scaleData.aiAnalysisResult.interpretation || '',
      modelUsed: scaleData.aiAnalysisResult.modelUsed || 'qwen2.5:14b'
    }) : null),

    // 🔥 新增：专业医疗建议（医生端使用）
    professionalAdvice: formatters.ensureString(scaleData.professionalAdvice),

    // 🔥 新增：使用的模型信息
    modelUsed: formatters.ensureString(scaleData.modelUsed)

    // createdAt由后端自动生成
  }

  console.log('📤 格式化后的量表数据:', formattedData)

  try {
    const result = await request.post('/system/scaleRecords/submit', formattedData)
    console.log('📥 量表提交响应结果:', result)
    return result
  } catch (error) {
    console.error('❌ 量表提交异常:', error)
    return {
      success: false,
      message: error.message || '量表提交失败',
      error
    }
  }
}

// 提交检测会话数据API
export async function submitDetectionSession(sessionData) {
  console.log('🔍 提交检测会话数据到RuoYi后端...')
  console.log('📤 原始会话数据:', sessionData)

  // 使用formatters确保数据类型和格式正确，匹配detection_sessions表结构
  const formattedData = {
    userId: formatters.ensureString(sessionData.userId),
    username: formatters.ensureString(sessionData.username),
    sessionId: formatters.ensureString(sessionData.sessionId),
    startTime: formatters.formatDateForRuoYi(sessionData.startTime),
    endTime: formatters.formatDateForRuoYi(sessionData.endTime),
    duration: formatters.ensureNumber(sessionData.duration, 0),

    // 必须包含avg_attention_level字段
    avgAttentionLevel: formatters.ensureNumber(sessionData.avgAttentionLevel, 0),
    avgAttentionScore: formatters.ensureNumber(sessionData.avgAttentionScore, 0),

    // 注意力等级计数 (level_0_count到level_3_count)
    level0Count: formatters.ensureNumber(sessionData.focusedCount, 0),      // 专注
    level1Count: formatters.ensureNumber(sessionData.normalCount, 0),       // 正常
    level2Count: formatters.ensureNumber(sessionData.distractedCount, 0),   // 分心
    level3Count: formatters.ensureNumber(sessionData.daydreamingCount, 0),  // 发呆

    totalDetections: formatters.ensureNumber(sessionData.totalDetections, 0),
    maxAttentionScore: formatters.ensureNumber(sessionData.maxAttentionScore, 0),
    minAttentionScore: formatters.ensureNumber(sessionData.minAttentionScore, 0)
    // createdAt由后端自动生成
  }

  console.log('📤 格式化后的会话数据:', formattedData)

  try {
    const result = await request.post('/system/sessions/sync', formattedData)
    console.log('📥 会话提交响应结果:', result)
    return result
  } catch (error) {
    console.error('❌ 会话提交异常:', error)
    return {
      success: false,
      message: error.message || '会话提交失败',
      error
    }
  }
}

// 获取用户信息API
export async function getUserInfo(userId) {
  console.log('👤 获取用户信息...')

  try {
    const result = await request.get(`/system/patient/${userId}`)
    console.log('📥 用户信息响应结果:', result)
    return result
  } catch (error) {
    console.error('❌ 获取用户信息异常:', error)
    return {
      success: false,
      message: error.message || '获取用户信息失败',
      error
    }
  }
}

// 更新用户信息API
export async function updateUserInfo(userId, userData) {
  console.log('👤 更新用户信息...');
  console.log('📤 更新数据:', userData);

  const formattedData = {
    patientId: Number(userId), // 使用patientId作为主键
    userId: String(userData.userId || userId),
    patientName: String(userData.username || userData.patientName || ''),
    age: userData.age ? Number(userData.age) : null,
    gender: userData.gender === '男' ? '0' : userData.gender === '女' ? '1' : '2',
    phoneNumber: String(userData.phone || userData.phoneNumber || ''),
    address: String(userData.address || ''),
    emergencyContact: String(userData.emergencyContact || ''),
    emergencyPhone: String(userData.emergencyPhone || '')
  };

  console.log('📤 格式化后的更新数据:', formattedData);

  const result = await request.put(`/system/patient/${userId}`, formattedData);

  console.log('📥 更新响应结果:', result);
  return result;
}

// 修改密码API
export async function updateUserPassword(userId, passwordData) {
  console.log('🔐 修改用户密码...')
  console.log('📤 密码修改数据:', {
    userId,
    hasCurrentPassword: !!passwordData.currentPassword,
    hasNewPassword: !!passwordData.newPassword
  })

  try {
    // 首先验证当前密码
    const userResult = await getUserInfo(userId)
    if (!userResult.success) {
      return {
        success: false,
        message: '获取用户信息失败'
      }
    }

    // 验证当前密码（这里简化处理，实际应该在后端验证）
    if (userResult.data && userResult.data.password !== passwordData.currentPassword) {
      return {
        success: false,
        message: '当前密码错误'
      }
    }

    // 更新密码
    const formattedData = {
      userId: formatters.ensureString(userId),
      password: formatters.ensureString(passwordData.newPassword)
    }

    console.log('📤 格式化后的密码数据:', {
      userId: formattedData.userId,
      hasPassword: !!formattedData.password
    })

    const result = await request.post('/system/info/updatePassword', formattedData)
    console.log('📥 密码修改响应结果:', result)
    return result

  } catch (error) {
    console.error('❌ 密码修改异常:', error)
    return {
      success: false,
      message: error.message || '密码修改失败',
      error
    }
  }
}

// 检查服务器连接状态
export async function checkServerStatus() {
  try {
    const result = await request.get('/actuator/health')
    return {
      success: result.success,
      status: result.code || 200,
      message: result.success ? '服务器连接正常' : '服务器连接异常',
      data: result.data
    }
  } catch (error) {
    console.error('❌ 服务器状态检查异常:', error)
    return {
      success: false,
      status: 0,
      message: '无法连接到服务器',
      error
    }
  }
}

// 本地存储工具
export const localStorageUtils = {
  // 保存用户信息
  saveUser(userInfo, remember = false) {
    const storage = remember ? localStorage : sessionStorage;
    storage.setItem('currentUser', JSON.stringify(userInfo));
  },

  // 获取用户信息
  getUser() {
    const userStr = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
    return userStr ? JSON.parse(userStr) : null;
  },

  // 清除用户信息
  clearUser() {
    localStorage.removeItem('currentUser');
    sessionStorage.removeItem('currentUser');
  },

  // 检查登录状态
  isLoggedIn() {
    return !!(localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser'));
  },

  // 保存注册用户列表（本地备份）
  saveRegisteredUsers(users) {
    localStorage.setItem('registeredUsers', JSON.stringify(users));
  },

  // 获取注册用户列表
  getRegisteredUsers() {
    return JSON.parse(localStorage.getItem('registeredUsers') || '[]');
  }
};

// 纯后端登录模式
export const backendAuth = {
  async login(identifier, password, remember = false) {
    console.log('🔄 后端登录模式...');
    console.log('🔍 登录信息:', { identifier, hasPassword: !!password });

    // 只使用后端验证
    const backendResult = await validateUser(identifier, password);

    if (backendResult.success) {
      console.log('✅ 后端验证成功');
      localStorageUtils.saveUser(backendResult.user, remember);
      return {
        success: true,
        user: backendResult.user,
        message: '登录成功'
      };
    }

    // 如果是因为数据库为空导致的失败，提供特殊处理
    if (backendResult.isEmpty) {
      console.log('📝 数据库为空，建议用户注册');
      return {
        success: false,
        message: '系统中暂无用户数据，请先注册账号',
        suggestRegister: true
      };
    }

    console.log('❌ 后端验证失败:', backendResult.message);
    return {
      success: false,
      message: backendResult.message || '登录失败，请检查用户名和密码'
    };
  },

  async register(userData) {
    console.log('🔄 混合注册模式：优先后端注册...');
    console.log('📤 注册用户数据:', userData);

    // 首先尝试后端注册
    const backendResult = await registerUser(userData);

    if (backendResult.success) {
      console.log('✅ 后端注册成功:', backendResult);

      // 从响应中获取用户ID
      let userId = null;
      if (backendResult.data && backendResult.data.data) {
        // 如果返回的是插入成功的行数，生成用户ID
        const localUsers = localStorageUtils.getRegisteredUsers();
        userId = (1000000 + localUsers.length).toString();
      } else if (backendResult.data && backendResult.data.userId) {
        userId = backendResult.data.userId;
      } else {
        // 生成默认用户ID
        const localUsers = localStorageUtils.getRegisteredUsers();
        userId = (1000000 + localUsers.length).toString();
      }

      // 同时保存到本地作为备份
      const localUsers = localStorageUtils.getRegisteredUsers();
      const newUser = {
        ...userData,
        userId: userId,
        createdAt: new Date().toISOString()
      };
      localUsers.push(newUser);
      localStorageUtils.saveRegisteredUsers(localUsers);

      return {
        success: true,
        userId: userId,
        message: '注册成功'
      };
    }
    
    console.log('⚠️ 后端注册失败，使用本地注册...');
    
    // 后端失败时使用本地存储
    const localUsers = localStorageUtils.getRegisteredUsers();
    
    // 检查重复
    const idCardExists = localUsers.some(u => u.idCard === userData.idCard);
    const phoneExists = localUsers.some(u => u.phone === userData.phone);
    
    if (idCardExists) {
      return { success: false, message: '身份证号已被注册' };
    }
    
    if (phoneExists) {
      return { success: false, message: '手机号已被注册' };
    }
    
    // 创建新用户
    const userId = (1000000 + localUsers.length).toString();
    const newUser = {
      ...userData,
      userId: userId,
      createdAt: new Date().toISOString()
    };
    
    localUsers.push(newUser);
    localStorageUtils.saveRegisteredUsers(localUsers);
    
    console.log('✅ 本地注册成功');
    
    return {
      success: true,
      userId: userId,
      message: '注册成功（本地模式）'
    };
  }
};

// 获取AI分析结果
export async function getAIAnalysis(recordId) {
  console.log('🤖 获取AI分析结果...')
  console.log('📋 记录ID:', recordId)

  try {
    const response = await request.get(`/system/scaleRecords/${recordId}`)
    console.log('📥 AI分析响应:', response)

    if (response.code === 200 && response.data) {
      return {
        success: true,
        data: {
          professionalAdvice: response.data.professionalAdvice,
          aiConfidence: response.data.aiConfidence,
          analysisMethod: response.data.analysisMethod,
          modelUsed: response.data.modelUsed,
          analysisDetails: response.data.analysisDetails,
          severityLevel: response.data.severityLevel
        }
      }
    } else {
      return {
        success: false,
        message: response.msg || '获取AI分析失败'
      }
    }
  } catch (error) {
    console.error('❌ 获取AI分析失败:', error)
    return {
      success: false,
      message: error.message || '获取AI分析失败',
      error: error
    }
  }
}

// 轮询检查AI分析状态
export async function pollAIAnalysisStatus(recordId, maxAttempts = 15, interval = 3000) {
  console.log('🔄 开始轮询AI分析状态...')
  console.log(`📋 记录ID: ${recordId}, 最大尝试次数: ${maxAttempts}, 间隔: ${interval}ms`)

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    console.log(`🔍 第${attempt}次检查AI分析状态...`)

    const result = await getAIAnalysis(recordId)

    if (result.success && result.data.professionalAdvice) {
      console.log('✅ AI分析完成!')
      return result
    }

    if (attempt < maxAttempts) {
      console.log(`⏳ AI分析进行中，${interval/1000}秒后重试...`)
      await new Promise(resolve => setTimeout(resolve, interval))
    }
  }

  console.log('⏰ AI分析超时')
  return {
    success: false,
    message: 'AI分析超时，请稍后在个人中心查看结果'
  }
}

// 优化的AI分析提交方法
export async function submitScaleWithAIAnalysis(scaleData) {
  console.log('🚀 提交量表并请求AI分析...')

  try {
    // 1. 先提交量表数据
    const submitResult = await submitScaleData(scaleData)

    if (!submitResult.success) {
      throw new Error(submitResult.message || '量表提交失败')
    }

    console.log('✅ 量表提交成功，记录ID:', submitResult.data?.id)

    // 2. 如果有记录ID，开始轮询AI分析结果
    if (submitResult.data?.id) {
      console.log('🤖 开始等待AI分析结果...')

      // 异步轮询，不阻塞用户界面
      const aiResult = await pollAIAnalysisStatus(submitResult.data.id)

      return {
        success: true,
        message: '量表提交成功',
        data: {
          ...submitResult.data,
          aiAnalysis: aiResult.success ? aiResult.data : null
        },
        aiAnalysisStatus: aiResult.success ? 'completed' : 'pending'
      }
    } else {
      return {
        success: true,
        message: '量表提交成功，AI分析将在后台进行',
        data: submitResult.data,
        aiAnalysisStatus: 'pending'
      }
    }
  } catch (error) {
    console.error('❌ 提交量表或AI分析失败:', error)
    return {
      success: false,
      message: error.message || '提交失败',
      error: error
    }
  }
}

// formatters已在上面导出，localStorageUtils已在上面导出，无需重复导出
