package org.dromara.patient.service;

import org.dromara.patient.domain.ScaleTestRecords;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;

/**
 * ADHD量表测试记录服务接口
 *
 * <AUTHOR> System
 * @date 2025-07-19
 */
public interface IScaleTestRecordsService {

    /**
     * 查询ADHD量表测试记录列表
     *
     * @param scaleTestRecords ADHD量表测试记录
     * @param pageQuery        分页查询
     * @return ADHD量表测试记录集合
     */
    TableDataInfo<ScaleTestRecords> queryPageList(ScaleTestRecords scaleTestRecords, PageQuery pageQuery);

    /**
     * 查询ADHD量表测试记录列表
     *
     * @param scaleTestRecords ADHD量表测试记录
     * @return ADHD量表测试记录集合
     */
    List<ScaleTestRecords> queryList(ScaleTestRecords scaleTestRecords);

    /**
     * 根据主键查询ADHD量表测试记录
     *
     * @param id 主键
     * @return ADHD量表测试记录
     */
    ScaleTestRecords queryById(Long id);

    /**
     * 新增ADHD量表测试记录
     *
     * @param scaleTestRecords ADHD量表测试记录
     * @return 结果
     */
    Boolean insertByBo(ScaleTestRecords scaleTestRecords);

    /**
     * 修改ADHD量表测试记录
     *
     * @param scaleTestRecords ADHD量表测试记录
     * @return 结果
     */
    Boolean updateByBo(ScaleTestRecords scaleTestRecords);

    /**
     * 校验并批量删除ADHD量表测试记录信息
     *
     * @param ids 主键集合
     * @return 结果
     */
    Boolean deleteWithValidByIds(List<Long> ids);

}
