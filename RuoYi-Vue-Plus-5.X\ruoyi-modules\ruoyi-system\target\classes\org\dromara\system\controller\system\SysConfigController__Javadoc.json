{"doc": " 参数配置 信息操作处理\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取参数配置列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出参数配置列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 根据参数编号获取详细信息\n\n @param configId 参数ID\n"}, {"name": "getConfigKey", "paramTypes": ["java.lang.String"], "doc": " 根据参数键名查询参数值\n\n @param configKey 参数Key\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": " 新增参数配置\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": " 修改参数配置\n"}, {"name": "updateByKey", "paramTypes": ["org.dromara.system.domain.bo.SysConfigBo"], "doc": " 根据参数键名修改参数配置\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除参数配置\n\n @param configIds 参数ID串\n"}, {"name": "refreshCache", "paramTypes": [], "doc": " 刷新参数缓存\n"}], "constructors": []}