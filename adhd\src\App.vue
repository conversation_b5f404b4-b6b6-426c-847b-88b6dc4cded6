<template>
  <div id="app">
    <router-view />
    <!-- 全局后台监控指示器 -->
    <BackgroundMonitorIndicator />
  </div>
</template>

<script>
import BackgroundMonitorIndicator from '@/components/common/BackgroundMonitorIndicator.vue'

export default {
  name: 'App',
  components: {
    BackgroundMonitorIndicator
  },
  mounted() {
    // 应用启动时的初始化逻辑
    this.initializeApp();
  },
  methods: {
    initializeApp() {
      // 检查用户登录状态
      this.checkAuthStatus();

      // 设置全局错误处理
      this.setupGlobalErrorHandling();

      // 初始化主题
      this.initializeTheme();
    },

    checkAuthStatus() {
      const token = localStorage.getItem('userToken') || sessionStorage.getItem('userToken');
      if (token) {
        // 验证token有效性
        this.validateToken(token);
      }
    },

    async validateToken(token) { // eslint-disable-line no-unused-vars
      try {
        // 这里可以调用API验证token
        // const response = await authAPI.validateToken(token);
        console.log('Token validation would happen here');
      } catch (error) {
        console.error('Token validation failed:', error);
        // 清除无效token
        localStorage.removeItem('userToken');
        sessionStorage.removeItem('userToken');
      }
    },

    setupGlobalErrorHandling() {
      // 全局错误处理
      window.addEventListener('error', (event) => {
        console.error('Global error:', event.error);
      });

      // 未处理的Promise拒绝
      window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled promise rejection:', event.reason);
      });
    },

    initializeTheme() {
      // 从本地存储获取主题设置
      const savedTheme = localStorage.getItem('theme');
      if (savedTheme) {
        document.documentElement.setAttribute('data-theme', savedTheme);
      }
    }
  }
}
</script>

<style>
#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: Avenir, Helvetica, Arial, sans-serif;
}
</style>
