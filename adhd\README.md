# ADHD辅助与筛查系统

## 📋 项目概述

本系统是一个专门针对ADHD（注意力缺陷多动障碍）的综合性辅助诊断与治疗平台。系统采用先进的人工智能技术，结合传统的心理学评估方法，为ADHD的早期筛查、诊断辅助和康复训练提供全方位的解决方案。

整个系统致力于对于ADHD的筛查与辅助治疗，通过眼动识别与量表筛查二者结合实现对于ADHD的筛查，整个系统分为**智能筛查**，**康复计划**，**在线咨询**，**注意力游戏**，**知识课程**，**数据分析**，**量表筛查**七大模块。

### 🎯 系统特色
- **非侵入性检测** - 基于摄像头的实时眼动追踪和面部表情分析
- **多维度评估** - 结合AI智能分析和标准化心理量表
- **个性化服务** - 根据用户特征提供定制化康复方案
- **数据驱动** - 持续数据收集与分析，优化评估准确性
- **全流程覆盖** - 从筛查到康复的完整服务链条

## 🚀 七大核心功能模块

### 1. 智能筛查模块 (Home.vue)
采用基于MediaPipe的实时眼动追踪技术，通过摄像头捕捉用户的面部表情和眼部运动数据。ADHD的一个显著特征就是注意力不集中，很难对一个事情保持长时间的专注。
- **实时AI检测** - 眨眼频率、注视稳定性、瞳孔运动模式分析
- **注意力评估** - 多维度注意力集中程度评估
- **即时反馈** - 实时显示检测结果和注意力状态
- **历史记录** - 检测数据自动保存和历史回顾

### 2. 康复计划模块 (AI-intelligent-rehabilitation.vue)
基于用户筛查结果和个人特征，提供个性化的ADHD康复训练方案。
- **个性化方案** - 根据评估结果定制康复计划
- **多维度训练** - 认知训练、行为干预、生活方式调整
- **进度跟踪** - 阶段性进度监控和效果评估
- **动态优化** - 根据训练表现调整方案

### 3. 在线咨询模块 (OnlineConsultation.vue)
为用户提供便捷的专业咨询服务平台。
- **专家预约** - 专业心理咨询师和ADHD专家预约
- **多种沟通** - 视频通话、文字交流等方式
- **记录管理** - 咨询记录保存和回顾
- **个性化建议** - 基于用户数据的精准指导

### 4. 注意力游戏模块 (AttentionGames.vue)
针对ADHD注意力不集中特点设计的训练游戏系统。
- **多维度训练** - 持续注意力、选择性注意力、分配性注意力
- **游戏化设计** - 趣味性强的注意力训练游戏
- **自适应难度** - 根据表现自动调整游戏难度
- **效果追踪** - 游戏表现数据分析和进步跟踪

### 5. 知识课程模块 (Knowledge-dissemination.vue)
提供全面的ADHD相关知识教育和学习资源。
- **专业内容** - 医生和心理学家编写的权威课程
- **多媒体学习** - 文字、图片、视频等多种形式
- **分类管理** - 按主题和难度分类的课程体系
- **学习跟踪** - 学习进度和效果评估

### 6. 数据分析模块 (data-analysis.vue)
系统的智能大脑，负责数据收集、分析和可视化展示。
- **趋势分析** - 注意力变化趋势和症状发展曲线
- **多维对比** - 不同时期和指标的对比分析
- **可视化图表** - 直观的数据图表和报告生成
- **智能洞察** - 基于数据的专业分析和建议

### 7. 量表筛查模块 (ADHDScale.vue)
集成多种国际标准化ADHD评估量表的专业筛查工具。
- **标准化量表** - PHQ-A、CDI-2、SCARED、GAD-7等专业量表
- **多年龄适用** - 覆盖儿童、青少年、成人不同年龄段
- **自动评分** - 智能计算和标准化评分报告
- **结果解读** - 专业的评估结果解释和建议

## 🏗️ 技术架构

### 📐 系统架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                      前端 Vue.js 应用                           │
├─────────────────────────────────────────────────────────────────┤
│  智能筛查 │ 数据分析 │ AI康复计划 │ 在线咨询 │ 知识课程 │ 量表检测 │
└─────────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Node.js Express 服务                         │
├─────────────────────────────────────────────────────────────────┤
│      API路由      │    Python服务管理    │     静态资源托管      │
└─────────────────────────────────────────────────────────────────┘
                                │
                    ┌───────────┼───────────┐
                    ▼           ▼           ▼
        ┌─────────────────┐ ┌─────────────┐ ┌─────────────┐
        │  Python WebRTC  │ │ Ollama模型  │ │ 情绪识别API │
        │   注意力分析    │ │  康复计划   │ │  后端服务   │
        └─────────────────┘ └─────────────┘ └─────────────┘
                    │
                    ▼
        ┌─────────────────────────────────────────────┐
        │     OpenCV + MediaPipe                      │
        │  人脸识别 │ 眼部跟踪 │ 动作捕捉 │ 发呆检测  │
        └─────────────────────────────────────────────┘
```

### 💻 技术栈

#### 前端技术
- **Vue 3** & **Vue Router 4** - 现代化SPA框架与路由管理
- **Chart.js** - 数据可视化图表库
- **Font Awesome** - 图标库
- **Axios** - HTTP请求库
- **CSS3** - 响应式设计与现代样式
- **WebRTC** - 实时音视频通信

#### 后端技术
- **Node.js (Express)** - API服务与Python服务管理
- **Python Flask** - 轻量级Web服务框架
- **OpenCV** - 计算机视觉处理库
- **MediaPipe** - 面部识别与分析框架
- **WebSocket** - 实时数据传输

#### AI与算法
- **Ollama** - 本地大语言模型服务
- **MediaPipe Face Mesh** - 468个面部关键点检测
- **眼动追踪算法** - 基于规则的注意力分析
- **发呆检测算法** - 多维度注意力状态评估
- **情绪识别模型** - 基于面部表情的情绪分析

## 📁 详细项目结构

```
adhd/                                    # 项目根目录
├── public/                              # 静态资源目录
│   ├── index.html                      # 主HTML模板文件，Vue应用挂载点
│   └── favicon.ico                     # 网站图标文件
│
├── src/                                # 前端源代码目录
│   ├── api/                           # API接口封装层
│   │   ├── index.js                   # API统一入口，配置axios基础设置
│   │   └── pythonServerAPI.js         # Python服务器API接口封装
│   │
│   ├── assets/                        # 静态资源文件
│   │   ├── images/                    # 图片资源
│   │   └── styles/                    # 样式文件
│   │
│   ├── components/                    # 公共组件目录
│   │   └── common/                    # 通用组件
│   │       ├── AppSidebar.vue         # 侧边栏导航组件，包含七大模块导航
│   │       ├── AppHeader.vue          # 顶部导航栏组件
│   │       └── LoadingSpinner.vue     # 加载动画组件
│   │
│   ├── router/                        # 路由配置目录
│   │   └── index.js                   # Vue Router配置，定义所有页面路由和权限
│   │
│   ├── services/                      # 业务服务层
│   │   ├── webrtcService.js           # WebRTC通信服务，处理视频流和实时数据传输
│   │   ├── dataService.js             # 数据处理服务，管理本地存储和数据分析
│   │   ├── chartService.js            # Chart.js图表服务，处理数据可视化
│   │   └── authService.js             # 用户认证服务，处理登录和权限验证
│   │
│   ├── views/                         # 页面组件目录（七大核心模块）
│   │   ├── Login.vue                  # 用户登录页面，系统入口
│   │   ├── Home.vue                   # 智能筛查主页，AI检测核心功能
│   │   ├── data-analysis.vue          # 数据分析页面，检测结果可视化展示
│   │   ├── AI-intelligent-rehabilitation.vue  # AI康复计划页面，个性化康复方案
│   │   ├── OnlineConsultation.vue     # 在线咨询页面，专家预约和咨询服务
│   │   ├── Knowledge-dissemination.vue # 知识课程页面，ADHD相关知识学习
│   │   ├── AttentionGames.vue         # 注意力游戏页面，游戏化训练模块
│   │   └── ADHDScale.vue             # 量表筛查页面，标准化心理评估
│   │
│   ├── App.vue                        # Vue根组件，应用程序主框架
│   └── main.js                        # 应用程序入口文件，Vue实例创建和配置
│
├── server/                            # Node.js后端服务目录
│   ├── app.js                         # Express服务器主文件，API路由和中间件配置
│   ├── pythonServerManager.js         # Python服务管理器，负责启动和管理Python进程
│   ├── routes/                        # API路由目录
│   │   ├── auth.js                    # 用户认证相关API路由
│   │   ├── detection.js               # 检测数据相关API路由
│   │   └── analysis.js                # 数据分析相关API路由
│   └── package.json                   # Node.js后端依赖配置文件
│
├── python/                            # Python AI检测服务目录
│   ├── daydream_detector.py           # 核心发呆检测算法，基于MediaPipe的注意力分析
│   ├── demo.py                        # 演示脚本，用于测试AI检测功能
│   ├── run.py                         # Python服务统一启动脚本，WebRTC服务器
│   ├── config.py                      # Python服务配置文件，参数和常量定义
│   ├── models/                        # AI模型目录
│   │   ├── attention_model.py         # 注意力检测模型
│   │   └── emotion_model.py           # 情绪识别模型
│   ├── utils/                         # 工具函数目录
│   │   ├── face_detection.py          # 面部检测工具函数
│   │   ├── eye_tracking.py            # 眼动追踪工具函数
│   │   └── data_processor.py          # 数据处理工具函数
│   └── requirements.txt               # Python依赖包列表文件
│
├── backend/                           # 情绪识别后端服务目录
│   ├── emotion_api/                   # 情绪识别API服务
│   │   ├── app.py                     # Flask应用主文件
│   │   ├── models/                    # 情绪识别模型文件
│   │   └── utils/                     # 工具函数
│   └── requirements.txt               # 情绪识别服务Python依赖
│
├── opencv_face_motion_project/        # OpenCV独立项目目录
│   ├── main.py                        # PyCharm版本的主程序，独立的OpenCV检测系统
│   ├── face_detection.py              # 面部检测模块
│   ├── motion_analysis.py             # 动作分析模块
│   └── config.py                      # OpenCV项目配置文件
│
├── docs/                              # 项目文档目录
│   ├── 后台监控功能说明.md             # 后台监控功能详细说明文档
│   ├── API文档.md                     # API接口文档
│   ├── 部署指南.md                     # 系统部署指南
│   └── 用户手册.md                     # 用户操作手册
│
├── tests/                             # 测试文件目录
│   ├── unit/                          # 单元测试
│   ├── integration/                   # 集成测试
│   └── e2e/                          # 端到端测试
│
├── config/                            # 配置文件目录
│   ├── development.js                 # 开发环境配置
│   ├── production.js                  # 生产环境配置
│   └── database.js                    # 数据库配置
│
├── scripts/                           # 脚本文件目录
│   ├── build.js                       # 构建脚本
│   ├── deploy.js                      # 部署脚本
│   └── setup.js                       # 环境设置脚本
│
├── package.json                       # 前端项目配置文件，依赖和脚本定义
├── vue.config.js                      # Vue CLI配置文件，webpack和开发服务器配置
├── start_all.js                       # 项目统一启动脚本，一键启动所有服务
├── PYTHON_SERVER_SETUP.md             # Python服务器设置说明文档
└── README.md                          # 项目说明文档（本文件）
```

## 🔧 核心文件详细说明

### 前端核心文件

#### 📄 src/views/Home.vue - 智能筛查主页
- **功能**: 系统核心模块，实现AI注意力检测
- **技术**: WebRTC视频流处理，MediaPipe面部识别
- **特性**: 实时检测、状态显示、历史记录、摄像头权限管理
- **数据流**: 摄像头 → WebRTC → Python AI → 结果显示

#### 📄 src/views/data-analysis.vue - 数据分析页面
- **功能**: 检测数据可视化分析和统计展示
- **技术**: Chart.js图表库，响应式数据绑定
- **特性**: 趋势图表、统计卡片、历史数据对比、导出功能
- **数据源**: LocalStorage本地存储的检测历史数据

#### 📄 src/services/webrtcService.js - WebRTC通信服务
- **功能**: 处理前端与Python服务器的实时通信
- **技术**: WebRTC协议，WebSocket连接
- **特性**: 视频流传输、数据双向通信、连接状态管理
- **接口**: startAnalysis(), stopAnalysis(), getConnectionStatus()

#### 📄 src/services/dataService.js - 数据处理服务
- **功能**: 管理检测数据的存储、检索和分析
- **技术**: LocalStorage API，数据序列化
- **特性**: 数据持久化、历史记录管理、统计计算
- **方法**: saveDetectionData(), getHistoryData(), calculateStats()

### 后端核心文件

#### 📄 server/app.js - Express服务器主文件
- **功能**: Node.js后端API服务器，处理前端请求
- **技术**: Express.js框架，中间件配置
- **特性**: API路由管理、Python服务控制、静态资源托管
- **端口**: 默认3000端口，提供RESTful API接口

#### 📄 server/pythonServerManager.js - Python服务管理器
- **功能**: 管理Python AI检测服务的启动、停止和监控
- **技术**: Node.js子进程管理，进程通信
- **特性**: 自动启动Python服务、进程状态监控、错误处理
- **方法**: startPythonServer(), stopPythonServer(), checkStatus()

#### 📄 python/daydream_detector.py - 核心AI检测算法
- **功能**: 基于MediaPipe的注意力和发呆状态检测
- **技术**: OpenCV图像处理，MediaPipe面部网格
- **算法**: 468个面部关键点分析，眼部运动追踪，注意力评估
- **输出**: 实时注意力状态、专注度评分、检测置信度

#### 📄 python/run.py - Python服务启动脚本
- **功能**: 统一的Python WebRTC服务器启动入口
- **技术**: Flask-SocketIO，WebRTC信令服务器
- **特性**: 多线程处理、实时数据传输、错误恢复
- **端口**: 默认5000端口，提供WebRTC信令和数据服务

### 配置文件

#### 📄 package.json - 前端项目配置
- **功能**: 定义前端依赖包、脚本命令和项目元信息
- **依赖**: Vue 3, Chart.js, Axios, Vue Router等
- **脚本**: serve(开发), build(构建), lint(代码检查)

#### 📄 vue.config.js - Vue CLI配置
- **功能**: Vue项目构建和开发服务器配置
- **配置**: Webpack设置、代理配置、构建优化
- **特性**: 开发服务器代理、生产环境优化

#### 📄 python/requirements.txt - Python依赖
- **功能**: 定义Python服务所需的依赖包
- **主要依赖**: opencv-python, mediapipe, flask, numpy
- **安装**: pip install -r requirements.txt

#### 📄 start_all.js - 统一启动脚本
- **功能**: 一键启动前端、后端和Python服务
- **特性**: 并发启动、状态检查、错误处理
- **使用**: node start_all.js

## 🛠️ 环境要求

### 系统要求
- **操作系统**: Windows 10/11, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: 版本 14.0 或更高
- **Python**: 版本 3.8 或更高
- **摄像头**: 支持WebRTC的摄像头设备
- **浏览器**: Chrome 88+, Firefox 85+, Safari 14+
- **内存**: 最低4GB RAM，推荐8GB+
- **存储**: 至少2GB可用空间

### 依赖环境
- **前端框架**: Vue 3.x, Vue Router 4.x
- **图表库**: Chart.js 3.x
- **HTTP客户端**: Axios
- **后端框架**: Express.js, Flask
- **AI库**: OpenCV 4.x, MediaPipe 0.8+
- **数据处理**: NumPy, Pandas
- **实时通信**: WebRTC, Socket.IO

## 🚀 快速开始

### 1. 环境准备
```bash
# 检查Node.js版本
node --version  # 应该 >= 14.0

# 检查Python版本
python --version  # 应该 >= 3.8

# 检查npm版本
npm --version
```

### 2. 克隆项目
```bash
git clone [项目地址]
cd adhd
```

### 3. 安装前端依赖
```bash
# 安装Vue.js项目依赖
npm install

# 验证安装
npm list vue
```

### 4. 安装后端依赖
```bash
# 进入后端目录
cd server

# 安装Node.js后端依赖
npm install

# 返回项目根目录
cd ..
```

### 5. 安装Python依赖
```bash
# 进入Python服务目录
cd python

# 创建虚拟环境（推荐）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# 安装Python依赖
pip install -r requirements.txt

# 验证关键依赖
python -c "import cv2, mediapipe; print('依赖安装成功')"

# 返回项目根目录
cd ..
```

### 6. 启动项目

#### 方式一：使用统一启动脚本（推荐）
```bash
# 一键启动所有服务
node start_all.js

# 启动后会看到以下输出：
# ✅ 前端服务启动成功: http://localhost:8080
# ✅ 后端API服务启动成功: http://localhost:3000
# ✅ Python AI服务启动成功: http://localhost:5000
```

#### 方式二：手动分别启动
```bash
# 终端1: 启动前端开发服务器
npm run serve

# 终端2: 启动后端API服务器
cd server
node app.js

# 终端3: 启动Python AI检测服务
cd python
python run.py
```

### 7. 访问应用
- **前端应用**: http://localhost:8080
- **后端API**: http://localhost:3000
- **Python服务**: http://localhost:5000
- **API文档**: http://localhost:3000/api-docs

## 📖 使用说明

### 🔐 系统登录
1. 打开浏览器访问 http://localhost:8080
2. 使用默认账号登录：
   - 用户名: admin
   - 密码: 200409
3. 或点击"注册"创建新账号

### 🎯 智能筛查使用流程
1. **进入智能筛查页面**
   - 点击侧边栏"智能筛查"菜单
   - 确保摄像头设备正常连接

2. **摄像头权限设置**
   - 浏览器会提示摄像头权限请求
   - 点击"允许"授权摄像头访问
   - 确认视频画面正常显示

3. **开始AI检测**
   - 点击"开始检测"按钮
   - 保持面部在摄像头视野内
   - 系统会显示实时检测状态

4. **查看检测结果**
   - 检测过程中可看到实时注意力状态
   - 检测完成后自动保存数据
   - 可在右侧面板查看详细结果

### 📊 数据分析功能
1. **进入数据分析页面**
   - 点击侧边栏"数据分析"菜单
   - 系统自动加载历史检测数据

2. **查看分析报告**
   - **注意力趋势图**: 显示时间序列变化
   - **注意力维度分析**: 多维度雷达图
   - **检测统计**: 总次数、平均分数等
   - **历史记录表**: 详细的检测历史

3. **数据交互**
   - 点击图表可查看详细数据
   - 支持时间范围筛选
   - 可导出分析报告

### 🎮 注意力游戏训练
1. **选择游戏类型**
   - 持续注意力训练
   - 选择性注意力训练
   - 分配性注意力训练

2. **开始游戏训练**
   - 根据游戏说明进行操作
   - 系统自动记录训练数据
   - 实时显示训练效果

### 📋 量表筛查评估
1. **选择评估量表**
   - ADHD相关量表
   - 抑郁症筛查量表
   - 焦虑症评估量表

2. **完成量表评估**
   - 按照题目要求如实填写
   - 系统自动计算评分
   - 生成专业评估报告

### 🎓 知识课程学习
1. **浏览课程内容**
   - 按分类查看课程
   - 根据推荐度排序
   - 搜索特定主题

2. **学习课程内容**
   - 阅读图文内容
   - 观看视频教程
   - 参与互动练习

### 💬 在线咨询服务
1. **预约专家咨询**
   - 选择专业咨询师
   - 预约咨询时间
   - 填写咨询需求

2. **进行在线咨询**
   - 视频通话咨询
   - 文字交流咨询
   - 查看咨询记录

### 🏥 康复计划制定
1. **生成个性化方案**
   - 基于检测结果自动生成
   - 根据个人特征调整
   - 设定康复目标

2. **执行康复计划**
   - 按计划进行训练
   - 记录执行情况
   - 定期评估效果

## 🔧 故障排除

### 常见问题及解决方案

#### 1. 摄像头权限问题
**问题**: 浏览器无法访问摄像头
**解决方案**:
```bash
# 检查浏览器权限设置
# Chrome: 设置 → 隐私设置和安全性 → 网站设置 → 摄像头
# Firefox: 设置 → 隐私与安全 → 权限 → 摄像头

# 确保使用HTTPS或localhost访问
# WebRTC需要安全上下文才能访问摄像头
```

#### 2. Python服务启动失败
**问题**: Python依赖安装失败或服务无法启动
**解决方案**:
```bash
# 检查Python版本
python --version

# 重新安装依赖
cd python
pip install --upgrade pip
pip install -r requirements.txt

# 检查OpenCV安装
python -c "import cv2; print(cv2.__version__)"

# 检查MediaPipe安装
python -c "import mediapipe; print('MediaPipe OK')"
```

#### 3. 前端编译错误
**问题**: npm install或npm run serve失败
**解决方案**:
```bash
# 清除npm缓存
npm cache clean --force

# 删除node_modules重新安装
rm -rf node_modules package-lock.json
npm install

# 检查Node.js版本兼容性
node --version  # 确保 >= 14.0
```

#### 4. Chart.js图表显示异常
**问题**: 数据分析页面图表无法显示或报错
**解决方案**:
```bash
# 检查浏览器控制台错误信息
# 常见错误: Canvas already in use

# 解决方案已内置在代码中:
# - 使用ultra-safe图表管理
# - 自动销毁和重建图表
# - DOM元素验证
```

#### 5. WebRTC连接超时
**问题**: 前端与Python服务连接失败
**解决方案**:
```bash
# 检查服务状态
curl http://localhost:5000/health

# 检查防火墙设置
# 确保5000端口未被阻止

# 重启Python服务
cd python
python run.py
```

#### 6. 数据传输问题
**问题**: 检测数据无法正确传输到数据分析页面
**解决方案**:
```javascript
// 检查LocalStorage数据
console.log(localStorage.getItem('adhd_detection_data'));

// 清除异常数据
localStorage.removeItem('adhd_detection_data');

// 重新进行检测
```

### 性能优化建议

#### 1. 系统性能优化
- **关闭不必要的后台程序**，释放CPU和内存资源
- **使用有线网络连接**，确保网络稳定性
- **关闭浏览器其他标签页**，减少资源占用
- **定期清理浏览器缓存**，避免存储空间不足

#### 2. 检测准确性优化
- **确保充足的光线**，避免背光或过暗环境
- **保持面部正对摄像头**，距离适中（50-80cm）
- **避免频繁移动**，保持相对静止状态
- **移除面部遮挡物**，如眼镜反光、帽子等

## 🛠️ 开发指南

### 开发环境配置
```bash
# 安装开发工具
npm install -g @vue/cli
npm install -g nodemon

# 启动开发模式
npm run serve          # 前端热重载
nodemon server/app.js  # 后端自动重启
```

### 代码规范
- **前端**: 遵循Vue.js官方风格指南
- **后端**: 使用ESLint进行代码检查
- **Python**: 遵循PEP 8代码规范
- **注释**: 中英文混合，关键逻辑必须注释

### 项目构建
```bash
# 开发环境构建
npm run serve

# 生产环境构建
npm run build

# 代码质量检查
npm run lint

# 运行测试
npm run test
```

### API接口开发
```javascript
// 新增API接口示例
// server/routes/newFeature.js
const express = require('express');
const router = express.Router();

router.post('/api/new-feature', (req, res) => {
    // 处理业务逻辑
    res.json({ success: true, data: result });
});

module.exports = router;
```

## ✨ 项目特色与优势

### 🎯 技术创新
- **非侵入性检测**: 仅需摄像头即可完成注意力评估
- **实时AI分析**: 基于MediaPipe的468个面部关键点追踪
- **多维度评估**: 眼动追踪、表情分析、行为模式识别
- **智能算法**: 自适应学习用户特征，提高准确性

### 🔬 科学严谨
- **标准化量表**: 集成国际权威ADHD评估量表
- **专业算法**: 基于心理学和神经科学研究
- **数据驱动**: 持续优化评估模型
- **循证医学**: 结合临床实践和科学研究

### 🎮 用户体验
- **界面友好**: 现代化UI设计，操作简单直观
- **游戏化训练**: 寓教于乐的注意力训练
- **个性化服务**: 定制化康复方案
- **全程跟踪**: 完整的进度记录和效果评估

### 📊 数据安全
- **本地存储**: 用户数据存储在本地，保护隐私
- **加密传输**: 数据传输采用安全加密协议
- **权限控制**: 严格的用户权限管理
- **合规标准**: 符合医疗数据保护法规

## 📝 更新日志

### v2.1.0 (2024-12-XX) - 当前版本
- ✅ 完成详细项目文档编写
- ✅ 优化README.md结构和内容
- ✅ 添加详细的文件说明和使用指南
- ✅ 完善故障排除和开发指南

### v2.0.0 (2024-12-XX)
- ✅ 完成"心理筛查"到"智能筛查"重命名
- ✅ 优化Chart.js图表管理系统
- ✅ 增强数据传输稳定性
- ✅ 改进响应式设计
- ✅ 添加注意力游戏模块

### v1.5.0 (2024-11-XX)
- ✅ 集成MediaPipe面部关键点检测
- ✅ 实现WebRTC实时视频传输
- ✅ 添加Python后端AI检测服务
- ✅ 完善数据分析可视化功能

### v1.0.0 (2024-10-XX)
- ✅ 基础系统架构搭建
- ✅ 七大功能模块开发完成
- ✅ 用户认证和权限管理
- ✅ 基础UI界面设计

## 🤝 贡献指南

### 参与贡献
1. Fork 本项目到您的GitHub账号
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

### 贡献类型
- **Bug修复**: 发现并修复系统缺陷
- **功能增强**: 添加新功能或改进现有功能
- **文档完善**: 改进文档质量和完整性
- **性能优化**: 提升系统性能和用户体验
- **测试覆盖**: 增加单元测试和集成测试

### 代码提交规范
```bash
# 提交信息格式
<type>(<scope>): <subject>

# 类型说明
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动

# 示例
feat(detection): 添加新的注意力检测算法
fix(chart): 修复Chart.js canvas冲突问题
docs(readme): 更新安装说明文档
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- **项目维护者**: ADHD辅助系统开发团队
- **技术支持**: [技术支持邮箱]
- **项目地址**: [GitHub仓库地址]
- **问题反馈**: [Issues页面地址]
- **讨论交流**: [讨论区地址]

## 🏥 免责声明

**重要提示**: 本系统仅用于辅助筛查和教育目的，不能替代专业医疗诊断。

- 本系统提供的检测结果仅供参考，不构成医疗建议
- 如有疑似ADHD症状，请及时咨询专业医疗机构
- 系统检测结果可能受环境、设备等因素影响
- 请在专业医生指导下进行诊断和治疗

---

**感谢您使用ADHD辅助与筛查系统！** 🎉

如果本项目对您有帮助，请给我们一个 ⭐ Star，您的支持是我们持续改进的动力！