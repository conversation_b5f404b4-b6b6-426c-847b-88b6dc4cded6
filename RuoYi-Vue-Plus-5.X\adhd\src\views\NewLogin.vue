<template>
  <div class="new-login-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shapes">
        <div class="shape shape-1"></div>
        <div class="shape shape-2"></div>
        <div class="shape shape-3"></div>
        <div class="shape shape-4"></div>
      </div>
    </div>

    <!-- 主登录容器 -->
    <div class="login-main-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="logo-container">
            <div class="logo-icon">
              <i class="fas fa-heartbeat"></i>
            </div>
            <h1 class="brand-title">智能心理健康</h1>
            <p class="brand-subtitle">专业的心理健康检测与筛查系统</p>
          </div>
          
          <div class="features-list">
            <div class="feature-item">
              <i class="fas fa-brain"></i>
              <span>AI智能分析</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-chart-line"></i>
              <span>专业评估报告</span>
            </div>
            <div class="feature-item">
              <i class="fas fa-shield-alt"></i>
              <span>隐私安全保护</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录区域 -->
      <div class="login-section">
        <div class="login-form-wrapper">
          <!-- 登录表单头部 -->
          <div class="form-header">
            <h2>{{ isRegisterMode ? '创建账号' : '欢迎回来' }}</h2>
            <p>{{ isRegisterMode ? '注册新账号开始您的健康管理之旅' : '请登录您的账号继续使用系统' }}</p>
          </div>

          <!-- 模式切换 -->
          <div class="mode-toggle">
            <button 
              :class="['toggle-btn', { active: !isRegisterMode }]"
              @click="switchToLogin"
            >
              登录
            </button>
            <button 
              :class="['toggle-btn', { active: isRegisterMode }]"
              @click="switchToRegister"
            >
              注册
            </button>
          </div>

          <!-- 登录表单 -->
          <form v-if="!isRegisterMode" @submit.prevent="handleLogin" class="auth-form">
            <div class="form-group">
              <label>用户名/手机号</label>
              <div class="input-wrapper">
                <i class="fas fa-user"></i>
                <input
                  v-model="loginForm.identifier"
                  type="text"
                  placeholder="请输入用户名或手机号"
                  :class="{ error: errors.identifier }"
                />
              </div>
              <span v-if="errors.identifier" class="error-text">{{ errors.identifier }}</span>
            </div>

            <div class="form-group">
              <label>密码</label>
              <div class="input-wrapper">
                <i class="fas fa-lock"></i>
                <input
                  v-model="loginForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  placeholder="请输入密码"
                  :class="{ error: errors.password }"
                />
                <button 
                  type="button" 
                  class="password-toggle"
                  @click="togglePassword"
                >
                  <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                </button>
              </div>
              <span v-if="errors.password" class="error-text">{{ errors.password }}</span>
            </div>

            <div class="form-options">
              <label class="checkbox-wrapper">
                <input type="checkbox" v-model="loginForm.rememberMe">
                <span class="checkmark"></span>
                记住我
              </label>
              <a href="#" class="forgot-link" @click.prevent="showForgotModal = true">
                忘记密码？
              </a>
            </div>

            <button 
              type="submit" 
              class="submit-btn"
              :disabled="isLoading"
            >
              <i v-if="isLoading" class="fas fa-spinner fa-spin"></i>
              <i v-else class="fas fa-sign-in-alt"></i>
              {{ isLoading ? '登录中...' : '立即登录' }}
            </button>
          </form>

          <!-- 注册表单 -->
          <form v-else @submit.prevent="handleRegister" class="auth-form">
            <div class="register-form-container">
              <div class="form-group">
                <label>真实姓名</label>
                <div class="input-wrapper">
                  <i class="fas fa-user"></i>
                  <input
                    v-model="registerForm.username"
                    type="text"
                    placeholder="请输入真实姓名"
                    :class="{ error: errors.username }"
                  />
                </div>
                <span v-if="errors.username" class="error-text">{{ errors.username }}</span>
              </div>

              <div class="form-group">
                <label>手机号码</label>
                <div class="input-wrapper">
                  <i class="fas fa-phone"></i>
                  <input
                    v-model="registerForm.phone"
                    type="tel"
                    placeholder="请输入手机号码"
                    :class="{ error: errors.phone }"
                  />
                </div>
                <span v-if="errors.phone" class="error-text">{{ errors.phone }}</span>
              </div>

              <div class="form-group">
                <label>身份证号</label>
                <div class="input-wrapper">
                  <i class="fas fa-id-card"></i>
                  <input
                    v-model="registerForm.idCard"
                    type="text"
                    placeholder="请输入身份证号码"
                    maxlength="18"
                    :class="{ error: errors.idCard }"
                  />
                </div>
                <span v-if="errors.idCard" class="error-text">{{ errors.idCard }}</span>
              </div>

              <div class="form-group">
                <label>年龄</label>
                <div class="input-wrapper">
                  <i class="fas fa-calendar-alt"></i>
                  <input
                    v-model="registerForm.age"
                    type="number"
                    placeholder="请输入年龄"
                    min="1"
                    max="120"
                    :class="{ error: errors.age }"
                  />
                </div>
                <span v-if="errors.age" class="error-text">{{ errors.age }}</span>
              </div>

              <div class="form-group">
                <label>性别</label>
                <div class="input-wrapper">
                  <i class="fas fa-venus-mars"></i>
                  <select
                    v-model="registerForm.gender"
                    :class="{ error: errors.gender }"
                  >
                    <option value="">请选择性别</option>
                    <option value="male">男</option>
                    <option value="female">女</option>
                    <option value="other">其他</option>
                  </select>
                </div>
                <span v-if="errors.gender" class="error-text">{{ errors.gender }}</span>
              </div>

              <div class="form-group">
                <label>密码</label>
                <div class="input-wrapper">
                  <i class="fas fa-lock"></i>
                  <input
                    v-model="registerForm.password"
                    :type="showPassword ? 'text' : 'password'"
                    placeholder="请设置密码（至少6位）"
                    :class="{ error: errors.password }"
                  />
                  <button
                    type="button"
                    class="password-toggle"
                    @click="togglePassword"
                  >
                    <i :class="showPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                  </button>
                </div>
                <span v-if="errors.password" class="error-text">{{ errors.password }}</span>
              </div>

              <div class="form-group">
                <label>确认密码</label>
                <div class="input-wrapper">
                  <i class="fas fa-lock"></i>
                  <input
                    v-model="registerForm.confirmPassword"
                    :type="showConfirmPassword ? 'text' : 'password'"
                    placeholder="请再次输入密码"
                    :class="{ error: errors.confirmPassword }"
                  />
                  <button
                    type="button"
                    class="password-toggle"
                    @click="toggleConfirmPassword"
                  >
                    <i :class="showConfirmPassword ? 'fas fa-eye-slash' : 'fas fa-eye'"></i>
                  </button>
                </div>
                <span v-if="errors.confirmPassword" class="error-text">{{ errors.confirmPassword }}</span>
              </div>

              <div class="form-group">
                <label class="checkbox-wrapper">
                  <input type="checkbox" v-model="registerForm.agreeTerms">
                  <span class="checkmark"></span>
                  我已阅读并同意 <a href="#" class="terms-link">用户协议</a> 和 <a href="#" class="terms-link">隐私政策</a>
                </label>
                <span v-if="errors.agreeTerms" class="error-text">{{ errors.agreeTerms }}</span>
              </div>
            </div>

            <button
              type="submit"
              class="submit-btn"
              :disabled="isLoading"
            >
              <i v-if="isLoading" class="fas fa-spinner fa-spin"></i>
              <i v-else class="fas fa-user-plus"></i>
              {{ isLoading ? '注册中...' : '立即注册' }}
            </button>
          </form>

          <!-- 底部链接 -->
          <div class="form-footer">
            <p v-if="!isRegisterMode">
              还没有账号？
              <a href="#" @click.prevent="switchToRegister" class="switch-link">立即注册</a>
            </p>
            <p v-else>
              已有账号？
              <a href="#" @click.prevent="switchToLogin" class="switch-link">立即登录</a>
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 忘记密码模态框 -->
    <div v-if="showForgotModal" class="modal-overlay" @click="showForgotModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>重置密码</h3>
          <button class="close-btn" @click="showForgotModal = false">
            <i class="fas fa-times"></i>
          </button>
        </div>
        <div class="modal-body">
          <p>请输入您的邮箱地址，我们将发送重置密码链接到您的邮箱。</p>
          <div class="form-group">
            <div class="input-wrapper">
              <i class="fas fa-envelope"></i>
              <input
                v-model="resetEmail"
                type="email"
                placeholder="请输入邮箱地址"
              />
            </div>
          </div>
        </div>
        <div class="modal-footer">
          <button class="btn-secondary" @click="showForgotModal = false">取消</button>
          <button class="btn-primary" @click="handleForgotPassword">发送重置链接</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { backendAuth } from '@/api/ruoyiAPI.js'

export default {
  name: 'NewLoginPage',
  data() {
    return {
      isRegisterMode: false,
      isLoading: false,
      showPassword: false,
      showConfirmPassword: false,
      showForgotModal: false,
      resetEmail: '',
      
      loginForm: {
        identifier: '',
        password: '',
        rememberMe: false
      },
      
      registerForm: {
        username: '',
        phone: '',
        idCard: '',
        age: '',
        gender: '',
        password: '',
        confirmPassword: '',
        agreeTerms: false
      },
      
      errors: {}
    }
  },
  
  methods: {
    // 切换到登录模式
    switchToLogin() {
      this.isRegisterMode = false
      this.clearErrors()
    },
    
    // 切换到注册模式
    switchToRegister() {
      this.isRegisterMode = true
      this.clearErrors()
    },
    
    // 清除错误信息
    clearErrors() {
      this.errors = {}
    },
    
    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword
    },

    // 切换确认密码显示
    toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword
    },
    
    // 验证登录表单
    validateLoginForm() {
      this.errors = {}
      
      if (!this.loginForm.identifier) {
        this.errors.identifier = '请输入用户名或手机号'
      }
      
      if (!this.loginForm.password) {
        this.errors.password = '请输入密码'
      }
      
      return Object.keys(this.errors).length === 0
    },
    
    // 验证注册表单
    validateRegisterForm() {
      this.errors = {}

      if (!this.registerForm.username) {
        this.errors.username = '请输入真实姓名'
      } else if (this.registerForm.username.length < 2) {
        this.errors.username = '姓名长度不能少于2位'
      }

      if (!this.registerForm.phone) {
        this.errors.phone = '请输入手机号码'
      } else if (!/^1[3-9]\d{9}$/.test(this.registerForm.phone)) {
        this.errors.phone = '请输入正确的手机号码'
      }

      if (!this.registerForm.idCard) {
        this.errors.idCard = '请输入身份证号码'
      } else if (!this.validateIdCard(this.registerForm.idCard)) {
        this.errors.idCard = '请输入正确的身份证号码'
      }

      if (!this.registerForm.age) {
        this.errors.age = '请输入年龄'
      } else if (this.registerForm.age < 1 || this.registerForm.age > 120) {
        this.errors.age = '请输入有效的年龄（1-120岁）'
      }

      if (!this.registerForm.gender) {
        this.errors.gender = '请选择性别'
      }

      if (!this.registerForm.password) {
        this.errors.password = '请设置密码'
      } else if (this.registerForm.password.length < 6) {
        this.errors.password = '密码长度不能少于6位'
      }

      if (!this.registerForm.confirmPassword) {
        this.errors.confirmPassword = '请确认密码'
      } else if (this.registerForm.password !== this.registerForm.confirmPassword) {
        this.errors.confirmPassword = '两次输入的密码不一致'
      }

      if (!this.registerForm.agreeTerms) {
        this.errors.agreeTerms = '请阅读并同意用户协议和隐私政策'
      }

      return Object.keys(this.errors).length === 0
    },

    // 验证身份证号码
    validateIdCard(idCard) {
      // 18位身份证号码正则表达式
      const idCardRegex = /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/

      if (!idCardRegex.test(idCard)) {
        return false
      }

      // 验证校验码
      const factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
      const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']

      let sum = 0
      for (let i = 0; i < 17; i++) {
        sum += parseInt(idCard[i]) * factors[i]
      }

      const checkCode = checkCodes[sum % 11]
      return checkCode === idCard[17].toUpperCase()
    },
    
    // 处理登录
    async handleLogin() {
      if (!this.validateLoginForm()) {
        return
      }
      
      this.isLoading = true
      
      try {
        const result = await backendAuth.login(
          this.loginForm.identifier,
          this.loginForm.password,
          this.loginForm.rememberMe
        )
        
        if (result.success) {
          alert('登录成功！')
          this.$router.push('/')
        } else {
          if (result.suggestRegister) {
            this.errors.identifier = result.message
            if (confirm('系统中暂无用户数据，是否要注册新账号？')) {
              this.switchToRegister()
              if (/^1[3-9]\d{9}$/.test(this.loginForm.identifier)) {
                this.registerForm.phone = this.loginForm.identifier
              }
            }
          } else {
            this.errors.identifier = result.message
          }
        }
      } catch (error) {
        this.errors.password = error.message || '登录失败，请稍后重试'
      } finally {
        this.isLoading = false
      }
    },
    
    // 处理注册
    async handleRegister() {
      if (!this.validateRegisterForm()) {
        return
      }

      this.isLoading = true
      this.clearErrors()

      try {
        console.log('📝 开始注册流程...')
        console.log('注册信息:', this.registerForm)

        // 调用后端注册API
        const result = await backendAuth.register({
          username: this.registerForm.username,
          phone: this.registerForm.phone,
          password: this.registerForm.password,
          age: parseInt(this.registerForm.age),
          gender: this.registerForm.gender,
          idCard: this.registerForm.idCard,
          // 可选字段，后续可以在个人中心补填
          address: '',
          emergencyContact: '',
          emergencyPhone: '',
          bloodType: '',
          allergies: '无',
          medicalHistory: '无'
        })

        if (result.success) {
          console.log('✅ 注册成功:', result)

          alert(`注册成功！您的用户ID是：${result.userId}，请妥善保管。现在可以登录您的账号。`)

          // 切换到登录模式并自动填充用户ID
          this.switchToLogin()
          this.loginForm.identifier = result.userId

          // 清空注册表单
          this.registerForm = {
            username: '',
            phone: '',
            idCard: '',
            age: '',
            gender: '',
            password: '',
            confirmPassword: '',
            agreeTerms: false
          }
        } else {
          // 注册失败，显示具体错误
          if (result.message.includes('身份证号')) {
            this.errors.idCard = result.message
          } else if (result.message.includes('手机号')) {
            this.errors.phone = result.message
          } else if (result.message.includes('用户名')) {
            this.errors.username = result.message
          } else {
            this.errors.username = result.message || '注册失败，请重试'
          }
        }
      } catch (error) {
        console.error('❌ 注册异常:', error)
        this.errors.username = error.message || '注册失败，请稍后重试'
      } finally {
        this.isLoading = false
      }
    },
    
    // 处理忘记密码
    async handleForgotPassword() {
      if (!this.resetEmail) {
        alert('请输入邮箱地址')
        return
      }
      
      try {
        console.log('发送重置邮件到:', this.resetEmail)
        alert('重置密码链接已发送到您的邮箱，请查收。')
        this.showForgotModal = false
      } catch (error) {
        alert('发送失败，请稍后重试')
      }
    }
  }
}
</script>

<style scoped>
/* 全新登录界面样式 */
.new-login-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
  font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-shapes {
  position: relative;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 主登录容器 */
.login-main-container {
  display: flex;
  width: 100%;
  max-width: 1200px;
  height: 700px;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  backdrop-filter: blur(10px);
}

/* 左侧品牌区域 */
.brand-section {
  flex: 1;
  background: linear-gradient(135deg, #16213e 0%, #1e3c72 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  position: relative;
}

.brand-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.brand-content {
  position: relative;
  z-index: 1;
  text-align: center;
}

.logo-container {
  margin-bottom: 40px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #2a5298, #1e3c72);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
  box-shadow: 0 10px 30px rgba(42, 82, 152, 0.3);
}

.logo-icon i {
  font-size: 36px;
  color: white;
}

.brand-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 10px 0;
  background: linear-gradient(135deg, #ffffff, #e8eaed);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  line-height: 1.5;
}

.features-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  font-size: 16px;
  opacity: 0.9;
}

.feature-item i {
  width: 24px;
  font-size: 20px;
  color: #2a5298;
}

/* 右侧登录区域 */
.login-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  background: #ffffff;
}

.login-form-wrapper {
  width: 100%;
  max-width: 400px;
  min-height: 500px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 表单头部 */
.form-header {
  text-align: center;
  margin-bottom: 30px;
}

.form-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #1e3c72;
  margin: 0 0 10px 0;
}

.form-header p {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

/* 模式切换 */
.mode-toggle {
  display: flex;
  background: #f8f9fc;
  border-radius: 12px;
  padding: 4px;
  margin-bottom: 30px;
  border: 1px solid #e3e6f0;
}

.toggle-btn {
  flex: 1;
  padding: 12px 20px;
  border: none;
  background: transparent;
  color: #666;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn.active {
  background: linear-gradient(135deg, #2a5298, #1e3c72);
  color: white;
  box-shadow: 0 4px 12px rgba(42, 82, 152, 0.3);
}

/* 表单样式 */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 注册表单容器 - 添加滚动 */
.register-form-container {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;
  margin-bottom: 20px;
}

/* 自定义滚动条样式 */
.register-form-container::-webkit-scrollbar {
  width: 6px;
}

.register-form-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.register-form-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
  transition: background 0.3s ease;
}

.register-form-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Firefox滚动条样式 */
.register-form-container {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-wrapper i {
  position: absolute;
  left: 16px;
  color: #666;
  font-size: 16px;
  z-index: 2;
}

.input-wrapper input,
.input-wrapper select {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: 2px solid #e3e6f0;
  border-radius: 12px;
  font-size: 16px;
  background: #f8f9fc;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.input-wrapper select {
  cursor: pointer;
  appearance: none;
  background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23666" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
  background-repeat: no-repeat;
  background-position: right 16px center;
  background-size: 12px;
}

.input-wrapper input:focus,
.input-wrapper select:focus {
  outline: none;
  border-color: #2a5298;
  background: white;
  box-shadow: 0 0 0 4px rgba(42, 82, 152, 0.1);
}

.input-wrapper input.error,
.input-wrapper select.error {
  border-color: #e74c3c;
  background: #fdf2f2;
}

.password-toggle {
  position: absolute;
  right: 16px;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 4px;
  z-index: 2;
}

.password-toggle:hover {
  color: #2a5298;
}

.error-text {
  font-size: 12px;
  color: #e74c3c;
  margin-top: 4px;
}

/* 表单选项 */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.checkbox-wrapper input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid #e3e6f0;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark {
  background: #2a5298;
  border-color: #2a5298;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.forgot-link {
  color: #2a5298;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.forgot-link:hover {
  color: #1e3c72;
  text-decoration: underline;
}

/* 提交按钮 */
.submit-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #2a5298, #1e3c72);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-top: 10px;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(42, 82, 152, 0.4);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* 表单底部 */
.form-footer {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e3e6f0;
}

.form-footer p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.switch-link {
  color: #2a5298;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.switch-link:hover {
  color: #1e3c72;
  text-decoration: underline;
}

.terms-link {
  color: #2a5298;
  text-decoration: none;
}

.terms-link:hover {
  text-decoration: underline;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 90%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e3e6f0;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  color: #1e3c72;
}

.close-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  padding: 4px;
}

.close-btn:hover {
  color: #e74c3c;
}

.modal-body {
  padding: 24px;
}

.modal-body p {
  margin: 0 0 20px 0;
  color: #666;
  line-height: 1.5;
}

.modal-footer {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e3e6f0;
}

.btn-secondary {
  flex: 1;
  padding: 12px 20px;
  background: #f8f9fc;
  color: #666;
  border: 1px solid #e3e6f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #e3e6f0;
}

.btn-primary {
  flex: 1;
  padding: 12px 20px;
  background: linear-gradient(135deg, #2a5298, #1e3c72);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(42, 82, 152, 0.3);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-main-container {
    flex-direction: column;
    height: auto;
    max-width: 500px;
  }

  .brand-section {
    padding: 40px 30px;
  }

  .brand-title {
    font-size: 24px;
  }

  .features-list {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .new-login-page {
    padding: 15px;
  }

  .login-main-container {
    border-radius: 16px;
  }

  .login-section {
    padding: 40px 30px;
  }

  .form-header h2 {
    font-size: 24px;
  }

  .input-wrapper input {
    padding: 14px 14px 14px 44px;
    font-size: 16px;
  }

  .submit-btn {
    padding: 14px;
  }
}

@media (max-width: 480px) {
  .brand-section {
    padding: 30px 20px;
  }

  .login-section {
    padding: 30px 20px;
  }

  .features-list {
    flex-direction: column;
    gap: 15px;
  }

  .feature-item {
    justify-content: center;
  }
}
</style>
