import axios from 'axios'

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API || 'http://localhost:8080', // RuoYi后端地址
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 添加token等认证信息
    const token = localStorage.getItem('userToken') || sessionStorage.getItem('userToken')
    if (token) {
      config.headers['Authorization'] = 'Bearer ' + token
    }
    
    // 添加用户ID（如果需要）
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || sessionStorage.getItem('userInfo') || '{}')
    if (userInfo.userId) {
      config.headers['X-User-ID'] = userInfo.userId
    }
    
    console.log('🚀 发送请求:', config.method?.toUpperCase(), config.url, config.data || config.params)
    return config
  },
  error => {
    console.error('❌ 请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    console.log('✅ 收到响应:', response.config.url, res)
    
    // 根据RuoYi的响应格式处理
    if (res.code !== undefined) {
      // RuoYi标准响应格式
      if (res.code === 200) {
        return res
      } else {
        console.error('❌ API错误:', res.msg || '未知错误')
        
        // 处理特殊错误码
        if (res.code === 401) {
          // token过期，清除本地存储并跳转登录
          localStorage.removeItem('userToken')
          localStorage.removeItem('userInfo')
          sessionStorage.removeItem('userToken')
          sessionStorage.removeItem('userInfo')
          
          // 如果不在登录页面，则跳转到登录页面
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
        }
        
        return Promise.reject(new Error(res.msg || '请求失败'))
      }
    } else {
      // 非标准响应格式，直接返回
      return response
    }
  },
  error => {
    console.error('❌ 响应错误:', error)
    
    let message = '网络错误'
    
    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          message = data?.msg || '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 清除本地存储
          localStorage.removeItem('userToken')
          localStorage.removeItem('userInfo')
          sessionStorage.removeItem('userToken')
          sessionStorage.removeItem('userInfo')
          // 跳转登录页面
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = data?.msg || `服务器错误 ${status}`
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      message = '网络连接超时，请检查网络'
    } else {
      // 其他错误
      message = error.message || '请求失败'
    }
    
    // 显示错误提示（可以根据需要使用不同的提示方式）
    if (window.Vue && window.Vue.prototype.$message) {
      window.Vue.prototype.$message.error(message)
    } else {
      console.error('错误信息:', message)
    }
    
    return Promise.reject(error)
  }
)

// 封装常用的请求方法
export const request = {
  get(url, params = {}) {
    return service({
      method: 'get',
      url,
      params
    })
  },
  
  post(url, data = {}) {
    return service({
      method: 'post',
      url,
      data
    })
  },
  
  put(url, data = {}) {
    return service({
      method: 'put',
      url,
      data
    })
  },
  
  delete(url, params = {}) {
    return service({
      method: 'delete',
      url,
      params
    })
  },
  
  upload(url, formData) {
    return service({
      method: 'post',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

export default service
