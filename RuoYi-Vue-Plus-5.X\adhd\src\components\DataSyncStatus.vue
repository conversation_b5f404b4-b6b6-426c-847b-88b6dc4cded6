<template>
  <div class="data-sync-status" v-if="visible">
    <div class="status-card">
      <div class="status-header">
        <h4>📊 数据同步状态</h4>
        <button class="close-btn" @click="$emit('close')">×</button>
      </div>
      
      <div class="sync-info">
        <div class="user-info">
          <div class="user-avatar">
            <i class="fas fa-user-circle"></i>
          </div>
          <div class="user-details">
            <div class="username">{{ currentUser.username || '未登录用户' }}</div>
            <div class="user-status" :class="authStatusClass">
              <i :class="authStatusIcon"></i>
              {{ authStatusText }}
            </div>
          </div>
        </div>
        
        <div class="sync-options">
          <div class="option-card" v-if="hasAuthToken">
            <div class="option-icon success">
              <i class="fas fa-shield-alt"></i>
            </div>
            <div class="option-content">
              <h5>认证用户同步</h5>
              <p>使用当前登录用户身份，安全可靠</p>
              <button class="sync-btn primary" @click="syncAsAuthenticatedUser">
                <i class="fas fa-sync"></i> 立即同步
              </button>
            </div>
          </div>
          
          <div class="option-card" v-if="!hasAuthToken">
            <div class="option-icon warning">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="option-content">
              <h5>需要登录</h5>
              <p>请先登录以获得最佳的数据同步体验</p>
              <button class="sync-btn secondary" @click="$emit('show-login')">
                <i class="fas fa-sign-in-alt"></i> 登录账户
              </button>
            </div>
          </div>
          
          <div class="option-card">
            <div class="option-icon info">
              <i class="fas fa-save"></i>
            </div>
            <div class="option-content">
              <h5>本地保存</h5>
              <p>数据已安全保存到本地，稍后可同步</p>
              <div class="local-data-info">
                <span class="data-count">{{ localDataCount }} 条待同步数据</span>
                <button v-if="localDataCount > 0" class="sync-btn info" @click="syncLocalData">
                  <i class="fas fa-upload"></i> 同步本地数据
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="sync-tips">
        <h5>💡 数据同步说明</h5>
        <ul>
          <li><strong>认证同步</strong>：数据直接同步到医生端系统，实时可见</li>
          <li><strong>本地保存</strong>：数据安全保存在本地，不会丢失</li>
          <li><strong>后续同步</strong>：登录后可将本地数据批量同步到系统</li>
          <li><strong>数据安全</strong>：所有数据都经过加密处理</li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DataSyncStatus',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentUser: {},
      localDataCount: 0
    }
  },
  computed: {
    hasAuthToken() {
      return !!(localStorage.getItem('userToken') || sessionStorage.getItem('userToken'));
    },
    authStatusClass() {
      return this.hasAuthToken ? 'authenticated' : 'not-authenticated';
    },
    authStatusIcon() {
      return this.hasAuthToken ? 'fas fa-check-circle' : 'fas fa-times-circle';
    },
    authStatusText() {
      return this.hasAuthToken ? '已认证用户' : '未登录用户';
    }
  },
  mounted() {
    this.loadUserInfo();
    this.checkLocalData();
  },
  methods: {
    loadUserInfo() {
      this.currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}');
    },
    
    checkLocalData() {
      try {
        const backupData = localStorage.getItem('ruoyi_backup_scale_records');
        if (backupData) {
          const data = JSON.parse(backupData);
          this.localDataCount = data.filter(item => item.syncStatus === 'pending').length;
        }
      } catch (error) {
        console.error('检查本地数据失败:', error);
      }
    },
    
    async syncAsAuthenticatedUser() {
      this.$emit('sync-authenticated');
    },
    
    async syncLocalData() {
      this.$emit('sync-local-data');
    }
  }
}
</script>

<style scoped>
.data-sync-status {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.status-card {
  background: white;
  border-radius: 12px;
  padding: 25px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #eee;
}

.status-header h4 {
  margin: 0;
  color: #2c3e50;
}

.close-btn {
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  font-size: 18px;
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 25px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.user-avatar i {
  font-size: 40px;
  color: #6c757d;
}

.username {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 5px;
}

.user-status {
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.user-status.authenticated {
  color: #28a745;
}

.user-status.not-authenticated {
  color: #dc3545;
}

.sync-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.option-card {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  background: #fff;
}

.option-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.option-icon.success {
  background: #28a745;
}

.option-icon.warning {
  background: #ffc107;
}

.option-icon.info {
  background: #17a2b8;
}

.option-content {
  flex: 1;
}

.option-content h5 {
  margin: 0 0 5px 0;
  color: #2c3e50;
}

.option-content p {
  margin: 0 0 10px 0;
  color: #6c757d;
  font-size: 14px;
}

.sync-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
}

.sync-btn.primary {
  background: #007bff;
  color: white;
}

.sync-btn.secondary {
  background: #6c757d;
  color: white;
}

.sync-btn.info {
  background: #17a2b8;
  color: white;
}

.local-data-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.data-count {
  font-size: 12px;
  color: #6c757d;
}

.sync-tips {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.sync-tips h5 {
  margin: 0 0 10px 0;
  color: #2c3e50;
}

.sync-tips ul {
  margin: 0;
  padding-left: 20px;
}

.sync-tips li {
  margin-bottom: 5px;
  font-size: 14px;
  color: #6c757d;
}
</style>
