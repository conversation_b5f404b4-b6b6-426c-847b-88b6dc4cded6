{"doc": " 租户套餐Service接口\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "queryById", "paramTypes": ["java.lang.Long"], "doc": " 查询租户套餐\n"}, {"name": "queryPageList", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询租户套餐列表\n"}, {"name": "selectList", "paramTypes": [], "doc": " 查询租户套餐已启用列表\n"}, {"name": "queryList", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 查询租户套餐列表\n"}, {"name": "insertByBo", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 新增租户套餐\n"}, {"name": "updateByBo", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 修改租户套餐\n"}, {"name": "checkPackageNameUnique", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 校验套餐名称是否唯一\n"}, {"name": "updatePackageStatus", "paramTypes": ["org.dromara.system.domain.bo.SysTenantPackageBo"], "doc": " 修改套餐状态\n"}, {"name": "deleteWithValidByIds", "paramTypes": ["java.util.Collection", "java.lang.Bo<PERSON>an"], "doc": " 校验并批量删除租户套餐信息\n"}], "constructors": []}