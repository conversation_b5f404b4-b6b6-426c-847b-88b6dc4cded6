"""
ADHD注意力检测 - WebRTC直连版本
最快速的AI检测方案，前端直接连接Python AI服务
"""

import cv2
import numpy as np
import asyncio
import websockets
import json
import base64
from datetime import datetime
import threading
import time
import mediapipe as mp

class WebRTCDirectDetector:
    def __init__(self):
        print("🚨🚨🚨 WebRTCDirectDetector 初始化开始！！！")  # 添加明显的调试信息
        self.is_running = False
        self.clients = set()
        self.detection_active = False

        # 初始化MediaPipe Face Mesh
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        print("🚨🚨🚨 WebRTCDirectDetector 初始化完成！！！")  # 添加明显的调试信息
        
    async def handle_client(self, websocket):
        """处理WebSocket客户端连接"""
        self.clients.add(websocket)
        print(f"🔗 客户端已连接，当前连接数: {len(self.clients)}")

        try:
            async for message in websocket:
                data = json.loads(message)
                await self.process_message(websocket, data)

        except websockets.exceptions.ConnectionClosed:
            print("📱 客户端断开连接")
        except Exception as e:
            print(f"❌ 处理客户端消息错误: {e}")
        finally:
            self.clients.discard(websocket)
            print(f"🔗 客户端已断开，当前连接数: {len(self.clients)}")
    
    async def process_message(self, websocket, data):
        """处理客户端消息"""
        message_type = data.get('type')
        
        if message_type == 'start_detection':
            print("🚀 开始AI注意力检测")
            self.detection_active = True
            await self.send_response(websocket, {
                'type': 'detection_started',
                'message': 'AI注意力检测已启动'
            })
            
        elif message_type == 'stop_detection':
            print("🛑 停止AI注意力检测")
            self.detection_active = False
            await self.send_response(websocket, {
                'type': 'detection_stopped',
                'message': 'AI注意力检测已停止'
            })
            
        elif message_type == 'video_frame':
            if self.detection_active:
                await self.process_video_frame(websocket, data)
                
        elif message_type == 'ping':
            await self.send_response(websocket, {
                'type': 'pong',
                'timestamp': datetime.now().isoformat()
            })
    
    async def process_video_frame(self, websocket, data):
        """处理视频帧并进行AI检测"""
        try:
            # 解码base64图像
            image_data = data.get('frame')
            if not image_data:
                return
                
            # 移除data:image/jpeg;base64,前缀
            if ',' in image_data:
                image_data = image_data.split(',')[1]
            
            # 解码图像
            img_bytes = base64.b64decode(image_data)
            nparr = np.frombuffer(img_bytes, np.uint8)
            frame = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
            
            if frame is None:
                return
            
            # 进行AI注意力检测
            detection_result = self.detect_attention(frame)
            
            # 发送检测结果
            response_data = {
                'type': 'detection_result',
                'result': detection_result,
                'timestamp': datetime.now().isoformat()
            }

            print(f"📤 发送AI检测结果: {detection_result}")
            await self.send_response(websocket, response_data)
            
        except Exception as e:
            print(f"❌ 处理视频帧错误: {e}")
    
    def detect_attention(self, frame):
        """AI注意力检测核心算法 - 增强版 + MediaPipe特征点"""
        print("🚨 DETECT_ATTENTION 被调用了！！！")  # 添加明显的调试信息
        try:
            # 转换为RGB格式 (MediaPipe需要RGB)
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # 转换为灰度图 (用于传统检测)
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 1. MediaPipe Face Mesh检测 (468个特征点)
            landmarks_data = []
            print(f"🔍 开始MediaPipe处理，图像尺寸: {rgb_frame.shape}")

            try:
                results = self.face_mesh.process(rgb_frame)
                print(f"📊 MediaPipe处理完成，结果: {results is not None}")

                if results.multi_face_landmarks:
                    print(f"👤 检测到 {len(results.multi_face_landmarks)} 个人脸")
                    for face_landmarks in results.multi_face_landmarks:
                        # 提取468个特征点
                        for idx, landmark in enumerate(face_landmarks.landmark):
                            landmarks_data.append({
                                'x': landmark.x,
                                'y': landmark.y,
                                'z': landmark.z,
                                'index': idx
                            })
                        break  # 只处理第一个检测到的人脸
                else:
                    print("❌ MediaPipe未检测到人脸特征点")

            except Exception as e:
                print(f"❌ MediaPipe处理错误: {e}")

            print(f"🎯 MediaPipe检测到 {len(landmarks_data)} 个特征点")

            # 2. 传统人脸检测 (用于注意力分析)
            face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            faces = face_cascade.detectMultiScale(gray, 1.1, 4)

            # 3. 眼部检测
            eye_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_eye.xml')
            eyes = eye_cascade.detectMultiScale(gray, 1.1, 4)

            # 初始化评分和变量
            attention_level = 0.5
            attention_status = "正常"
            eye_score = 0.5
            face_score = 0.5
            motion_score = 0.5
            confidence = 0.85
            face_ratio = 0.0  # 初始化face_ratio

            # 3. 面部评分计算
            if len(faces) > 0:
                largest_face = max(faces, key=lambda f: f[2] * f[3])  # 选择最大的人脸
                x, y, w, h = largest_face

                # 计算人脸质量评分
                face_area = w * h
                frame_area = frame.shape[0] * frame.shape[1]
                face_ratio = face_area / frame_area

                # 面部评分：基于人脸大小、位置和清晰度
                face_score = min(0.95, max(0.1, face_ratio * 8))

                # 人脸位置评分（中心位置更好）
                face_center_x = x + w // 2
                face_center_y = y + h // 2
                frame_center_x = frame.shape[1] // 2
                frame_center_y = frame.shape[0] // 2

                center_distance = ((face_center_x - frame_center_x) ** 2 +
                                 (face_center_y - frame_center_y) ** 2) ** 0.5
                max_distance = (frame.shape[1] ** 2 + frame.shape[0] ** 2) ** 0.5
                position_score = 1 - (center_distance / max_distance)

                face_score = (face_score + position_score) / 2

                # 4. 眼部评分计算
                eyes_in_face = []
                for (ex, ey, ew, eh) in eyes:
                    # 检查眼部是否在人脸区域内
                    if (x <= ex <= x + w and y <= ey <= y + h):
                        eyes_in_face.append((ex, ey, ew, eh))

                if len(eyes_in_face) >= 2:
                    # 检测到双眼，高分
                    eye_score = min(0.95, 0.7 + len(eyes_in_face) * 0.1)
                elif len(eyes_in_face) == 1:
                    # 检测到单眼，中等分数
                    eye_score = 0.6
                else:
                    # 未检测到眼部，可能闭眼或侧脸
                    eye_score = max(0.2, face_score * 0.5)

                # 5. 动作评分计算（基于人脸稳定性）
                # 这里简化处理，实际应该比较连续帧
                if face_ratio > 0.15:
                    motion_score = 0.8  # 人脸大且稳定
                    attention_status = "专注"
                    attention_level = min(0.9, 0.6 + face_ratio * 2)
                elif face_ratio > 0.08:
                    motion_score = 0.6  # 中等稳定
                    attention_status = "正常"
                    attention_level = 0.5 + face_ratio
                else:
                    motion_score = 0.3  # 不稳定或距离远
                    attention_status = "分心"
                    attention_level = max(0.2, face_ratio * 3)

            else:
                # 未检测到人脸
                face_score = 0.1
                eye_score = 0.1
                motion_score = 0.1
                attention_level = 0.2
                attention_status = "发呆"
                confidence = 0.3

            # 6. 综合置信度计算
            confidence = (face_score + eye_score + motion_score) / 3

            return {
                'attention_level': round(attention_level, 2),
                'attention_status': attention_status,
                'face_count': len(faces),
                'confidence': round(confidence, 2),
                'eye_score': round(eye_score, 2),
                'face_score': round(face_score, 2),
                'motion_score': round(motion_score, 2),
                'landmarks': landmarks_data,  # 添加468个特征点数据
                'details': {
                    'eyes_detected': len(eyes),
                    'face_ratio': round(face_ratio if len(faces) > 0 else 0, 3),
                    'frame_size': f"{frame.shape[1]}x{frame.shape[0]}",
                    'landmarks_count': len(landmarks_data)
                }
            }
            
        except Exception as e:
            print(f"❌ AI检测错误: {e}")
            return {
                'attention_level': 0.5,
                'attention_status': "检测错误",
                'face_count': 0,
                'confidence': 0.0,
                'eye_score': 0.0,
                'face_score': 0.0,
                'motion_score': 0.0,
                'landmarks': [],  # 空的特征点数据
                'details': {
                    'error': str(e),
                    'landmarks_count': 0
                }
            }
    
    async def send_response(self, websocket, data):
        """发送响应给客户端"""
        try:
            await websocket.send(json.dumps(data))
        except Exception as e:
            print(f"❌ 发送响应错误: {e}")
    
    async def start_server(self, host='localhost', port=8765):
        """启动WebRTC直连服务器"""
        print(f"🚀 启动ADHD WebRTC直连服务器")
        print(f"📡 地址: ws://{host}:{port}")
        print(f"🎯 前端可直接连接进行AI检测")

        async with websockets.serve(self.handle_client, host, port):
            print("✅ WebRTC服务器启动成功")
            await asyncio.Future()  # 保持服务器运行

async def main():
    """主函数"""
    detector = WebRTCDirectDetector()

    try:
        await detector.start_server()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器错误: {e}")

if __name__ == "__main__":
    asyncio.run(main())
