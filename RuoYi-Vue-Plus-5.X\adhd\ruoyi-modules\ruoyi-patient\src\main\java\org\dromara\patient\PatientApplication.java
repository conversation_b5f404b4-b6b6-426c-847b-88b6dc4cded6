package org.dromara.patient;

import org.dromara.common.core.utils.SpringUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;

/**
 * 患者管理模块启动程序
 *
 * <AUTHOR> System
 * @date 2025-07-19
 */
@SpringBootApplication
public class PatientApplication {

    public static void main(String[] args) {
        System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication application = new SpringApplication(PatientApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(♥◠‿◠)ﾉﾞ  患者管理模块启动成功   ლ(´ڡ`ლ)ﾞ");
        System.out.println("AI赋能青少年心理健康管理平台 - 患者管理模块已启动");
    }

}
