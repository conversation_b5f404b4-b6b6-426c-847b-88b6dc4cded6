/**
 * 用户认证相关API服务 - 专门处理登录、注册、用户管理
 */

import { request, formatters } from './axios.js'

// 用户注册API
export async function registerUser(userData) {
  console.log('👤 注册用户到RuoYi后端...')
  console.log('📤 原始注册数据:', userData)

  // 确保userId是正确的ID而不是用户名
  let correctUserId = userData.userId;
  if (!correctUserId || correctUserId === userData.username) {
    // 如果没有正确的userId或userId被错误设置为用户名，重新生成
    const localUsers = localStorageUtils.getRegisteredUsers();
    correctUserId = (10000000 + localUsers.length).toString();
    console.log('🔧 重新生成用户ID:', correctUserId);
  }

  // 使用formatters确保数据类型正确，匹配SysPatient实体类
  const formattedData = {
    userId: formatters.ensureString(correctUserId), // 🔥 使用正确的用户ID
    patientName: formatters.ensureString(userData.username || userData.patientName), // 患者姓名
    age: formatters.ensureNumber(userData.age, null),
    gender: userData.gender === '男' ? '0' : userData.gender === '女' ? '1' : '2', // 转换为数字格式
    idCard: formatters.ensureString(userData.idCard), // 🔥 身份证字段
    phoneNumber: formatters.ensureString(userData.phone || userData.phoneNumber), // 手机号码
    address: formatters.ensureString(userData.address),
    emergencyContact: formatters.ensureString(userData.emergencyContact),
    emergencyPhone: formatters.ensureString(userData.emergencyPhone),
    status: '0' // 默认状态为正常
    // 其他字段由后端自动生成或处理
  }

  console.log('📤 格式化后的注册数据:', formattedData)

  try {
    const result = await request.post('/system/patient', formattedData)
    console.log('📥 注册响应结果:', result)
    return result
  } catch (error) {
    console.error('❌ 注册请求异常:', error)
    return {
      success: false,
      message: error.message || '注册失败',
      error
    }
  }
}

// 用户登录验证API
export async function validateUser(identifier, password) { // eslint-disable-line no-unused-vars
  console.log('🔐 验证用户登录...')
  console.log('🔍 登录标识符:', identifier)

  try {
    // 查询用户信息列表 - 5.X版本的响应格式
    const result = await request.get('/system/patient/list', {
      pageNum: 1,
      pageSize: 100 // 增加查询数量
    })

    console.log('📥 患者列表查询结果:', result)

    // 处理axios响应拦截器包装后的数据
    let users = []
    if (result && result.success) {
      // axios拦截器包装后的格式
      const responseData = result.data
      if (responseData && responseData.rows) {
        users = responseData.rows
      } else if (Array.isArray(responseData)) {
        users = responseData
      }
    } else if (result && result.rows) {
      // 直接的RuoYi响应格式
      users = result.rows
    }

    console.log('👥 找到患者数量:', users.length)

    if (users.length === 0) {
      console.log('⚠️ 患者列表为空，可能是新系统或数据未初始化')
      return {
        success: false,
        message: '系统中暂无患者数据，请先注册',
        isEmpty: true
      }
    }

    // 查找匹配的用户
    const user = users.find(u => {
      const matches = (
        String(u.phoneNumber) === String(identifier) ||
        String(u.userId) === String(identifier) ||
        String(u.patientName) === String(identifier) ||
        String(u.patientId) === String(identifier)
      )
      const isActive = u.status === '0' || u.status === 0 // 正常状态

      console.log('🔍 检查用户:', {
        patientName: u.patientName,
        phoneNumber: u.phoneNumber,
        userId: u.userId,
        patientId: u.patientId,
        status: u.status,
        identifier: identifier,
        matches,
        isActive
      })

      return matches && isActive
    })

    if (user) {
      console.log('✅ 找到匹配用户:', user.patientName)
      // 简化验证：患者端暂时不验证密码，只验证用户存在且状态正常
      return {
        success: true,
        user: user,
        message: '登录成功'
      }
    } else {
      console.log('❌ 未找到匹配的用户')
      return {
        success: false,
        message: '用户不存在或已被禁用，请检查登录信息'
      }
    }
  } catch (error) {
    console.error('❌ 登录验证异常:', error)
    return {
      success: false,
      message: error.message || '登录验证失败，请检查网络连接'
    }
  }
}

// 获取用户信息API
export async function getUserInfo(userId) {
  console.log('👤 获取用户信息...')

  try {
    const result = await request.get(`/system/patient/${userId}`)
    console.log('📥 用户信息响应结果:', result)
    return result
  } catch (error) {
    console.error('❌ 获取用户信息异常:', error)
    return {
      success: false,
      message: error.message || '获取用户信息失败',
      error
    }
  }
}

// 更新用户信息API
export async function updateUserInfo(userId, userData) {
  console.log('👤 更新用户信息...');
  console.log('📤 更新数据:', userData);

  const formattedData = {
    patientId: Number(userId), // 使用patientId作为主键
    userId: String(userData.userId || userId),
    patientName: String(userData.username || userData.patientName || ''),
    age: userData.age ? Number(userData.age) : null,
    gender: userData.gender === '男' ? '0' : userData.gender === '女' ? '1' : '2',
    phoneNumber: String(userData.phone || userData.phoneNumber || ''),
    address: String(userData.address || ''),
    emergencyContact: String(userData.emergencyContact || ''),
    emergencyPhone: String(userData.emergencyPhone || '')
  };

  console.log('📤 格式化后的更新数据:', formattedData);

  const result = await request.put(`/system/patient/${userId}`, formattedData);

  console.log('📥 更新响应结果:', result);
  return result;
}

// 修改密码API
export async function updateUserPassword(userId, passwordData) {
  console.log('🔐 修改用户密码...')
  console.log('📤 密码修改数据:', {
    userId,
    hasCurrentPassword: !!passwordData.currentPassword,
    hasNewPassword: !!passwordData.newPassword
  })

  try {
    // 首先验证当前密码
    const userResult = await getUserInfo(userId)
    if (!userResult.success) {
      return {
        success: false,
        message: '获取用户信息失败'
      }
    }

    // 验证当前密码（这里简化处理，实际应该在后端验证）
    if (userResult.data && userResult.data.password !== passwordData.currentPassword) {
      return {
        success: false,
        message: '当前密码错误'
      }
    }

    // 更新密码
    const formattedData = {
      userId: formatters.ensureString(userId),
      password: formatters.ensureString(passwordData.newPassword)
    }

    console.log('📤 格式化后的密码数据:', {
      userId: formattedData.userId,
      hasPassword: !!formattedData.password
    })

    const result = await request.post('/system/info/updatePassword', formattedData)
    console.log('📥 密码修改响应结果:', result)
    return result

  } catch (error) {
    console.error('❌ 密码修改异常:', error)
    return {
      success: false,
      message: error.message || '密码修改失败',
      error
    }
  }
}

// 检查服务器连接状态
export async function checkServerStatus() {
  try {
    const result = await request.get('/actuator/health')
    return {
      success: result.success,
      status: result.code || 200,
      message: result.success ? '服务器连接正常' : '服务器连接异常',
      data: result.data
    }
  } catch (error) {
    console.error('❌ 服务器状态检查异常:', error)
    return {
      success: false,
      status: 0,
      message: '无法连接到服务器',
      error
    }
  }
}

// 本地存储工具
export const localStorageUtils = {
  // 保存用户信息
  saveUser(userInfo, remember = false) {
    const storage = remember ? localStorage : sessionStorage;
    storage.setItem('currentUser', JSON.stringify(userInfo));
  },

  // 获取用户信息
  getUser() {
    const userStr = localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser');
    return userStr ? JSON.parse(userStr) : null;
  },

  // 清除用户信息
  clearUser() {
    localStorage.removeItem('currentUser');
    sessionStorage.removeItem('currentUser');
  },

  // 检查登录状态
  isLoggedIn() {
    return !!(localStorage.getItem('currentUser') || sessionStorage.getItem('currentUser'));
  },

  // 保存注册用户列表（本地备份）
  saveRegisteredUsers(users) {
    localStorage.setItem('registeredUsers', JSON.stringify(users));
  },

  // 获取注册用户列表
  getRegisteredUsers() {
    return JSON.parse(localStorage.getItem('registeredUsers') || '[]');
  },

  // 清理重复的手机号数据
  cleanDuplicatePhones() {
    const users = this.getRegisteredUsers();
    const phoneSet = new Set();
    const cleanedUsers = [];
    
    users.forEach(user => {
      const phone = user.phone || user.phoneNumber;
      if (phone && !phoneSet.has(phone)) {
        phoneSet.add(phone);
        cleanedUsers.push(user);
      } else if (!phone) {
        cleanedUsers.push(user); // 保留没有手机号的用户
      }
    });
    
    if (cleanedUsers.length !== users.length) {
      console.log(`🧹 清理了 ${users.length - cleanedUsers.length} 个重复手机号的用户`);
      this.saveRegisteredUsers(cleanedUsers);
    }
    
    return cleanedUsers;
  },

  // 检查手机号是否已存在（清理后检查）
  isPhoneExists(phone) {
    const users = this.cleanDuplicatePhones();
    return users.some(u => u.phone === phone || u.phoneNumber === phone);
  },

  // 初始化测试用户（用于开发测试）
  initTestUsers() {
    const existingUsers = this.getRegisteredUsers();
    
    // 如果没有用户数据，添加测试用户
    if (existingUsers.length === 0) {
      const testUsers = [
        {
          userId: '10000000',
          username: '测试用户',
          phone: '13888888888',
          phoneNumber: '13888888888',
          password: '123456',
          age: 25,
          gender: '男',
          idCard: '110101199001011234',
          address: '北京市朝阳区',
          emergencyContact: '紧急联系人',
          emergencyPhone: '13999999999',
          createTime: new Date().toISOString()
        }
      ];
      
      this.saveRegisteredUsers(testUsers);
      console.log('🏗️ 初始化测试用户数据完成');
      return testUsers;
    }
    
    return existingUsers;
  }
};


export const backendAuth = {
  async login(identifier, password, remember = false) {
    console.log('🔄 后端登录模式...');
    console.log('🔍 登录信息:', { identifier, hasPassword: !!password });

    // 只使用后端验证
    const backendResult = await validateUser(identifier, password);

    if (backendResult.success) {
      console.log('✅ 后端验证成功');
      localStorageUtils.saveUser(backendResult.user, remember);
      return {
        success: true,
        user: backendResult.user,
        message: '登录成功'
      };
    }

    // 如果是因为数据库为空导致的失败，提供特殊处理
    if (backendResult.isEmpty) {
      console.log('📝 数据库为空，建议用户注册');
      return {
        success: false,
        message: '系统中暂无用户数据，请先注册账号',
        suggestRegister: true
      };
    }

    console.log('❌ 后端验证失败:', backendResult.message);
    return {
      success: false,
      message: backendResult.message || '登录失败，请检查用户名和密码'
    };
  },

  async register(userData) {
    console.log('🔄 后端注册模式...');
    console.log('📤 注册用户数据:', userData);

    // 使用后端注册
    const backendResult = await registerUser(userData);

    if (backendResult.success) {
      console.log('✅ 后端注册成功:', backendResult);

      // 从响应中获取用户ID - 使用递增ID逻辑
      let userId = null;
      
      // 尝试多种方式获取用户ID
      if (backendResult.data) {
        // 方式1: 直接从data中获取
        if (backendResult.data.userId) {
          userId = String(backendResult.data.userId);
        }
        // 方式2: 从data.data中获取
        else if (backendResult.data.data && backendResult.data.data.userId) {
          userId = String(backendResult.data.data.userId);
        }
      }
      
      // 如果没有获取到用户ID，使用递增逻辑
      if (!userId) {
        const localUsers = localStorageUtils.getRegisteredUsers();
        // 从10000000开始递增
        userId = (10000000 + localUsers.length).toString();
      }

      console.log('🆔 生成的用户ID:', userId);

      return {
        success: true,
        userId: userId,
        message: '注册成功'
      };
    }
    
    console.log('❌ 后端注册失败:', backendResult.message);
    return backendResult;
  }
};

// 混合认证模式（优先后端，失败时本地）
export const hybridAuth = {
  async login(identifier, password, remember = false) {
    console.log('🔄 混合登录模式：优先后端，失败时本地...');
    console.log('🔍 登录信息:', { identifier, hasPassword: !!password });

    // 首先尝试后端验证
    try {
      const backendResult = await validateUser(identifier, password);

      if (backendResult.success) {
        console.log('✅ 后端验证成功');
        localStorageUtils.saveUser(backendResult.user, remember);
        return {
          success: true,
          user: backendResult.user,
          message: '登录成功',
          authMode: 'backend'
        };
      }

      // 如果后端返回数据库为空的错误
      if (backendResult.isEmpty) {
        console.log('📝 后端数据库为空，建议用户注册');
        return {
          success: false,
          message: '系统中暂无用户数据，请先注册账号',
          suggestRegister: true,
          authMode: 'backend-empty'
        };
      }

      console.log('⚠️ 后端验证失败，尝试本地验证...');
    } catch (error) {
      console.log('❌ 后端连接失败，使用本地验证:', error.message);
      console.log('🔍 错误详情:', error);
    }

    // 后端失败时，尝试本地验证
    const localUsers = localStorageUtils.getRegisteredUsers();
    console.log('🔍 本地用户数据:', localUsers);
    console.log('🔍 查找用户标识符:', identifier);
    
    const user = localUsers.find(u => {
      const identifierMatch = (
        String(u.username) === String(identifier) || 
        String(u.phoneNumber) === String(identifier) || 
        String(u.phone) === String(identifier) ||
        String(u.userId) === String(identifier)
      );
      
      console.log('🔍 检查本地用户:', {
        username: u.username,
        phoneNumber: u.phoneNumber,
        phone: u.phone,
        userId: u.userId,
        identifier: identifier,
        identifierMatch: identifierMatch,
        passwordMatch: u.password === password
      });
      
      return identifierMatch && u.password === password;
    });

    if (user) {
      console.log('✅ 本地验证成功');
      localStorageUtils.saveUser(user, remember);
      return {
        success: true,
        user: user,
        message: '登录成功（本地模式）',
        authMode: 'local'
      };
    }

    console.log('❌ 本地验证也失败 - 检查是否有注册用户');
    
    // 如果本地没有任何注册用户，建议注册
    if (localUsers.length === 0) {
      return {
        success: false,
        message: '网络连接失败且本地无用户数据，请在网络恢复后重试或注册新账号',
        suggestRegister: true,
        authMode: 'no-local-data'
      };
    }
    
    // 如果有用户但没找到匹配的，可能是用户名或密码错误
    return {
      success: false,
      message: '用户名或密码错误',
      authMode: 'failed'
    };
  },

  async register(userData) {
    console.log('🔄 混合注册模式：优先后端注册...');
    
    // 首先尝试后端注册
    try {
      const backendResult = await registerUser(userData);
      
      if (backendResult.success) {
        console.log('✅ 后端注册成功');
        
        // 确保返回正确的用户ID
        if (!backendResult.userId) {
          // 使用递增ID逻辑
          let userId = null;
          
          if (backendResult.data) {
            if (backendResult.data.userId) {
              userId = String(backendResult.data.userId);
            } else if (backendResult.data.data && backendResult.data.data.userId) {
              userId = String(backendResult.data.data.userId);
            }
          }
          
          if (!userId) {
            const localUsers = localStorageUtils.getRegisteredUsers();
            // 从10000000开始递增
            userId = (10000000 + localUsers.length).toString();
          }
          
          backendResult.userId = userId;
          console.log('🆔 混合模式生成的用户ID:', userId);
        }
        
        return backendResult;
      }
      
      console.log('⚠️ 后端注册失败，尝试本地注册...');
    } catch (error) {
      console.log('❌ 后端连接失败，使用本地注册:', error.message);
    }
    
    // 后端失败时，使用本地注册
    console.log('⚠️ 使用本地注册作为备用方案');
    
    // 先清理重复数据
    const localUsers = localStorageUtils.cleanDuplicatePhones();
    
    // 检查重复（使用清理后的数据）
    const idCardExists = localUsers.some(u => u.idCard === userData.idCard);
    const phoneExists = localStorageUtils.isPhoneExists(userData.phone || userData.phoneNumber);
    
    if (idCardExists) {
      return { success: false, message: '身份证号已被注册' };
    }
    
    if (phoneExists) {
      return { success: false, message: '手机号已被注册' };
    }
    
    // 创建新用户 - 使用递增ID逻辑
    const userId = (10000000 + localUsers.length).toString();
    
    const newUser = {
      ...userData,
      userId: userId,
      createdAt: new Date().toISOString()
    };
    
    localUsers.push(newUser);
    localStorageUtils.saveRegisteredUsers(localUsers);
    
    console.log('🆔 本地注册生成的用户ID:', userId);
    
    return {
      success: true,
      userId: userId,
      message: '注册成功（本地模式）'
    };
  }
};
