<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词语归类训练 - 注意力评估系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f8f9fa;
            min-height: 100vh;
            color: #2c3e50;
            overflow-x: hidden;
            line-height: 1.6;
        }

        .game-wrapper {
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            position: relative;
        }

        .background-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .bubble {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: bubbleFloat 8s infinite ease-in-out;
        }

        @keyframes bubbleFloat {
            0%, 100% { transform: translateY(0px) scale(1); opacity: 0.7; }
            50% { transform: translateY(-30px) scale(1.1); opacity: 1; }
        }

        .game-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 1200px;
            width: 100%;
            border: 1px solid #e9ecef;
            position: relative;
            z-index: 2;
            animation: containerSlide 1s ease-out;
        }

        @keyframes containerSlide {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .game-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .game-title {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: #2c5aa0;
            font-weight: 600;
        }

        @keyframes titleGlow {
            from { filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.5)); }
            to { filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8)); }
        }

        .game-description {
            font-size: 1.2rem;
            color: #6c757d;
            margin-bottom: 20px;
        }

        .stats-bar {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .stat-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #ffd700;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .game-area {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
            margin-bottom: 30px;
        }

        .words-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            border: 2px solid rgba(255, 255, 255, 0.2);
        }

        .section-title {
            font-size: 1.4rem;
            font-weight: bold;
            margin-bottom: 20px;
            color: #ffd700;
            text-align: center;
        }

        .words-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 12px;
            min-height: 200px;
            padding: 15px;
            border: 2px dashed rgba(255, 255, 255, 0.3);
            border-radius: 15px;
            background: rgba(255, 255, 255, 0.05);
        }

        .word-item {
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
            padding: 12px 16px;
            border-radius: 12px;
            font-size: 1rem;
            font-weight: 600;
            cursor: grab;
            transition: all 0.3s ease;
            text-align: center;
            border: 2px solid rgba(255, 255, 255, 0.3);
            user-select: none;
            position: relative;
            overflow: hidden;
            /* 🔥 固定词语块大小 */
            width: 120px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 120px;
            max-width: 120px;
            min-height: 50px;
            max-height: 50px;
            box-sizing: border-box;
        }

        .word-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .word-item:hover::before {
            left: 100%;
        }

        .word-item:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 8px 20px rgba(78, 205, 196, 0.4);
        }

        .word-item:active {
            cursor: grabbing;
            transform: rotate(5deg) scale(0.95);
        }

        .word-item.dragging {
            opacity: 0.7;
            transform: rotate(10deg) scale(1.1);
            z-index: 1000;
        }

        .categories-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .category-box {
            background: #f8f9fa;
            border: 3px solid #2c3e50;
            border-radius: 12px;
            padding: 20px;
            min-height: 200px;
            transition: all 0.3s ease;
            position: relative;
        }

        .category-box.drag-over {
            border-color: #2c5aa0;
            background: rgba(44, 90, 160, 0.1);
            transform: scale(1.02);
        }

        .category-title {
            font-size: 1.2rem;
            font-weight: bold;
            text-align: center;
            margin-bottom: 15px;
            color: #2c5aa0;
            padding: 10px;
            background: rgba(44, 90, 160, 0.1);
            border-radius: 8px;
        }

        .category-items {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            min-height: 120px;
        }



        .game-controls {
            text-align: center;
            margin-top: 30px;
        }

        .btn {
            background: linear-gradient(45deg, #4ade80, #22c55e);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            box-shadow: 0 6px 15px rgba(34, 197, 94, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 20px rgba(34, 197, 94, 0.4);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            background: #6b7280;
        }

        .feedback {
            text-align: center;
            margin: 20px 0;
            font-size: 1.4rem;
            font-weight: bold;
            min-height: 40px;
            animation: feedbackSlide 0.5s ease-in-out;
        }

        @keyframes feedbackSlide {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .correct {
            color: #4ade80;
            animation: successPulse 0.6s ease-in-out;
        }

        .incorrect {
            color: #ff6b6b;
            animation: errorShake 0.6s ease-in-out;
        }

        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        @keyframes errorShake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .level-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #ffd700, #ffed4e);
            color: #333;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            box-shadow: 0 5px 15px rgba(255, 215, 0, 0.3);
            animation: badgeFloat 3s ease-in-out infinite;
        }

        @keyframes badgeFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-8px); }
        }

        @media (max-width: 768px) {
            .game-area {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .game-title {
                font-size: 2.2rem;
            }
            
            .words-grid {
                grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
            }
            
            .categories-section {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="background-animation" id="backgroundAnimation"></div>
    
    <div class="level-badge">
        关卡 <span id="levelDisplay">1</span>
    </div>

    <div class="game-wrapper">
        <div class="game-container">
            <div class="game-header">
                <h1 class="game-title" style="color: #2c5aa0;">🎯 词语归类训练</h1>
                <p class="game-description" style="color: #6c757d;">逻辑思维与分类能力评估训练系统</p>
            </div>

            <div class="stats-bar">
                <div class="stat-item">
                    <div class="stat-value" id="score">0</div>
                    <div class="stat-label">得分</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="streak">0</div>
                    <div class="stat-label">连击</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="accuracy">0%</div>
                    <div class="stat-label">正确率</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value" id="round">1</div>
                    <div class="stat-label">轮次</div>
                </div>
            </div>

            <div class="game-area">
                <div class="words-section">
                    <div class="section-title">📝 待分类词语</div>
                    <div class="words-grid" id="wordsGrid"></div>
                </div>

                <div class="categories-section" id="categoriesSection"></div>
            </div>

            <div class="feedback" id="feedback"></div>

            <div class="game-controls">
                <button class="btn" id="startBtn" onclick="startGame()">🚀 开始游戏</button>
                <button class="btn" id="checkBtn" onclick="checkClassification()" disabled>✅ 检查分类</button>
                <button class="btn" id="nextBtn" onclick="nextRound()" disabled style="display: none;">➡️ 下一题</button>
            </div>
        </div>
    </div>

    <script>
        // 创建背景动画
        function createBackgroundAnimation() {
            const container = document.getElementById('backgroundAnimation');
            for (let i = 0; i < 20; i++) {
                const bubble = document.createElement('div');
                bubble.className = 'bubble';
                bubble.style.left = Math.random() * 100 + '%';
                bubble.style.top = Math.random() * 100 + '%';
                bubble.style.width = bubble.style.height = Math.random() * 40 + 20 + 'px';
                bubble.style.animationDelay = Math.random() * 8 + 's';
                bubble.style.animationDuration = (Math.random() * 4 + 6) + 's';
                container.appendChild(bubble);
            }
        }

        // 游戏状态
        let gameState = {
            score: 0,
            streak: 0,
            level: 1,
            currentRound: 1,
            totalRounds: 10,
            gameActive: false,
            currentWords: [],
            currentCategories: [],
            correctClassification: {},
            userClassification: {},
            totalQuestions: 0,
            correctAnswers: 0
        };

        // 词语数据库
        const wordDatabase = [
            {
                categories: ['动物', '植物', '食物'],
                words: {
                    '动物': ['狗', '猫', '鸟', '鱼', '兔子'],
                    '植物': ['花', '树', '草', '叶子', '根'],
                    '食物': ['苹果', '面包', '牛奶', '鸡蛋', '米饭']
                }
            },
            {
                categories: ['交通工具', '家具', '文具'],
                words: {
                    '交通工具': ['汽车', '飞机', '火车', '自行车', '船'],
                    '家具': ['桌子', '椅子', '床', '沙发', '柜子'],
                    '文具': ['笔', '本子', '橡皮', '尺子', '书包']
                }
            },
            {
                categories: ['颜色', '形状', '数字'],
                words: {
                    '颜色': ['红色', '蓝色', '绿色', '黄色', '紫色'],
                    '形状': ['圆形', '方形', '三角形', '长方形', '椭圆形'],
                    '数字': ['一', '二', '三', '四', '五']
                }
            },
            {
                categories: ['运动', '乐器', '职业'],
                words: {
                    '运动': ['跑步', '游泳', '篮球', '足球', '乒乓球'],
                    '乐器': ['钢琴', '吉他', '小提琴', '鼓', '笛子'],
                    '职业': ['医生', '老师', '警察', '厨师', '司机']
                }
            },
            {
                categories: ['天气', '季节', '时间'],
                words: {
                    '天气': ['晴天', '雨天', '雪天', '阴天', '风天'],
                    '季节': ['春天', '夏天', '秋天', '冬天', '雨季'],
                    '时间': ['早上', '中午', '下午', '晚上', '夜晚']
                }
            }
        ];

        // 生成当前轮次的词语和分类
        function generateRound() {
            const roundIndex = (gameState.currentRound - 1) % wordDatabase.length;
            const roundData = wordDatabase[roundIndex];

            gameState.currentCategories = [...roundData.categories];
            gameState.currentWords = [];
            gameState.correctClassification = {};
            gameState.userClassification = {};

            // 从每个分类中随机选择词语
            roundData.categories.forEach(category => {
                const categoryWords = roundData.words[category];
                const selectedWords = categoryWords.sort(() => 0.5 - Math.random()).slice(0, 3);
                selectedWords.forEach(word => {
                    gameState.currentWords.push(word);
                    gameState.correctClassification[word] = category;
                });
            });

            // 打乱词语顺序
            gameState.currentWords.sort(() => 0.5 - Math.random());
        }

        // 开始游戏
        function startGame() {
            gameState.gameActive = true;
            gameState.score = 0;
            gameState.streak = 0;
            gameState.level = 1;
            gameState.currentRound = 1;
            gameState.totalQuestions = 0;
            gameState.correctAnswers = 0;

            document.getElementById('startBtn').disabled = true;
            document.getElementById('checkBtn').disabled = false;

            nextRound();
        }

        // 下一轮
        function nextRound() {
            if (gameState.currentRound > gameState.totalRounds) {
                endGame();
                return;
            }

            generateRound();
            renderWords();
            renderCategories();

            document.getElementById('feedback').textContent = '';
            document.getElementById('nextBtn').style.display = 'none';
            document.getElementById('checkBtn').disabled = false;

            updateDisplay();
        }

        // 渲染词语
        function renderWords() {
            const wordsGrid = document.getElementById('wordsGrid');
            wordsGrid.innerHTML = '';

            // 添加拖拽区域事件
            wordsGrid.addEventListener('dragover', handleDragOver);
            wordsGrid.addEventListener('drop', handleWordsGridDrop);

            gameState.currentWords.forEach(word => {
                const wordElement = document.createElement('div');
                wordElement.className = 'word-item';
                wordElement.textContent = word;
                wordElement.draggable = true;
                wordElement.dataset.word = word;

                // 添加拖拽事件
                wordElement.addEventListener('dragstart', handleDragStart);
                wordElement.addEventListener('dragend', handleDragEnd);

                wordsGrid.appendChild(wordElement);
            });
        }

        // 渲染分类区域
        function renderCategories() {
            const categoriesSection = document.getElementById('categoriesSection');
            categoriesSection.innerHTML = '';

            gameState.currentCategories.forEach(category => {
                const categoryBox = document.createElement('div');
                categoryBox.className = 'category-box';
                categoryBox.dataset.category = category;

                const categoryTitle = document.createElement('div');
                categoryTitle.className = 'category-title';
                categoryTitle.textContent = category;

                const categoryItems = document.createElement('div');
                categoryItems.className = 'category-items';

                categoryBox.appendChild(categoryTitle);
                categoryBox.appendChild(categoryItems);

                // 添加拖拽事件
                categoryBox.addEventListener('dragover', handleDragOver);
                categoryBox.addEventListener('drop', handleDrop);
                categoryBox.addEventListener('dragleave', handleDragLeave);

                categoriesSection.appendChild(categoryBox);
            });
        }

        // 拖拽事件处理
        function handleDragStart(e) {
            e.dataTransfer.setData('text/plain', e.target.dataset.word);
            e.dataTransfer.setData('source', e.target.closest('.words-grid') ? 'grid' : 'category');
            e.target.classList.add('dragging');
        }

        function handleDragEnd(e) {
            e.target.classList.remove('dragging');
        }

        function handleDragOver(e) {
            e.preventDefault();
            if (e.target.closest('.category-box')) {
                e.target.closest('.category-box').classList.add('drag-over');
            }
        }

        function handleDragLeave(e) {
            if (e.target.classList.contains('category-box')) {
                e.target.classList.remove('drag-over');
            }
        }

        function handleDrop(e) {
            e.preventDefault();
            const word = e.dataTransfer.getData('text/plain');
            const source = e.dataTransfer.getData('source');
            const categoryBox = e.target.closest('.category-box');

            if (categoryBox) {
                const category = categoryBox.dataset.category;
                const categoryItems = categoryBox.querySelector('.category-items');

                // 移除原来的词语元素
                const originalElement = document.querySelector(`[data-word="${word}"]`);
                if (originalElement) {
                    originalElement.remove();
                }

                // 如果来源是分类，需要从用户分类记录中移除
                if (source === 'category') {
                    delete gameState.userClassification[word];
                }

                // 在分类中添加词语，保持原始样式
                const wordItem = document.createElement('div');
                wordItem.className = 'word-item';
                wordItem.textContent = word;
                wordItem.dataset.word = word;

                // 添加拖拽功能让词语可以被拖出
                wordItem.draggable = true;
                wordItem.addEventListener('dragstart', function(e) {
                    e.dataTransfer.setData('text/plain', word);
                    e.dataTransfer.setData('source', 'category');
                    wordItem.classList.add('dragging');
                });

                wordItem.addEventListener('dragend', function(e) {
                    wordItem.classList.remove('dragging');
                });

                // 双击返回功能
                wordItem.addEventListener('dblclick', function() {
                    returnWordToGrid(word);
                });

                categoryItems.appendChild(wordItem);

                // 更新用户分类记录
                gameState.userClassification[word] = category;

                categoryBox.classList.remove('drag-over');

                // 添加放置动画效果
                wordItem.style.animation = 'dropAnimation 0.5s ease-out';
            }
        }

        function handleWordsGridDrop(e) {
            e.preventDefault();
            const word = e.dataTransfer.getData('text/plain');
            const source = e.dataTransfer.getData('source');

            // 只处理从分类拖回来的词语
            if (source === 'category') {
                returnWordToGrid(word);
            }
        }

        // 将词语返回到网格
        function returnWordToGrid(word) {
            // 从分类中移除（现在分类中的词语也使用word-item样式）
            const wordItem = document.querySelector(`.category-items .word-item[data-word="${word}"]`);
            if (wordItem) {
                wordItem.remove();
            }

            // 从用户分类记录中移除
            delete gameState.userClassification[word];

            // 添加回词语网格
            const wordsGrid = document.getElementById('wordsGrid');
            const wordElement = document.createElement('div');
            wordElement.className = 'word-item';
            wordElement.textContent = word;
            wordElement.draggable = true;
            wordElement.dataset.word = word;

            // 添加拖拽事件
            wordElement.addEventListener('dragstart', handleDragStart);
            wordElement.addEventListener('dragend', handleDragEnd);

            wordsGrid.appendChild(wordElement);

            // 添加返回动画效果
            wordElement.style.animation = 'returnAnimation 0.5s ease-out';
        }

        // 检查分类
        function checkClassification() {
            gameState.totalQuestions++;

            let correctCount = 0;
            let totalCount = gameState.currentWords.length;

            // 检查每个词语的分类是否正确
            gameState.currentWords.forEach(word => {
                const userCategory = gameState.userClassification[word];
                const correctCategory = gameState.correctClassification[word];
                if (userCategory && userCategory === correctCategory) {
                    correctCount++;
                }
            });

            const feedback = document.getElementById('feedback');

            if (correctCount === totalCount) {
                const points = 20 * gameState.currentRound;
                gameState.score += points;
                gameState.streak++;
                gameState.correctAnswers++;
                feedback.textContent = `🎉 完美分类！+${points}分`;
                feedback.className = 'feedback correct';
                createSuccessEffect();
            } else {
                gameState.streak = 0;
                feedback.textContent = `❌ 分类错误！正确 ${correctCount}/${totalCount} 个`;
                feedback.className = 'feedback incorrect';
            }

            gameState.currentRound++;

            document.getElementById('checkBtn').disabled = true;
            document.getElementById('nextBtn').style.display = 'inline-block';
            document.getElementById('nextBtn').disabled = false;

            updateDisplay();
        }

        // 创建成功效果
        function createSuccessEffect() {
            const container = document.querySelector('.game-container');
            for (let i = 0; i < 15; i++) {
                const star = document.createElement('div');
                star.style.position = 'absolute';
                star.style.left = Math.random() * 100 + '%';
                star.style.top = Math.random() * 100 + '%';
                star.style.width = star.style.height = Math.random() * 8 + 4 + 'px';
                star.style.background = '#ffd700';
                star.style.borderRadius = '50%';
                star.style.pointerEvents = 'none';
                star.style.animation = 'starBurst 1.5s ease-out forwards';
                container.appendChild(star);

                setTimeout(() => {
                    star.remove();
                }, 1500);
            }
        }

        // 更新显示
        function updateDisplay() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('streak').textContent = gameState.streak;
            document.getElementById('levelDisplay').textContent = gameState.level;
            document.getElementById('round').textContent = gameState.currentRound - 1;

            const accuracy = gameState.totalQuestions > 0 ?
                Math.round((gameState.correctAnswers / gameState.totalQuestions) * 100) : 0;
            document.getElementById('accuracy').textContent = accuracy + '%';
        }

        // 结束游戏
        function endGame() {
            gameState.gameActive = false;

            const accuracy = gameState.totalQuestions > 0 ?
                Math.round((gameState.correctAnswers / gameState.totalQuestions) * 100) : 0;

            document.getElementById('feedback').textContent = `🏆 游戏结束！最终得分: ${gameState.score}分`;
            document.getElementById('feedback').className = 'feedback correct';

            document.getElementById('startBtn').disabled = false;
            document.getElementById('checkBtn').disabled = true;
            document.getElementById('nextBtn').style.display = 'none';

            createSuccessEffect();

            if (window.opener) {
                window.opener.postMessage({
                    type: 'gameEnd',
                    score: gameState.score
                }, '*');
            }
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes dropAnimation {
                0% { transform: scale(0) rotate(180deg); opacity: 0; }
                100% { transform: scale(1) rotate(0deg); opacity: 1; }
            }

            @keyframes returnAnimation {
                0% { transform: scale(1.2) rotate(-10deg); opacity: 0.7; }
                100% { transform: scale(1) rotate(0deg); opacity: 1; }
            }

            @keyframes starBurst {
                0% { transform: scale(0) rotate(0deg); opacity: 1; }
                100% { transform: scale(1) rotate(360deg) translateY(-100px); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            createBackgroundAnimation();
            updateDisplay();
        });
    </script>
</body>
</html>
