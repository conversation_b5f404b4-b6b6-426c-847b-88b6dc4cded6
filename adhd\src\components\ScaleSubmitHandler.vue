<template>
  <div>
    <!-- 询问是否提供详细信息的弹窗 -->
    <el-dialog
      title="完善诊断信息"
      :visible.sync="showConfirmDialog"
      width="400px"
      :close-on-click-modal="false"
      center
    >
      <div style="text-align: center;">
        <i class="el-icon-info" style="font-size: 40px; color: #409EFF; margin-bottom: 15px;"></i>
        <h3>量表评测已完成</h3>
        <p>为了获得更准确的AI分析，建议提供一些补充信息：</p>
        <ul style="text-align: left; margin: 15px 0;">
          <li>家庭基本情况</li>
          <li>孩子发病史</li>
          <li>生活习惯</li>
          <li>行为表现</li>
        </ul>
        <p style="font-size: 12px; color: #999;">信息仅用于AI分析，严格保密</p>
      </div>
      
      <div slot="footer">
        <el-button @click="skipDetailInfo">跳过，直接分析</el-button>
        <el-button type="primary" @click="showDetailForm">提供详细信息</el-button>
      </div>
    </el-dialog>

    <!-- 详细信息收集弹窗 -->
    <el-dialog
      title="补充详细信息"
      :visible.sync="showDetailDialog"
      width="70%"
      :close-on-click-modal="false"
    >
      <el-form :model="detailForm" label-width="100px">
        <h4>家庭基本情况</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="家庭结构">
              <el-select v-model="detailForm.familyStructure" placeholder="请选择">
                <el-option label="双亲家庭" value="both_parents"></el-option>
                <el-option label="单亲家庭" value="single_parent"></el-option>
                <el-option label="隔代抚养" value="grandparents"></el-option>
                <el-option label="其他" value="other"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="家庭关系">
              <el-rate v-model="detailForm.familyRelation" show-text :texts="['很差', '较差', '一般', '良好', '很好']"></el-rate>
            </el-form-item>
          </el-col>
        </el-row>
        
        <h4>发病史</h4>
        <el-form-item label="症状开始时间">
          <el-select v-model="detailForm.symptomStart" placeholder="请选择">
            <el-option label="3岁以前" value="before_3"></el-option>
            <el-option label="3-6岁" value="3_to_6"></el-option>
            <el-option label="6-12岁" value="6_to_12"></el-option>
            <el-option label="12岁以后" value="after_12"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="家族病史">
          <el-checkbox-group v-model="detailForm.familyHistory">
            <el-checkbox label="ADHD">ADHD</el-checkbox>
            <el-checkbox label="抑郁症">抑郁症</el-checkbox>
            <el-checkbox label="焦虑症">焦虑症</el-checkbox>
            <el-checkbox label="其他精神疾病">其他精神疾病</el-checkbox>
            <el-checkbox label="无">无</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        
        <h4>生活习惯</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="睡眠时间">
              <el-select v-model="detailForm.sleepHours">
                <el-option label="少于8小时" value="<8"></el-option>
                <el-option label="8-10小时" value="8-10"></el-option>
                <el-option label="10小时以上" value=">10"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="运动频率">
              <el-select v-model="detailForm.exercise">
                <el-option label="很少" value="rarely"></el-option>
                <el-option label="偶尔" value="sometimes"></el-option>
                <el-option label="经常" value="often"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="屏幕时间">
              <el-select v-model="detailForm.screenTime">
                <el-option label="少于2小时" value="<2"></el-option>
                <el-option label="2-4小时" value="2-4"></el-option>
                <el-option label="4小时以上" value=">4"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <h4>行为表现</h4>
        <el-form-item label="主要问题">
          <el-checkbox-group v-model="detailForm.mainProblems">
            <el-checkbox label="注意力不集中">注意力不集中</el-checkbox>
            <el-checkbox label="多动冲动">多动冲动</el-checkbox>
            <el-checkbox label="情绪问题">情绪问题</el-checkbox>
            <el-checkbox label="学习困难">学习困难</el-checkbox>
            <el-checkbox label="社交问题">社交问题</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="其他说明">
          <el-input v-model="detailForm.otherInfo" type="textarea" :rows="3" placeholder="请描述其他重要信息"></el-input>
        </el-form-item>
      </el-form>
      
      <div slot="footer">
        <el-button @click="showDetailDialog = false">取消</el-button>
        <el-button type="primary" @click="submitWithDetail">提交并分析</el-button>
      </div>
    </el-dialog>

    <!-- AI分析中 -->
    <el-dialog
      title="AI分析中"
      :visible.sync="showAnalyzing"
      width="350px"
      :close-on-click-modal="false"
      :show-close="false"
      center
    >
      <div style="text-align: center; padding: 20px 0;">
        <i class="el-icon-loading" style="font-size: 40px; color: #409EFF; animation: rotate 2s linear infinite;"></i>
        <h3>AI正在分析中...</h3>
        <p>{{ analyzeStatus }}</p>
      </div>
    </el-dialog>

    <!-- 分析结果 -->
    <el-dialog
      title="AI分析结果"
      :visible.sync="showResult"
      width="80%"
    >
      <div v-if="analysisResult">
        <div class="result-header">
          <h3>{{ patientName }} 的AI分析报告</h3>
          <el-tag :type="getRiskType(analysisResult.riskLevel)" size="large">
            {{ analysisResult.riskLevel }}
          </el-tag>
        </div>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-card title="综合评分">
              <div style="text-align: center;">
                <div style="font-size: 36px; font-weight: bold; color: #409EFF;">
                  {{ analysisResult.totalScore }}
                </div>
                <div>总分: {{ analysisResult.totalScore }}/100</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="16">
            <el-card title="AI分析摘要">
              <ul>
                <li v-for="point in analysisResult.keyPoints" :key="point">{{ point }}</li>
              </ul>
            </el-card>
          </el-col>
        </el-row>

        <el-card title="题目详细分析" style="margin-top: 20px;">
          <div v-for="(item, index) in analysisResult.questionAnalysis" :key="index" class="question-analysis">
            <div class="question-header">
              <span><strong>Q{{ index + 1 }}:</strong> {{ item.question }}</span>
              <el-tag :type="getQuestionRiskType(item.riskLevel)" size="mini">{{ item.riskLevel }}</el-tag>
            </div>
            <p><strong>回答:</strong> {{ item.answer }} <span style="color: #409EFF;">({{ item.score }}分)</span></p>
            <p style="color: #666; font-style: italic;"><strong>AI解读:</strong> {{ item.aiAnalysis }}</p>
          </div>
        </el-card>

        <el-card title="诊断建议" style="margin-top: 20px;">
          <el-alert :title="analysisResult.diagnosis" type="info" :closable="false"></el-alert>
          <div style="margin-top: 15px;">
            <h4>建议措施:</h4>
            <ul>
              <li v-for="suggestion in analysisResult.suggestions" :key="suggestion">{{ suggestion }}</li>
            </ul>
          </div>
        </el-card>
      </div>
      
      <div slot="footer">
        <el-button @click="showResult = false">关闭</el-button>
        <el-button type="primary" @click="sendToDoctor">推送给医生</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'ScaleSubmitHandler',
  props: {
    visible: Boolean,
    patientName: String,
    scaleData: Object // 包含题目、答案、分数的完整量表数据
  },
  
  data() {
    return {
      showConfirmDialog: false,
      showDetailDialog: false,
      showAnalyzing: false,
      showResult: false,
      analyzeStatus: '正在分析量表结果...',
      
      detailForm: {
        familyStructure: '',
        familyRelation: 0,
        symptomStart: '',
        familyHistory: [],
        sleepHours: '',
        exercise: '',
        screenTime: '',
        mainProblems: [],
        otherInfo: ''
      },
      
      analysisResult: null
    }
  },
  
  watch: {
    visible(val) {
      if (val) {
        this.showConfirmDialog = true
      }
    }
  },
  
  methods: {
    // 跳过详细信息，直接分析
    skipDetailInfo() {
      this.showConfirmDialog = false
      this.startAIAnalysis(false)
    },
    
    // 显示详细信息表单
    showDetailForm() {
      this.showConfirmDialog = false
      this.showDetailDialog = true
    },
    
    // 提交详细信息并分析
    submitWithDetail() {
      this.showDetailDialog = false
      this.startAIAnalysis(true)
    },
    
    // 开始AI分析
    async startAIAnalysis(hasDetailInfo) {
      this.showAnalyzing = true
      
      // 准备发送给后端的完整数据
      const analysisData = {
        patientName: this.patientName,
        scaleData: {
          scaleType: this.scaleData.scaleType,
          totalScore: this.scaleData.totalScore,
          // 包含每个题目的详细信息
          questions: this.scaleData.questions.map((q, index) => ({
            questionId: q.id,
            questionText: q.text,
            answer: this.scaleData.answers[index],
            score: this.scaleData.scores[index]
          }))
        },
        hasDetailInfo,
        detailInfo: hasDetailInfo ? this.detailForm : null,
        timestamp: new Date().toISOString()
      }
      
      try {
        // 模拟分析过程
        const steps = [
          '正在分析量表结果...',
          '正在分析每道题目...',
          hasDetailInfo ? '正在综合详细信息...' : '正在评估风险等级...',
          '正在生成诊断建议...',
          '分析完成！'
        ]
        
        for (let i = 0; i < steps.length; i++) {
          this.analyzeStatus = steps[i]
          await new Promise(resolve => setTimeout(resolve, 1000))
        }
        
        // 这里调用真实的AI分析API
        // const response = await this.$http.post('/api/ai-analysis', analysisData)
        
        // 模拟AI分析结果
        this.analysisResult = this.generateAnalysisResult(analysisData)
        
        this.showAnalyzing = false
        this.showResult = true
        
      } catch (error) {
        this.showAnalyzing = false
        this.$message.error('AI分析失败: ' + error.message)
      }
    },
    
    // 生成模拟的AI分析结果
    generateAnalysisResult(data) {
      const totalScore = data.scaleData.totalScore
      let riskLevel = '低风险'
      if (totalScore >= 70) riskLevel = '高风险'
      else if (totalScore >= 50) riskLevel = '中风险'
      
      return {
        totalScore,
        riskLevel,
        keyPoints: [
          `量表总分${totalScore}分，${riskLevel}`,
          '存在注意力不集中问题',
          data.hasDetailInfo ? '结合详细信息，建议专业评估' : '建议提供更多信息以获得准确诊断'
        ],
        questionAnalysis: data.scaleData.questions.map((q, index) => ({
          question: q.questionText,
          answer: q.answer,
          score: q.score,
          riskLevel: q.score >= 3 ? '高风险' : q.score >= 2 ? '中风险' : '正常',
          aiAnalysis: `基于此题回答"${q.answer}"，AI分析认为在${q.questionText.substring(0, 10)}...方面${q.score >= 3 ? '存在明显问题' : q.score >= 2 ? '需要关注' : '表现正常'}`
        })),
        diagnosis: totalScore >= 70 ? '疑似ADHD，建议专业诊断' : totalScore >= 50 ? '存在注意力问题，建议观察' : '暂未发现明显问题',
        suggestions: [
          '建议到专业医疗机构进一步评估',
          '注意改善生活作息',
          '加强家庭支持和理解',
          data.hasDetailInfo ? '根据提供的详细信息，建议重点关注家庭环境因素' : '建议提供更多详细信息'
        ]
      }
    },
    
    // 推送给医生
    sendToDoctor() {
      this.$confirm('确认将AI分析结果推送给医生？', '确认', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        // 发送给医生的API调用
        // this.$http.post('/api/send-to-doctor', this.analysisResult)
        this.$message.success('已推送给医生，医生将及时查看')
        this.showResult = false
        this.$emit('analysis-complete', this.analysisResult)
      })
    },
    
    getRiskType(level) {
      if (level.includes('高')) return 'danger'
      if (level.includes('中')) return 'warning'
      return 'success'
    },
    
    getQuestionRiskType(level) {
      if (level.includes('高')) return 'danger'
      if (level.includes('中')) return 'warning'
      return 'success'
    }
  }
}
</script>

<style scoped>
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.question-analysis {
  border-bottom: 1px solid #eee;
  padding: 15px 0;
}

.question-analysis:last-child {
  border-bottom: none;
}

.question-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}
</style>
