<?xml version="1.0" encoding="UTF-8"?>
<!--
 Copyright (c) 2014 <PERSON> <<EMAIL>>

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.
-->

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <version>1.4.2-SNAPSHOT</version>
    <groupId>com.github.mikephil</groupId>
    <artifactId>MPAndroidChart</artifactId>
    <name>MPAndroidChart</name>
    <description>A simple Android chart view/graph view library, supporting line- bar- and piecharts as well as scaling, dragging and animations</description>
    <url>https://github.com/PhilJay/MPAndroidChart</url>
    <packaging>aar</packaging>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <build>
        <sourceDirectory>src</sourceDirectory>
        <plugins>
            <plugin>
                <groupId>com.jayway.maven.plugins.android.generation2</groupId>
                <artifactId>android-maven-plugin</artifactId>
                <version>3.9.0-rc.2</version>
                <extensions>true</extensions>
                <configuration>
                    <!--<sdk>-->
                        <!--<path>${env.ANDROID_HOME}</path>-->
                        <!--<platform>16</platform>-->
                    <!--</sdk>-->
                    <undeployBeforeDeploy>true</undeployBeforeDeploy>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <dependencies>
        <dependency>
            <groupId>com.google.android</groupId>
            <artifactId>android</artifactId>
            <version>2.3.3</version>
            <scope>provided</scope>
        </dependency>
    </dependencies>


    <issueManagement>
        <url>https://github.com/PhilJay/MPAndroidChart/issues</url>
        <system>GitHub Issues</system>
    </issueManagement>

    <licenses>
        <license>
            <name>Apache License Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <scm>
        <url>https://github.com/PhilJay/MPAndroidChart</url>
        <connection>scm:git:git://github.com/PhilJay/MPAndroidChart.git</connection>
        <developerConnection>scm:git:**************:PhilJay/MPAndroidChart.git</developerConnection>
    </scm>

    <developers>
        <developer>
            <name>Philipp Jahoda</name>
            <email><EMAIL></email>
            <url>http://stackoverflow.com/users/1590502/philipp-jahoda</url>
            <id>PhilJay</id>
        </developer>
    </developers>
</project>
