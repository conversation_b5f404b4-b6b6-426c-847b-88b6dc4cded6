-- 用户信息表
CREATE TABLE users (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_id VARCHAR(50) UNIQUE NOT NULL COMMENT '用户唯一标识',
    username VARCHAR(100) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    
    -- 基本信息
    age INT NOT NULL COMMENT '年龄',
    gender ENUM('male', 'female', 'other') NOT NULL COMMENT '性别',
    
    -- 状态信息
    status ENUM('active', 'inactive', 'banned') DEFAULT 'active' COMMENT '账号状态',
    email_verified BOOLEAN DEFAULT FALSE COMMENT '邮箱是否验证',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    
    -- 索引
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_user_id (user_id),
    INDEX idx_status (status)
);

-- 插入示例用户数据
INSERT INTO users (user_id, username, email, password_hash, age, gender) VALUES
('user_demo_001', 'demo_user', '<EMAIL>', '$2b$10$example_hash', 25, 'male'),
('user_admin_001', 'admin', '<EMAIL>', '$2b$10$example_hash', 30, 'female');
