{"doc": " 用户对象导出VO\n\n <AUTHOR> Li\n", "fields": [{"name": "userId", "doc": " 用户ID\n"}, {"name": "userName", "doc": " 用户账号\n"}, {"name": "nick<PERSON><PERSON>", "doc": " 用户昵称\n"}, {"name": "email", "doc": " 用户邮箱\n"}, {"name": "phonenumber", "doc": " 手机号码\n"}, {"name": "sex", "doc": " 用户性别\n"}, {"name": "status", "doc": " 帐号状态（0正常 1停用）\n"}, {"name": "loginIp", "doc": " 最后登录IP\n"}, {"name": "loginDate", "doc": " 最后登录时间\n"}, {"name": "deptName", "doc": " 部门名称\n"}, {"name": "leader<PERSON><PERSON>", "doc": " 负责人\n"}], "enumConstants": [], "methods": [], "constructors": []}