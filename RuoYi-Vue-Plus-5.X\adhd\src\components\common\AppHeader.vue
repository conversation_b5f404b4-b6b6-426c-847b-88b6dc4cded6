<template>
  <header class="app-header">
    <div class="logo">
      <i class="fas fa-heartbeat"></i>
      <span>HealthIt</span>
    </div>
    <div class="user-actions">
      <!-- 如果用户已登录，显示个人中心 -->
      <UserProfile v-if="isLoggedIn" />

      <!-- 如果用户未登录，显示登录按钮 -->
      <div v-else class="login-prompt">
        <button @click="goToLogin" class="login-btn">
          <i class="fas fa-sign-in-alt"></i>
          登录
        </button>
      </div>
    </div>
  </header>
</template>

<script>
import UserProfile from './UserProfile.vue'
import { localStorageUtils } from '@/api/ruoyiAPI.js'

export default {
  name: 'AppHeader',
  components: {
    UserProfile
  },
  props: {
    currentRoute: {
      type: String,
      default: 'home'
    }
  },
  data() {
    return {
      searchQuery: ''
    }
  },

  computed: {
    isLoggedIn() {
      // 使用统一的登录状态检查
      return localStorageUtils.isLoggedIn()
    }
  },

  methods: {
    onSearch() {
      this.$emit('search', this.searchQuery);
    },
    handleSearch() {
      if (this.searchQuery.trim()) {
        this.$emit('search', this.searchQuery);
      }
    },

    goToLogin() {
      this.$router.push('/login');
    }
  }
}
</script>

<style scoped>
.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 25px;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  height: var(--header-height, 60px);
}

.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color, #4e73df);
}

.logo i {
  font-size: 24px;
  margin-right: 10px;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #f1f2f6;
  border-radius: 20px;
  padding: 5px 15px;
  width: 300px;
  transition: all var(--transition-speed, 0.3s);
}

.search-bar:focus-within {
  box-shadow: 0 0 0 2px var(--primary-light, #e8efff);
}

.search-bar input {
  border: none;
  background: none;
  outline: none;
  width: 100%;
  padding: 8px;
  font-size: 14px;
}

.search-bar button {
  background: none;
  border: none;
  color: var(--primary-color, #4e73df);
  cursor: pointer;
  transition: transform var(--transition-speed, 0.3s);
}

.search-bar button:hover {
  transform: scale(1.1);
}

.user-actions {
  display: flex;
  align-items: center;
}

.login-prompt {
  display: flex;
  align-items: center;
}

.login-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.login-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.actions {
  display: flex;
  margin-right: 20px;
}

.icon-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 18px;
  margin-left: 15px;
  position: relative;
  cursor: pointer;
  transition: color var(--transition-speed, 0.3s);
}

.icon-btn:hover {
  color: var(--primary-color, #4e73df);
}

.badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--danger-color, #e74a3b);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.username {
  margin-right: 10px;
  font-weight: 500;
}

.avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-light, #e8efff);
  transition: transform var(--transition-speed, 0.3s);
}

.avatar:hover {
  transform: scale(1.05);
}

@media (max-width: 768px) {
  .app-header {
    flex-wrap: wrap;
    padding: 10px 15px;
  }
  
  .search-bar {
    order: 3;
    width: 100%;
    margin: 10px 0 0;
  }
  
  .user-actions {
    width: 100%;
    justify-content: space-between;
  }
}
</style>
