/**
 * 患者信息管理API
 * 统一管理患者相关的后端API调用
 */

// API基础配置
const API_BASE_URL = 'http://localhost:80'
const PATIENT_API_PREFIX = '/system/patient'

/**
 * 患者信息API服务
 */
export const patientAPI = {
  /**
   * 获取患者信息列表
   * @param {Object} params 查询参数
   * @returns {Promise} API响应
   */
  async getPatientList(params = {}) {
    console.log('📋 获取患者信息列表:', params)
    
    try {
      const queryParams = new URLSearchParams({
        pageNum: params.pageNum || 1,
        pageSize: params.pageSize || 10,
        ...params
      }).toString()

      const response = await fetch(`${API_BASE_URL}${PATIENT_API_PREFIX}/list?${queryParams}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      })

      if (response.ok) {
        const result = await response.json()
        console.log('📥 患者列表响应:', result)
        return {
          success: result.code === 200,
          data: result.rows || [],
          total: result.total || 0,
          message: result.msg || '获取成功'
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('❌ 获取患者列表失败:', error)
      return {
        success: false,
        data: [],
        message: error.message || '获取患者列表失败'
      }
    }
  },

  /**
   * 根据用户ID获取患者信息
   * @param {string} userId 用户ID
   * @returns {Promise} API响应
   */
  async getPatientByUserId(userId) {
    console.log('👤 根据用户ID获取患者信息:', userId)
    
    try {
      const result = await this.getPatientList({ userId, pageSize: 1 })
      
      if (result.success && result.data.length > 0) {
        return {
          success: true,
          data: result.data[0],
          message: '获取患者信息成功'
        }
      } else {
        return {
          success: false,
          data: null,
          message: '未找到患者信息'
        }
      }
    } catch (error) {
      console.error('❌ 获取患者信息失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取患者信息失败'
      }
    }
  },

  /**
   * 获取单个患者详细信息
   * @param {number} patientId 患者ID
   * @returns {Promise} API响应
   */
  async getPatientById(patientId) {
    console.log('🔍 获取患者详细信息:', patientId)
    
    try {
      const response = await fetch(`${API_BASE_URL}${PATIENT_API_PREFIX}/${patientId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      })

      if (response.ok) {
        const result = await response.json()
        console.log('📥 患者详情响应:', result)
        return {
          success: result.code === 200,
          data: result.data,
          message: result.msg || '获取成功'
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('❌ 获取患者详情失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '获取患者详情失败'
      }
    }
  },

  /**
   * 注册新患者
   * @param {Object} patientData 患者数据
   * @returns {Promise} API响应
   */
  async registerPatient(patientData) {
    console.log('📝 注册新患者:', patientData)
    
    try {
      const response = await fetch(`${API_BASE_URL}${PATIENT_API_PREFIX}/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(patientData)
      })

      if (response.ok) {
        const result = await response.json()
        console.log('📥 患者注册响应:', result)
        return {
          success: result.code === 200,
          data: result.data,
          message: result.msg || '注册成功'
        }
      } else {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }
    } catch (error) {
      console.error('❌ 患者注册失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '患者注册失败'
      }
    }
  },

  /**
   * 更新患者信息
   * @param {Object} patientData 患者数据
   * @returns {Promise} API响应
   */
  async updatePatient(patientData) {
    console.log('✏️ 更新患者信息:', patientData)
    
    try {
      const response = await fetch(`${API_BASE_URL}${PATIENT_API_PREFIX}/profile-update`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(patientData)
      })

      if (response.ok) {
        const result = await response.json()
        console.log('📥 患者更新响应:', result)
        return {
          success: result.code === 200,
          data: result.data,
          message: result.msg || '更新成功'
        }
      } else {
        const errorText = await response.text()
        throw new Error(`HTTP ${response.status}: ${errorText}`)
      }
    } catch (error) {
      console.error('❌ 患者更新失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '患者更新失败'
      }
    }
  },

  /**
   * 删除患者信息
   * @param {number} patientId 患者ID
   * @returns {Promise} API响应
   */
  async deletePatient(patientId) {
    console.log('🗑️ 删除患者信息:', patientId)
    
    try {
      const response = await fetch(`${API_BASE_URL}${PATIENT_API_PREFIX}/${patientId}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      })

      if (response.ok) {
        const result = await response.json()
        console.log('📥 患者删除响应:', result)
        return {
          success: result.code === 200,
          data: result.data,
          message: result.msg || '删除成功'
        }
      } else {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
    } catch (error) {
      console.error('❌ 患者删除失败:', error)
      return {
        success: false,
        data: null,
        message: error.message || '患者删除失败'
      }
    }
  }
}

/**
 * 数据格式化工具
 */
export const patientDataFormatter = {
  /**
   * 格式化患者数据用于提交
   * @param {Object} rawData 原始数据
   * @returns {Object} 格式化后的数据
   */
  formatForSubmit(rawData) {
    return {
      patientId: rawData.patientId || null,
      userId: String(rawData.userId || ''),
      patientName: String(rawData.patientName || rawData.username || ''),
      age: rawData.age ? Number(rawData.age) : null,
      gender: this.formatGender(rawData.gender),
      idCard: String(rawData.idCard || ''),
      phoneNumber: String(rawData.phoneNumber || rawData.phone || ''),
      address: String(rawData.address || ''),
      emergencyContact: String(rawData.emergencyContact || ''),
      emergencyPhone: String(rawData.emergencyPhone || ''),
      bloodType: String(rawData.bloodType || ''),
      allergies: String(rawData.allergies || '无'),
      medicalHistory: String(rawData.medicalHistory || '无'),
      avatar: String(rawData.avatar || ''),
      password: String(rawData.password || ''),
      status: String(rawData.status || '0'),
      remark: String(rawData.remark || ''),
      birthDate: this.calculateBirthDate(rawData.age)
    }
  },

  /**
   * 格式化性别字段
   * @param {string} gender 性别值
   * @returns {string} 格式化后的性别
   */
  formatGender(gender) {
    if (gender === 'male' || gender === '男' || gender === '0') return '0'
    if (gender === 'female' || gender === '女' || gender === '1') return '1'
    return '2' // 未知
  },

  /**
   * 根据年龄计算出生日期
   * @param {number} age 年龄
   * @returns {string} 出生日期
   */
  calculateBirthDate(age) {
    if (!age || age <= 0) return new Date().toISOString().split('T')[0]
    
    const currentYear = new Date().getFullYear()
    const birthYear = currentYear - age
    return `${birthYear}-01-01`
  },

  /**
   * 格式化显示用的性别
   * @param {string} gender 性别值
   * @returns {string} 显示文本
   */
  formatGenderDisplay(gender) {
    if (gender === '0' || gender === 'male') return '男'
    if (gender === '1' || gender === 'female') return '女'
    return '未知'
  }
}

export default patientAPI
