<template>
  <div class="page-container">
    <!-- 公共头部组件 -->
    <AppHeader
      :user="currentUser"
      current-route="data-analysis"
      @search="handleSearch"
      @show-notifications="showNotifications"
      @show-messages="showMessages"
      @show-settings="showSettings"
      @show-user-menu="showUserMenu"
    />

    <!-- 内容包装区 -->
    <div class="content-wrapper">
      <!-- 公共侧边栏组件 -->
      <AppSidebar
        current-route="data-analysis"
        :expanded="navExpanded"
        @expand="expandNav"
        @collapse="collapseNav"
      />

      <!-- 主内容区 -->
      <div class="main-area">
        <div class="page-main-container">
          <div class="area-header">
            <h3><i class="fas fa-chart-line"></i> 数据分析与进展追踪</h3>

            <!-- 数据调试工具 -->
            <div class="debug-tools" style="margin-top: 10px;">
              <button @click="debugDataIssues" class="debug-btn" style="margin-right: 10px; padding: 5px 10px; font-size: 12px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">🔍 调试数据</button>
              <button @click="validateDataTransmission" class="debug-btn" style="margin-right: 10px; padding: 5px 10px; font-size: 12px; background: #6f42c1; color: white; border: none; border-radius: 4px; cursor: pointer;">✅ 验证传输</button>
              <button @click="clearOldTestData" class="debug-btn" style="margin-right: 10px; padding: 5px 10px; font-size: 12px; background: #dc3545; color: white; border: none; border-radius: 4px; cursor: pointer;">🗑️ 清理异常数据</button>
              <button @click="refreshAllData" class="debug-btn" style="padding: 5px 10px; font-size: 12px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">🔄 刷新数据</button>
            </div>
          </div>

          <!-- 主要内容 -->
          <div class="main-content">
          <!-- 数据概览卡片 -->
          <div class="data-overview-cards">
            <div class="card">
              <div class="card-icon">
                <i class="fas fa-brain"></i>
              </div>
              <div class="card-content">
                <h3>AI检测次数</h3>
                <p class="card-value">{{ trainingStats.totalSessions }}</p>

              </div>
            </div>

            <div class="card">
              <div class="card-icon">
                <i class="fas fa-clock"></i>
              </div>
              <div class="card-content">
                <h3>累计分析时长</h3>
                <p class="card-value">{{ trainingStats.totalHours }} 小时</p>
                <span :class="['card-trend', trainingStats.weeklyTrend.type]">{{ trainingStats.weeklyTrend.text }}</span>
              </div>
            </div>

            <div class="card">
              <div class="card-icon">
                <i class="fas fa-chart-line"></i>
              </div>
              <div class="card-content">
                <h3>平均注意力分数</h3>
                <p class="card-value">{{ avgAttentionScore }}</p>

              </div>
            </div>

            <div class="card">
              <div class="card-icon">
                <i class="fas fa-target"></i>
              </div>
              <div class="card-content">
                <h3>专注度达标率</h3>
                <p class="card-value">{{ focusSuccessRate }}%</p>

              </div>
            </div>

            <div class="card">
              <div class="card-icon">
                <i class="fas fa-trophy"></i>
              </div>
              <div class="card-content">
                <h3>连续训练天数</h3>
                <p class="card-value">{{ consecutiveDays }} 天</p>
                <span class="card-trend neutral">保持良好</span>
              </div>
            </div>

            <div class="card">
              <div class="card-icon">
                <i class="fas fa-smile"></i>
              </div>
              <div class="card-content">
                <h3>情绪稳定指数</h3>
                <p class="card-value">{{ emotionStabilityIndex }}</p>

              </div>
            </div>
          </div>

          <!-- 图表区域 -->
          <div class="charts-container">
            <!-- 第一行图表 -->
            <div class="chart-row">
              <!-- 注意力趋势图 -->
              <div class="chart-wrapper large">
                <div class="chart-header">
                  <h3><i class="fas fa-chart-line"></i> 注意力趋势分析</h3>
                  <div class="chart-controls">
                    <select v-model="attentionChartRange" @change="updateAttentionChart">
                      <option value="week">近一周</option>
                      <option value="month">近一月</option>
                      <option value="year">近一年</option>
                    </select>
                  </div>
                </div>
                <div class="chart-body">
                  <canvas ref="attentionTrendChart"></canvas>
                </div>
                <div class="chart-insights">
                  <div class="insight-item">
                    <span class="insight-label">最高分数:</span>
                    <span class="insight-value">{{ maxAttentionScore }}</span>
                  </div>

                </div>
              </div>

              <!-- 专注度分布饼图 -->
              <div class="chart-wrapper medium">
                <div class="chart-header">
                  <h3><i class="fas fa-pie-chart"></i> 专注度分布</h3>
                </div>
                <div class="chart-body">
                  <canvas ref="focusDistributionChart"></canvas>
                </div>
                <div class="chart-legend">
                  <div class="legend-item">
                    <span class="legend-color high-focus"></span>
                    <span>高度专注 ({{ focusDistribution.high }}%)</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color normal-focus"></span>
                    <span>专注 ({{ focusDistribution.normal }}%)</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color low-focus"></span>
                    <span>分心 ({{ focusDistribution.low }}%)</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color distracted"></span>
                    <span>发呆 ({{ focusDistribution.distracted }}%)</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 第二行图表 -->
            <div class="chart-row">
              <!-- 注意力维度分析雷达图 -->
              <div class="chart-wrapper medium">
                <div class="chart-header">
                  <h3><i class="fas fa-chart-area"></i> 注意力维度分析</h3>
                  <div class="chart-controls">
                    <select v-model="emotionChartRange" @change="updateEmotionChart">
                      <option value="week">近一周</option>
                      <option value="month">近一月</option>
                      <option value="year">近一年</option>
                    </select>
                  </div>
                </div>
                <div class="chart-body">
                  <canvas ref="emotionRadarChart"></canvas>
                </div>
              </div>

              <!-- 训练时长统计 -->
              <div class="chart-wrapper medium">
                <div class="chart-header">
                  <h3><i class="fas fa-clock"></i> 每日训练时长</h3>
                </div>
                <div class="chart-body">
                  <canvas ref="dailyTrainingChart"></canvas>
                </div>
                <div class="chart-insights">
                  <div class="insight-item">
                    <span class="insight-label">日均时长:</span>
                    <span class="insight-value">{{ avgDailyTime }} 分钟</span>
                  </div>
                  <div class="insight-item">
                    <span class="insight-label">最佳时段:</span>
                    <span class="insight-value">{{ bestTimeSlot }}</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 第三行图表 -->
            <div class="chart-row">
              <!-- 进步趋势对比 -->
              <div class="chart-wrapper large">
                <div class="chart-header">
                  <h3><i class="fas fa-chart-bar"></i> 多维度进步对比</h3>
                  <div class="chart-controls">
                    <button
                      v-for="period in comparisonPeriods"
                      :key="period.value"
                      :class="['period-btn', { active: selectedPeriod === period.value }]"
                      @click="selectComparisonPeriod(period.value)"
                    >
                      {{ period.label }}
                    </button>
                  </div>
                </div>
                <div class="chart-body">
                  <canvas ref="progressComparisonChart"></canvas>
                </div>
              </div>
            </div>
          </div>
          </div>
        </div>
      </div>
    </div>

    <footer>
      <p>© 2025 学生心理健康系统 | <a href="#">使用条款</a> | <a href="#">隐私政策</a></p>
    </footer>
  </div>
</template>

<script>
// 使用Chart.js/auto自动注册所有组件
import Chart from 'chart.js/auto'

import AppHeader from '@/components/common/AppHeader.vue'
import AppSidebar from '@/components/common/AppSidebar.vue'
import dataCollectionService from '@/services/dataCollectionService.js'
import dataSync from '@/utils/dataSync.js'
import testDataGenerator from '@/utils/testDataGenerator.js'

export default {
  name: 'DataAnalysis',
  components: {
    AppHeader,
    AppSidebar
  },
  data() {
    return {
      // 搜索查询
      searchQuery: '',
      
      // 导航状态
      navExpanded: false,
      currentRoute: 'data-analysis',

      // 当前用户信息
      currentUser: {
        name: '张同学',
        avatar: 'https://randomuser.me/api/portraits/men/44.jpg'
      },
      
      // 图表时间范围
      attentionChartRange: 'week',
      emotionChartRange: 'week',

      // 图表重试计数器
      emotionChartRetryCount: 0,
      
      // 统计数据 - 从真实数据加载
      trainingStats: {
        totalSessions: 0,
        totalHours: 0,
        totalDuration: 0,
        totalDetections: 0,
        weeklyTrend: {
          type: 'neutral',
          text: '暂无数据'
        }
      },
      avgAttentionScore: '0.0',
      focusSuccessRate: 0,
      consecutiveDays: 0,
      emotionStabilityIndex: 0,
      maxAttentionScore: '0.0',

      avgDailyTime: 0,
      bestTimeSlot: '--:--',

      // 专注度分布数据 - 从真实数据计算
      focusDistribution: {
        high: 0,
        normal: 0,
        low: 0,
        distracted: 0
      },

      // 对比周期选项
      comparisonPeriods: [
        { label: '本周 vs 上周', value: 'week' },
        { label: '本月 vs 上月', value: 'month' },
        { label: '本季 vs 上季', value: 'quarter' }
      ],
      selectedPeriod: 'week',

      // 图表实例
      attentionChart: null, // 保留兼容性
      attentionTrendChart: null, // 新的注意力趋势图
      emotionChart: null,
      focusDistributionChart: null,
      emotionRadarChart: null,
      dailyTrainingChart: null,
      progressComparisonChart: null,

      // 图表初始化重试计数器
      chartInitRetryCount: 0,

      // 数据状态
      hasRealData: false
    }
  },
  
  mounted() {
    // 初始化导航状态
    if (localStorage.getItem('navExpanded') === 'true') {
      this.navExpanded = true;
    }

    // 强制清除所有测试数据，确保只显示真实数据
    this.forceCleanTestData();

    // 配置Chart.js全局默认值
    this.configureChartDefaults();

    // 订阅数据更新事件
    this.subscribeToDataUpdates();

    // 添加页面可见性监听器
    this.addVisibilityListener();

    // 延迟初始化页面，确保DOM完全加载
    this.$nextTick(() => {
      setTimeout(() => {
        this.initPage();
      }, 200);
    });
  },
  
  beforeUnmount() {
    console.log('🗑️ 数据分析组件即将销毁，清理资源...');

    // 销毁所有图表实例
    const charts = [
      'attentionChart',
      'emotionChart',
      'focusDistributionChart',
      'emotionRadarChart',
      'dailyTrainingChart',
      'progressComparisonChart'
    ];

    charts.forEach(chartName => {
      this.destroyChart(chartName);
    });

    // 停止所有Chart.js动画
    try {
      if (typeof Chart !== 'undefined' && Chart.helpers && Chart.helpers.cancelAnimFrame) {
        Chart.helpers.cancelAnimFrame();
      }
    } catch (error) {
      console.warn('停止Chart.js动画时出错:', error);
    }

    // 取消数据更新订阅
    this.unsubscribeFromDataUpdates();

    // 移除页面可见性监听器
    this.removeVisibilityListener();

    console.log('✅ 数据分析组件资源清理完成');
  },
  
  methods: {
    // 配置Chart.js全局默认值
    configureChartDefaults() {
      // 设置Chart.js 3.x全局默认配置
      if (Chart && Chart.defaults) {
        // Chart.js 3.x的全局配置方式
        Chart.defaults.font = {
          family: 'Arial, sans-serif',
          size: 12
        };

        Chart.defaults.responsive = true;
        Chart.defaults.maintainAspectRatio = false;

        Chart.defaults.animation = {
          duration: 300
        };

        // Chart.js 3.x的交互配置
        Chart.defaults.interaction = {
          intersect: false,
          mode: 'nearest'
        };
      }
    },

    // AppHeader 事件处理
    handleSearch(query) {
      console.log('搜索数据:', query);
      // 实现数据搜索逻辑
    },

    showNotifications() {
      console.log('显示通知');
    },

    showMessages() {
      console.log('显示消息');
    },

    showSettings() {
      console.log('显示设置');
    },

    showUserMenu() {
      console.log('显示用户菜单');
    },

    // 选择对比周期
    selectComparisonPeriod(period) {
      this.selectedPeriod = period;
      this.updateProgressComparisonChart();
    },

    // 导航展开
    expandNav() {
      this.navExpanded = true;
      localStorage.setItem('navExpanded', 'true');
    },
    
    // 导航收起
    collapseNav(e) {
      // 检查事件对象是否存在
      if (e && e.clientX !== undefined) {
        const mouseX = e.clientX;
        if (mouseX > 180) {
          this.navExpanded = false;
          localStorage.removeItem('navExpanded');
        }
      } else {
        // 如果没有事件对象，直接收起导航
        this.navExpanded = false;
        localStorage.removeItem('navExpanded');
      }
    },
    
    // 初始化页面
    initPage() {
      this.$nextTick(() => {
        this.stableChartInit();
        this.loadData();
      });
    },

    // 稳定的图表初始化方法
    stableChartInit() {
      // 等待DOM完全渲染
      this.$nextTick(() => {
        // 再等待一个渲染周期确保canvas元素可用
        setTimeout(() => {
          this.initAllChartsWithRetry();
        }, 200);
      });
    },

    // 带重试的图表初始化
    initAllChartsWithRetry(retryCount = 0) {
      const maxRetries = 3;

      try {
        // 检查所有canvas元素是否可用
        const canvasElements = [
          'attentionTrendChart',
          'focusDistributionChart',
          'emotionRadarChart',
          'dailyTrainingChart',
          'progressComparisonChart'
        ];

        let allCanvasReady = true;
        for (const canvasRef of canvasElements) {
          const canvas = this.$refs[canvasRef];
          if (!canvas || typeof canvas.getContext !== 'function') {
            allCanvasReady = false;
            console.warn(`Canvas ${canvasRef} 未就绪`);
            break;
          }
        }

        if (allCanvasReady) {
          console.log('✅ 所有Canvas元素已就绪，开始初始化图表');
          this.initAllChartsSimple();
        } else if (retryCount < maxRetries) {
          console.log(`⏳ Canvas未就绪，重试 ${retryCount + 1}/${maxRetries}`);
          setTimeout(() => {
            this.initAllChartsWithRetry(retryCount + 1);
          }, 500);
        } else {
          console.error('❌ Canvas初始化失败，已达到最大重试次数');
        }
      } catch (error) {
        console.error('❌ 图表初始化检查失败:', error);
      }
    },
    
    // 初始化所有图表
    initCharts() {
      try {
        // 确保DOM元素存在且可用
        const attentionCanvas = this.$refs.attentionTrendChart;
        const focusCanvas = this.$refs.focusDistributionChart;
        const emotionRadarCanvas = this.$refs.emotionRadarChart;
        const dailyTrainingCanvas = this.$refs.dailyTrainingChart;
        const progressCanvas = this.$refs.progressComparisonChart;

        if (attentionCanvas && focusCanvas && emotionRadarCanvas && dailyTrainingCanvas && progressCanvas &&
            typeof attentionCanvas.getContext === 'function' &&
            typeof focusCanvas.getContext === 'function' &&
            typeof emotionRadarCanvas.getContext === 'function' &&
            typeof dailyTrainingCanvas.getContext === 'function' &&
            typeof progressCanvas.getContext === 'function') {
          this.initAttentionTrendChart();
          this.initFocusDistributionChart();
          this.initEmotionRadarChart();
          this.initDailyTrainingChart();
          this.initProgressComparisonChart();
        } else {
          console.warn('图表DOM元素未找到或不可用，延迟初始化');
          // 限制重试次数，避免无限循环
          if (!this.chartInitRetryCount) {
            this.chartInitRetryCount = 0;
          }
          if (this.chartInitRetryCount < 5) {
            this.chartInitRetryCount++;
            setTimeout(() => {
              this.initCharts();
            }, 500);
          } else {
            console.error('图表初始化重试次数超限，停止重试');
          }
        }
      } catch (error) {
        console.error('初始化图表失败:', error);
      }
    },
    
    // 初始化注意力趋势图
    initAttentionTrendChart() {
      // 先销毁现有图表
      this.destroyChart('attentionTrendChart');
      // 直接调用更新方法来创建图表
      this.updateAttentionChart();
    },

    // 初始化专注度分布饼图
    initFocusDistributionChart() {
      // 先销毁现有图表
      this.ultraSafeDestroyChart('focusDistributionChart');

      // 使用安全包装器创建图表
      this.focusDistributionChart = this.createSafeChart('focusDistributionChart', {
        type: 'doughnut',
        data: {
          labels: ['高度专注', '专注', '分心', '发呆'],
          datasets: [{
            data: [
              this.focusDistribution.high,
              this.focusDistribution.normal,
              this.focusDistribution.low,
              this.focusDistribution.distracted
            ],
            backgroundColor: [
              '#4CAF50',  // 高度专注 - 绿色
              '#2196F3',  // 专注 - 蓝色
              '#FF9800',  // 分心 - 橙色
              '#F44336'   // 发呆 - 红色
            ],
            borderWidth: 2,
            borderColor: '#fff'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false  // 使用自定义图例
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return context.label + ': ' + context.parsed + '%';
                }
              }
            }
          }
        }
      }, 'focusDistributionChart');
    },

    // 初始化注意力维度分析雷达图（使用真实数据）
    initEmotionRadarChart() {
      // 先销毁现有图表
      this.ultraSafeDestroyChart('emotionRadarChart');

      // 获取真实的注意力维度数据
      const emotionData = this.getEmotionRadarData();

      // 使用安全包装器创建图表
      this.emotionRadarChart = this.createSafeChart('emotionRadarChart', {
        type: 'radar',
        data: {
          labels: ['注意力强度', '持续专注', '反应敏捷', '状态稳定', '清醒程度', '投入程度'],
          datasets: [{
            label: '本周',
            data: emotionData.thisWeek,
            borderColor: '#2196F3',
            backgroundColor: 'rgba(33, 150, 243, 0.2)',
            pointBackgroundColor: '#2196F3',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: '#2196F3'
          }, {
            label: '上周',
            data: emotionData.lastWeek,
            borderColor: '#FF9800',
            backgroundColor: 'rgba(255, 152, 0, 0.2)',
            pointBackgroundColor: '#FF9800',
            pointBorderColor: '#fff',
            pointHoverBackgroundColor: '#fff',
            pointHoverBorderColor: '#FF9800'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            r: {
              beginAtZero: true,
              max: 100,
              ticks: {
                stepSize: 20
              }
            }
          },
          plugins: {
            legend: {
              position: 'bottom'
            }
          }
        }
      }, 'emotionRadarChart');
    },

    // 初始化每日训练时长图（使用真实数据）
    initDailyTrainingChart() {
      // 先销毁现有图表
      this.ultraSafeDestroyChart('dailyTrainingChart');

      // 获取真实的每日训练数据
      const dailyData = this.getDailyTrainingData();

      // 使用安全包装器创建图表
      this.dailyTrainingChart = this.createSafeChart('dailyTrainingChart', {
        type: 'bar',
        data: {
          labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          datasets: [{
            label: '训练时长(分钟)',
            data: dailyData.durations,
            backgroundColor: dailyData.colors,
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            y: {
              beginAtZero: true,
              max: Math.max(60, Math.max(...dailyData.durations) + 10),
              ticks: {
                stepSize: 10
              }
            }
          },
          plugins: {
            legend: {
              display: false
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  return context.parsed.y + ' 分钟';
                }
              }
            }
          }
        }
      }, 'dailyTrainingChart');
    },

    // 初始化进步对比图（使用真实数据）
    initProgressComparisonChart() {
      try {
        // 先销毁现有图表
        this.ultraSafeDestroyChart('progressComparisonChart');

        // 获取真实的进步对比数据
        const progressData = this.getProgressComparisonData();

        // 使用安全包装器创建图表
        this.progressComparisonChart = this.createSafeChart('progressComparisonChart', {
          type: 'bar',
          data: {
            labels: ['注意力', '专注度', '稳定性', '训练时长', '一致性'],
            datasets: [{
              label: '本周',
              data: progressData.thisWeek,
              backgroundColor: '#2196F3'
            }, {
              label: '上周',
              data: progressData.lastWeek,
              backgroundColor: '#FF9800'
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                max: 100
              }
            },
            plugins: {
              legend: {
                display: true
              },
              tooltip: {
                enabled: false // 禁用tooltip避免复杂回调
              }
            }
          }
        }, 'progressComparisonChart');

        if (this.progressComparisonChart) {
          console.log('✅ 进步对比图初始化成功');
        }

      } catch (error) {
        console.error('❌ 进步对比图初始化失败:', error);
        this.progressComparisonChart = null;
      }
    },

    // 加载数据
    loadData() {
      console.log('🔄 开始加载数据...');

      // 首先检查并清除测试数据
      this.checkAndClearTestData();

      this.loadRealData();
      this.updateAvgAttentionScore('week');

      // 如果没有真实数据，显示提示
      const allData = dataCollectionService.getAllData();
      if (!allData || !allData.sessions || allData.sessions.length === 0) {
        console.log('ℹ️ 暂无检测数据，请先进行AI检测');
      } else {
        console.log('✅ 已加载真实检测数据:', allData.sessions.length, '个会话');
      }
    },

    // 加载真实数据
    loadRealData() {
      try {
        // 获取所有数据
        const allData = dataCollectionService.getAllData();
        if (!allData || !allData.sessions || allData.sessions.length === 0) {
          console.log('暂无检测数据');
          this.hasRealData = false;
          return;
        }

        // 更新数据状态
        this.hasRealData = true;

        // 更新统计数据
        this.updateStatisticsFromRealData(allData);

        // 更新专注度分布
        this.updateFocusDistributionFromRealData(allData);

        console.log('✅ 真实数据加载完成，会话数:', allData.sessions.length);
      } catch (error) {
        console.error('加载真实数据失败:', error);
        this.hasRealData = false;
      }
    },

    // 从真实数据更新统计信息
    updateStatisticsFromRealData(data) {
      const stats = data.statistics;

      // 更新基础统计
      this.trainingStats.totalSessions = stats.totalSessions || 0;
      this.trainingStats.totalDuration = stats.totalDuration || 0;
      this.trainingStats.totalDetections = stats.totalDetections || 0;
      this.trainingStats.totalHours = stats.totalDuration ? (stats.totalDuration / 60).toFixed(1) : 0;

      // 计算平均注意力分数
      if (data.sessions && data.sessions.length > 0) {
        const recentSessions = data.sessions.slice(-10); // 最近10个会话
        const totalScore = recentSessions.reduce((sum, session) =>
          sum + (session.statistics.avgAttentionScore || 0), 0);
        this.avgAttentionScore = recentSessions.length > 0 ?
          (totalScore / recentSessions.length).toFixed(1) : '0.0';

        // 计算最高注意力分数
        const maxScore = Math.max(...data.sessions.map(session =>
          session.statistics.avgAttentionScore || 0));
        this.maxAttentionScore = maxScore.toFixed(1);
      } else {
        this.avgAttentionScore = '0.0';
        this.maxAttentionScore = '0.0';
      }

      // 计算专注度达标率
      if (data.sessions && data.sessions.length > 0) {
        const goodSessions = data.sessions.filter(session =>
          session.statistics.avgAttentionScore >= 70).length;
        this.focusSuccessRate = Math.round((goodSessions / data.sessions.length) * 100);
      } else {
        this.focusSuccessRate = 0;
      }

      // 计算连续训练天数
      this.consecutiveDays = this.calculateConsecutiveDays(data.dailyData);

      // 计算情绪稳定指数（基于最近会话的情绪变化）
      this.emotionStabilityIndex = this.calculateEmotionStability(data.sessions);

      // 计算平均每日训练时间
      if (data.dailyData && Object.keys(data.dailyData).length > 0) {
        const totalDays = Object.keys(data.dailyData).length;
        this.avgDailyTime = Math.round(stats.totalDuration / totalDays);
      } else {
        this.avgDailyTime = 0;
      }



      // 计算最佳时间段
      this.bestTimeSlot = this.calculateBestTimeSlot(data.sessions);

      // 计算本周训练时长趋势
      this.trainingStats.weeklyTrend = this.calculateWeeklyTrend(data.sessions);

      console.log('📊 统计数据已更新:', {
        sessions: this.trainingStats.totalSessions,
        avgAttention: this.avgAttentionScore,
        successRate: this.focusSuccessRate,
        consecutiveDays: this.consecutiveDays,
        weeklyTrend: this.trainingStats.weeklyTrend
      });
    },

    // 从真实数据更新专注度分布
    updateFocusDistributionFromRealData(data) {
      if (!data.sessions || data.sessions.length === 0) return;

      // 统计所有检测结果的注意力等级分布
      let totalDetections = 0;
      const levelCounts = { 0: 0, 1: 0, 2: 0, 3: 0 };

      data.sessions.forEach(session => {
        if (session.statistics && session.statistics.attentionLevels) {
          Object.keys(session.statistics.attentionLevels).forEach(level => {
            levelCounts[level] += session.statistics.attentionLevels[level];
            totalDetections += session.statistics.attentionLevels[level];
          });
        }
      });

      if (totalDetections > 0) {
        this.focusDistribution = {
          high: Math.round((levelCounts[0] / totalDetections) * 100),      // 高度专注
          normal: Math.round((levelCounts[1] / totalDetections) * 100),    // 专注
          low: Math.round((levelCounts[2] / totalDetections) * 100),       // 分心
          distracted: Math.round((levelCounts[3] / totalDetections) * 100) // 发呆
        };

        // 更新图表
        this.updateFocusDistributionChart();
      }
    },

    // 更新专注度分布图表
    updateFocusDistributionChart() {
      try {
        // 检查图表对象是否存在且有效
        if (!this.focusDistributionChart ||
            !this.focusDistributionChart.data ||
            !this.focusDistributionChart.data.datasets ||
            !this.focusDistributionChart.data.datasets[0]) {
          console.warn('专注度分布图表对象无效，重新创建');
          this.destroyChart('focusDistributionChart');
          this.initFocusDistributionChart();
          return;
        }

        // 检查图表是否已被销毁
        if (this.focusDistributionChart.destroyed) {
          console.warn('专注度分布图表已被销毁，重新创建');
          this.focusDistributionChart = null;
          this.initFocusDistributionChart();
          return;
        }

        // 更新数据
        this.focusDistributionChart.data.datasets[0].data = [
          this.focusDistribution.high,
          this.focusDistribution.normal,
          this.focusDistribution.low,
          this.focusDistribution.distracted
        ];

        // 安全更新图表
        this.focusDistributionChart.update('none'); // 使用'none'模式避免动画问题
        console.log('✅ 专注度分布图表更新成功');
      } catch (error) {
        console.error('❌ 更新专注度分布图表失败:', error);
        // 如果更新失败，销毁并重新创建图表
        this.destroyChart('focusDistributionChart');
        this.$nextTick(() => {
          this.initFocusDistributionChart();
        });
      }
    },

    // 安全销毁图表 - 增强版
    destroyChart(chartName) {
      if (!this[chartName]) {
        return; // 图表不存在，直接返回
      }

      try {
        // 停止所有动画
        if (this[chartName].stop) {
          this[chartName].stop();
        }

        // 检查图表是否已经被销毁
        if (this[chartName].destroyed) {
          console.log(`⚠️ 图表 ${chartName} 已经被销毁`);
          this[chartName] = null;
          return;
        }

        // 安全销毁图表
        if (typeof this[chartName].destroy === 'function') {
          console.log(`🗑️ 销毁图表: ${chartName}`);

          // 先清理canvas引用，防止Chart.js访问null canvas
          const originalCanvas = this[chartName].canvas;
          if (originalCanvas) {
            // 创建一个临时canvas来替换原canvas，避免null引用
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = originalCanvas.width || 400;
            tempCanvas.height = originalCanvas.height || 300;
            this[chartName].canvas = tempCanvas;
            this[chartName].ctx = tempCanvas.getContext('2d');
          }

          // 现在安全销毁
          this[chartName].destroy();
        }
      } catch (error) {
        console.error(`❌ 销毁图表失败 ${chartName}:`, error);
        // 即使销毁失败，也要清理引用
      }

      // 强制设置为null
      this[chartName] = null;

      // 清理DOM canvas元素
      this.cleanupCanvas(chartName);
    },

    // 清理Canvas元素
    cleanupCanvas(chartName) {
      const canvasRef = this.getCanvasRef(chartName);
      if (canvasRef && this.$refs[canvasRef]) {
        try {
          const canvas = this.$refs[canvasRef];
          if (canvas && canvas.getContext) {
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            // 重置canvas尺寸来清理内容
            const currentWidth = canvas.width;
            canvas.width = currentWidth;
            console.log(`🧹 清理canvas: ${canvasRef}`);
          }
        } catch (error) {
          console.warn(`清理canvas失败 ${canvasRef}:`, error);
        }
      }
    },

    // 超级安全的图表重建方案
    recreateAllCharts() {
      console.log('🔄 开始安全重建所有图表...');

      // 1. 完全销毁所有图表和DOM引用
      this.safeDestroyAllCharts();

      // 2. 清理所有可能的残留引用
      this.clearAllChartReferences();

      // 3. 等待多个渲染周期确保DOM完全清理
      this.$nextTick(() => {
        setTimeout(() => {
          this.$nextTick(() => {
            setTimeout(() => {
              this.safeInitAllCharts();
              console.log('✅ 图表安全重建完成');
            }, 300);
          });
        }, 200);
      });
    },

    // 安全销毁所有图表
    safeDestroyAllCharts() {
      const chartNames = [
        'focusDistributionChart',
        'emotionRadarChart',
        'dailyTrainingChart',
        'progressComparisonChart',
        'attentionTrendChart'
      ];

      chartNames.forEach(chartName => {
        this.ultraSafeDestroyChart(chartName);
      });
    },

    // 超级安全的单个图表销毁
    ultraSafeDestroyChart(chartName) {
      if (!this[chartName]) return;

      try {
        // 停止所有动画和渲染
        if (this[chartName].stop) {
          this[chartName].stop();
        }

        // 检查图表状态
        if (this[chartName].destroyed) {
          this[chartName] = null;
          return;
        }

        // 获取canvas引用并验证
        const canvas = this[chartName].canvas;
        if (canvas && canvas.parentNode) {
          // Canvas还在DOM中，安全销毁
          this[chartName].destroy();
        } else {
          // Canvas已经不在DOM中，直接清理引用
          console.warn(`Canvas for ${chartName} not in DOM, clearing reference`);
        }

      } catch (error) {
        console.warn(`销毁图表 ${chartName} 时出错:`, error);
      } finally {
        // 无论如何都清理引用
        this[chartName] = null;
      }
    },

    // 清理所有图表引用
    clearAllChartReferences() {
      this.focusDistributionChart = null;
      this.emotionRadarChart = null;
      this.dailyTrainingChart = null;
      this.progressComparisonChart = null;
      this.attentionTrendChart = null;
      this.attentionChart = null;
      this.emotionChart = null;
    },

    // 安全初始化所有图表
    safeInitAllCharts() {
      try {
        console.log('🎨 开始安全初始化所有图表...');

        // 验证DOM元素存在且可用
        if (!this.validateAllCanvasElements()) {
          console.error('❌ Canvas元素验证失败，跳过图表初始化');
          return;
        }

        // 逐个安全初始化图表
        this.safeInitChart('attentionTrendChart', () => this.initAttentionTrendChart());
        this.safeInitChart('focusDistributionChart', () => this.initFocusDistributionChart());
        this.safeInitChart('emotionRadarChart', () => this.initEmotionRadarChart());
        this.safeInitChart('dailyTrainingChart', () => this.initDailyTrainingChart());
        this.safeInitChart('progressComparisonChart', () => this.initProgressComparisonChart());

        console.log('✅ 所有图表安全初始化完成');
      } catch (error) {
        console.error('❌ 安全初始化图表失败:', error);
      }
    },

    // 验证所有Canvas元素
    validateAllCanvasElements() {
      const canvasRefs = [
        'attentionTrendChart',
        'focusDistributionChart',
        'emotionRadarChart',
        'dailyTrainingChart',
        'progressComparisonChart'
      ];

      for (const ref of canvasRefs) {
        const canvas = this.$refs[ref];
        if (!canvas || !canvas.getContext || !canvas.parentNode) {
          console.warn(`Canvas ${ref} 验证失败`);
          return false;
        }
      }

      return true;
    },

    // 安全初始化单个图表
    safeInitChart(chartName, initFunction) {
      try {
        console.log(`🎨 安全初始化图表: ${chartName}`);
        initFunction();
      } catch (error) {
        console.error(`❌ 初始化图表 ${chartName} 失败:`, error);
        // 清理可能的残留引用
        this[chartName] = null;
      }
    },

    // Chart.js安全包装器 - 创建图表
    createSafeChart(canvasRef, chartConfig, chartName) {
      try {
        // 验证canvas元素
        const canvas = this.$refs[canvasRef];
        if (!canvas || !canvas.getContext || !canvas.parentNode) {
          throw new Error(`Canvas ${canvasRef} 不可用`);
        }

        // 获取上下文
        const ctx = canvas.getContext('2d');
        if (!ctx) {
          throw new Error(`无法获取 ${canvasRef} 的2D上下文`);
        }

        // 添加安全配置
        const safeConfig = {
          ...chartConfig,
          options: {
            ...chartConfig.options,
            responsive: true,
            maintainAspectRatio: false,
            // 禁用动画以减少DOM操作
            animation: false,
            // 添加错误处理
            onResize: (chart) => {
              try {
                if (chart.canvas && chart.canvas.parentNode) {
                  // 只有当canvas还在DOM中时才处理resize
                  return true;
                }
              } catch (error) {
                console.warn('Chart resize error:', error);
                return false;
              }
            }
          }
        };

        // 创建图表
        const chart = new Chart(ctx, safeConfig);

        // 添加安全检查方法
        chart.safeUpdate = function() {
          try {
            if (this.canvas && this.canvas.parentNode && this.ctx) {
              this.update('none');
            }
          } catch (error) {
            console.warn('Chart update error:', error);
          }
        };

        chart.safeDestroy = function() {
          try {
            // 检查图表是否还有效
            if (this.canvas && this.ctx) {
              this.destroy();
            }
          } catch (error) {
            console.warn('Chart destroy error:', error);
          }
        };

        console.log(`✅ 安全创建图表: ${chartName}`);
        return chart;

      } catch (error) {
        console.error(`❌ 创建图表 ${chartName} 失败:`, error);
        return null;
      }
    },

    // 销毁所有图表
    destroyAllCharts() {
      const chartNames = [
        'focusDistributionChart',
        'emotionRadarChart',
        'dailyTrainingChart',
        'progressComparisonChart',
        'attentionTrendChart'
      ];

      chartNames.forEach(chartName => {
        if (this[chartName]) {
          try {
            this[chartName].destroy();
            console.log(`🗑️ 销毁图表: ${chartName}`);
          } catch (error) {
            console.warn(`销毁图表失败 ${chartName}:`, error);
          }
          this[chartName] = null;
        }
      });
    },

    // 简化的图表初始化方法
    initAllChartsSimple() {
      try {
        console.log('🎨 开始初始化所有图表...');

        // 直接初始化，不做复杂检查
        this.initAttentionTrendChart();
        this.initFocusDistributionChart();
        this.initEmotionRadarChart();
        this.initDailyTrainingChart();
        this.initProgressComparisonChart();

        console.log('✅ 所有图表初始化完成');
      } catch (error) {
        console.error('❌ 初始化图表失败:', error);
      }
    },

    // 获取图表对应的canvas引用名
    getCanvasRef(chartName) {
      const refMap = {
        'focusDistributionChart': 'focusDistributionChart',
        'emotionRadarChart': 'emotionRadarChart',
        'dailyTrainingChart': 'dailyTrainingChart',
        'progressComparisonChart': 'progressComparisonChart',
        'attentionTrendChart': 'attentionTrendChart',
        'attentionChart': 'attentionTrendChart', // 兼容旧名称
        'emotionChart': 'emotionDistributionChart'
      };
      return refMap[chartName];
    },

    // 安全更新图表
    safeUpdateChart(chartName, updateFunction) {
      try {
        if (!this[chartName]) {
          console.warn(`图表 ${chartName} 不存在，跳过更新`);
          return false;
        }

        if (this[chartName].destroyed) {
          console.warn(`图表 ${chartName} 已被销毁，跳过更新`);
          this[chartName] = null;
          return false;
        }

        // 检查canvas是否还存在且有效
        if (!this[chartName].canvas || !this[chartName].ctx) {
          console.warn(`图表 ${chartName} 的canvas已失效，销毁图表`);
          this.destroyChart(chartName);
          return false;
        }

        // 检查图表配置是否完整
        if (!this[chartName].data || !this[chartName].options) {
          console.warn(`图表 ${chartName} 配置不完整，重新创建`);
          this.destroyChart(chartName);
          return false;
        }

        updateFunction();

        // 使用更安全的更新方式
        try {
          this[chartName].update('none');
        } catch (updateError) {
          console.warn(`图表 ${chartName} 更新失败，跳过渲染:`, updateError);
          // 不调用render()，避免递归问题
          return false;
        }

        console.log(`✅ ${chartName} 更新成功`);
        return true;
      } catch (error) {
        console.error(`❌ ${chartName} 更新失败:`, error);

        // 检查是否是canvas相关错误
        if (error.message && (
          error.message.includes('getContext') ||
          error.message.includes('canvas') ||
          error.message.includes('null') ||
          error.message.includes('Cannot read properties of null')
        )) {
          console.error(`🚨 Canvas错误，强制清理图表 ${chartName}`);
          this[chartName] = null;
        } else if (error.message && error.message.includes('Maximum call stack size exceeded')) {
          console.error(`🚨 检测到栈溢出，强制销毁图表 ${chartName}`);
          this[chartName] = null;
        } else {
          this.destroyChart(chartName);
        }
        return false;
      }
    },

    // 计算连续训练天数
    calculateConsecutiveDays(dailyData) {
      if (!dailyData) return 0;

      const dates = Object.keys(dailyData).sort().reverse(); // 最新日期在前
      let consecutive = 0;
      const today = new Date().toISOString().split('T')[0];

      for (let i = 0; i < dates.length; i++) {
        const date = dates[i];
        const daysDiff = Math.floor((new Date(today) - new Date(date)) / (1000 * 60 * 60 * 24));

        if (daysDiff === consecutive) {
          consecutive++;
        } else {
          break;
        }
      }

      return consecutive;
    },

    // 计算情绪稳定指数
    calculateEmotionStability(sessions) {
      if (!sessions || sessions.length < 2) return 85; // 默认值

      const recentSessions = sessions.slice(-7); // 最近7个会话
      const scores = recentSessions.map(session => session.statistics.avgAttentionScore || 0);

      // 计算标准差
      const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
      const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
      const stdDev = Math.sqrt(variance);

      // 稳定指数：100 - 标准差（标准差越小越稳定）
      return Math.max(0, Math.min(100, Math.round(100 - stdDev)));
    },



    // 计算最佳时间段
    calculateBestTimeSlot(sessions) {
      if (!sessions || sessions.length === 0) return '--:--';

      // 按小时统计注意力分数
      const hourlyStats = {};

      sessions.forEach(session => {
        const hour = new Date(session.startTime).getHours();
        if (!hourlyStats[hour]) {
          hourlyStats[hour] = { total: 0, count: 0 };
        }
        hourlyStats[hour].total += session.statistics.avgAttentionScore || 0;
        hourlyStats[hour].count++;
      });

      // 找到平均分数最高的时间段
      let bestHour = 0;
      let bestScore = 0;

      Object.keys(hourlyStats).forEach(hour => {
        const avg = hourlyStats[hour].total / hourlyStats[hour].count;
        if (avg > bestScore) {
          bestScore = avg;
          bestHour = parseInt(hour);
        }
      });

      // 格式化时间段（2小时窗口）
      const startHour = String(bestHour).padStart(2, '0');
      const endHour = String((bestHour + 2) % 24).padStart(2, '0');
      return `${startHour}:00-${endHour}:00`;
    },

    // 获取情绪分布数据
    getEmotionDistributionData() {
      const allData = dataCollectionService.getAllData();

      if (!allData || !allData.sessions || allData.sessions.length === 0) {
        // 如果没有数据，返回空分布
        return [
          { emotion_type: '专注', count: 0 },
          { emotion_type: '分心', count: 0 },
          { emotion_type: '疲劳', count: 0 },
          { emotion_type: '焦虑', count: 0 }
        ];
      }

      // 统计情绪分布
      const emotionCounts = {};

      allData.sessions.forEach(session => {
        if (session.detections && session.detections.length > 0) {
          session.detections.forEach(detection => {
            const emotion = detection.emotion || '未知';
            emotionCounts[emotion] = (emotionCounts[emotion] || 0) + 1;
          });
        }
      });

      // 转换为图表数据格式
      const emotionMapping = {
        'focused': '专注',
        'distracted': '分心',
        'tired': '疲劳',
        'anxious': '焦虑',
        'neutral': '平静',
        'happy': '愉快',
        'sad': '沮丧'
      };

      const result = [];
      Object.keys(emotionCounts).forEach(emotion => {
        const displayName = emotionMapping[emotion] || emotion;
        result.push({
          emotion_type: displayName,
          count: emotionCounts[emotion]
        });
      });

      // 如果没有情绪数据，基于注意力等级生成
      if (result.length === 0) {
        const attentionLevels = { 0: 0, 1: 0, 2: 0, 3: 0 };

        allData.sessions.forEach(session => {
          if (session.statistics && session.statistics.attentionLevels) {
            Object.keys(session.statistics.attentionLevels).forEach(level => {
              attentionLevels[level] += session.statistics.attentionLevels[level];
            });
          }
        });

        return [
          { emotion_type: '高度专注', count: attentionLevels[3] || 0 },
          { emotion_type: '专注', count: attentionLevels[2] || 0 },
          { emotion_type: '分心', count: attentionLevels[1] || 0 },
          { emotion_type: '发呆', count: attentionLevels[0] || 0 }
        ];
      }

      return result;
    },

    // 获取注意力维度分析数据
    getEmotionRadarData() {
      const allData = dataCollectionService.getAllData();

      if (!allData || !allData.sessions || allData.sessions.length === 0) {
        // 如果没有数据，返回空数据
        return {
          thisWeek: [0, 0, 0, 0, 0, 0],
          lastWeek: [0, 0, 0, 0, 0, 0]
        };
      }

      const now = new Date();
      const thisWeekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const lastWeekStart = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

      // 分别统计本周和上周的数据
      const thisWeekSessions = allData.sessions.filter(session =>
        new Date(session.startTime) >= thisWeekStart
      );

      const lastWeekSessions = allData.sessions.filter(session => {
        const sessionDate = new Date(session.startTime);
        return sessionDate >= lastWeekStart && sessionDate < thisWeekStart;
      });

      // 计算注意力维度指标
      const calculateAttentionScores = (sessions) => {
        if (sessions.length === 0) return [0, 0, 0, 0, 0, 0];

        let totalIntensity = 0;      // 注意力强度
        let totalSustained = 0;      // 持续专注
        let totalResponsive = 0;     // 反应敏捷
        let totalStable = 0;         // 状态稳定
        let totalAlert = 0;          // 清醒程度
        let totalEngaged = 0;        // 投入程度

        sessions.forEach(session => {
          const avgAttention = session.statistics.avgAttentionScore || 0;
          const duration = session.duration || 0;

          // 基于真实检测数据计算各维度
          totalIntensity += avgAttention; // 注意力强度 = 平均注意力分数

          // 持续专注 = 基于会话时长和注意力分数
          totalSustained += Math.min(100, (duration / 60) * avgAttention * 0.1); // 时长越长，持续性越好

          // 反应敏捷 = 基于高注意力等级的比例
          if (session.statistics.attentionLevels) {
            const highLevels = (session.statistics.attentionLevels[2] || 0) + (session.statistics.attentionLevels[3] || 0);
            const totalLevels = Object.values(session.statistics.attentionLevels).reduce((sum, count) => sum + count, 0);
            totalResponsive += totalLevels > 0 ? (highLevels / totalLevels) * 100 : 0;
          } else {
            totalResponsive += avgAttention * 0.8; // 备用计算
          }

          // 状态稳定 = 基于注意力分数的一致性
          totalStable += avgAttention * 0.9;

          // 清醒程度 = 基于检测频率和注意力
          const detectionCount = session.statistics.totalDetections || 0;
          totalAlert += Math.min(100, (detectionCount / Math.max(1, duration / 60)) * avgAttention * 0.01);

          // 投入程度 = 综合指标
          totalEngaged += (avgAttention + (duration / 60) * 2) * 0.4;
        });

        const count = sessions.length;
        return [
          Math.round(totalIntensity / count),
          Math.round(totalSustained / count),
          Math.round(totalResponsive / count),
          Math.round(totalStable / count),
          Math.round(totalAlert / count),
          Math.round(Math.min(100, totalEngaged / count))
        ];
      };

      return {
        thisWeek: calculateAttentionScores(thisWeekSessions),
        lastWeek: calculateAttentionScores(lastWeekSessions)
      };
    },

    // 获取每日训练数据
    getDailyTrainingData() {
      const allData = dataCollectionService.getAllData();

      if (!allData || !allData.sessions || allData.sessions.length === 0) {
        return {
          durations: [0, 0, 0, 0, 0, 0, 0],
          colors: ['#E0E0E0', '#E0E0E0', '#E0E0E0', '#E0E0E0', '#E0E0E0', '#E0E0E0', '#E0E0E0']
        };
      }

      // 获取最近7天的数据
      const now = new Date();
      const weekStart = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000);

      const dailyDurations = new Array(7).fill(0);

      allData.sessions.forEach(session => {
        const sessionDate = new Date(session.startTime);
        if (sessionDate >= weekStart) {
          const dayOfWeek = sessionDate.getDay();
          const adjustedDay = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // 调整为周一=0
          dailyDurations[adjustedDay] += session.duration || 0;
        }
      });

      // 根据训练时长设置颜色
      const colors = dailyDurations.map(duration => {
        if (duration === 0) return '#E0E0E0';      // 灰色 - 无训练
        if (duration < 15) return '#F44336';      // 红色 - 训练不足
        if (duration < 30) return '#FF9800';      // 橙色 - 训练一般
        return '#4CAF50';                         // 绿色 - 训练充足
      });

      return {
        durations: dailyDurations,
        colors: colors
      };
    },

    // 获取进步对比数据
    getProgressComparisonData() {
      const allData = dataCollectionService.getAllData();

      if (!allData || !allData.sessions || allData.sessions.length === 0) {
        return {
          thisWeek: [0, 0, 0, 0, 0],
          lastWeek: [0, 0, 0, 0, 0]
        };
      }

      const now = new Date();
      const thisWeekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const lastWeekStart = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

      // 分别获取本周和上周的会话
      const thisWeekSessions = allData.sessions.filter(session =>
        new Date(session.startTime) >= thisWeekStart
      );

      const lastWeekSessions = allData.sessions.filter(session => {
        const sessionDate = new Date(session.startTime);
        return sessionDate >= lastWeekStart && sessionDate < thisWeekStart;
      });

      // 计算各项指标
      const calculateMetrics = (sessions) => {
        if (sessions.length === 0) return [0, 0, 0, 0, 0];

        // 注意力分数
        const avgAttention = sessions.reduce((sum, s) => sum + (s.statistics.avgAttentionScore || 0), 0) / sessions.length;

        // 专注度（基于高注意力等级的比例）
        let totalHighFocus = 0;
        let totalDetections = 0;
        sessions.forEach(s => {
          if (s.statistics.attentionLevels) {
            totalHighFocus += (s.statistics.attentionLevels[2] || 0) + (s.statistics.attentionLevels[3] || 0);
            totalDetections += Object.values(s.statistics.attentionLevels).reduce((sum, count) => sum + count, 0);
          }
        });
        const focusRate = totalDetections > 0 ? (totalHighFocus / totalDetections) * 100 : 0;

        // 情绪稳定性（基于注意力分数的标准差）
        const scores = sessions.map(s => s.statistics.avgAttentionScore || 0);
        const variance = scores.reduce((sum, score) => sum + Math.pow(score - avgAttention, 2), 0) / scores.length;
        const stability = Math.max(0, 100 - Math.sqrt(variance));

        // 训练时长（平均每天，以分钟为单位）
        const totalDuration = sessions.reduce((sum, s) => sum + (s.duration || 0), 0);
        const avgDailyDuration = Math.round(totalDuration / 7); // 平均每天的分钟数

        // 一致性（训练频率）
        const consistency = Math.min(100, (sessions.length / 7) * 100);

        return [
          Math.round(avgAttention),
          Math.round(focusRate),
          Math.round(stability),
          Math.round(avgDailyDuration),
          Math.round(consistency)
        ];
      };

      return {
        thisWeek: calculateMetrics(thisWeekSessions),
        lastWeek: calculateMetrics(lastWeekSessions)
      };
    },

    // 计算本周训练时长趋势
    calculateWeeklyTrend(sessions) {
      if (!sessions || sessions.length === 0) {
        return {
          type: 'neutral',
          text: '暂无数据'
        };
      }

      const now = new Date();
      const thisWeekStart = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      const lastWeekStart = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

      // 计算本周和上周的训练时长
      const thisWeekSessions = sessions.filter(session =>
        new Date(session.startTime) >= thisWeekStart
      );

      const lastWeekSessions = sessions.filter(session => {
        const sessionDate = new Date(session.startTime);
        return sessionDate >= lastWeekStart && sessionDate < thisWeekStart;
      });

      const thisWeekDuration = thisWeekSessions.reduce((sum, s) => sum + (s.duration || 0), 0);
      const lastWeekDuration = lastWeekSessions.reduce((sum, s) => sum + (s.duration || 0), 0);

      if (lastWeekDuration === 0) {
        if (thisWeekDuration > 0) {
          return {
            type: 'positive',
            text: `+${(thisWeekDuration / 60).toFixed(1)}h 本周`
          };
        } else {
          return {
            type: 'neutral',
            text: '暂无训练'
          };
        }
      }

      const difference = thisWeekDuration - lastWeekDuration;
      const differenceHours = Math.abs(difference) / 60;

      if (difference > 0) {
        return {
          type: 'positive',
          text: `+${differenceHours.toFixed(1)}h 本周`
        };
      } else if (difference < 0) {
        return {
          type: 'negative',
          text: `-${differenceHours.toFixed(1)}h 本周`
        };
      } else {
        return {
          type: 'neutral',
          text: '与上周持平'
        };
      }
    },

    // 生成测试数据
    generateTestData() {
      console.log('🧪 开始生成测试数据...');

      try {
        const result = testDataGenerator.generateCompleteTestDataset();
        console.log('✅ 测试数据生成完成:', result);

        // 重新加载数据
        this.loadRealData();

        // 更新图表
        this.updateAvgAttentionScore('week');
        this.updateEmotionChart();

        // 显示成功消息
        alert(`测试数据生成成功！\n会话数: ${result.sessions}\n总检测次数: ${result.totalDetections}\n总时长: ${result.totalDuration}分钟`);

      } catch (error) {
        console.error('生成测试数据失败:', error);
        alert('生成测试数据失败，请查看控制台了解详情');
      }
    },

    // 强制清除测试数据（页面加载时调用）
    forceCleanTestData() {
      const allData = dataCollectionService.getAllData();
      if (allData && allData.sessions && allData.sessions.length > 0) {
        console.log('🔍 检查当前存储的数据:', allData.sessions);

        // 检查数据的时间戳，删除未来时间的数据
        const now = new Date();

        const validSessions = allData.sessions.filter(session => {
          const sessionDate = new Date(session.startTime);
          const isValidTime = sessionDate <= now; // 不能是未来时间
          const isTestData = session.id && session.id.startsWith('test_');

          if (!isValidTime) {
            console.log('❌ 发现未来时间的数据:', session.startTime, session);
          }
          if (isTestData) {
            console.log('❌ 发现测试数据:', session.id);
          }

          return isValidTime && !isTestData;
        });

        if (validSessions.length !== allData.sessions.length) {
          console.log('🧹 清除虚假/测试数据，保留有效数据:', validSessions.length);
          if (validSessions.length === 0) {
            dataCollectionService.clearAllData();
          } else {
            // 只保留有效数据
            const cleanData = { ...allData, sessions: validSessions };
            localStorage.setItem('adhd_detection_data', JSON.stringify(cleanData));
          }
          this.hasRealData = validSessions.length > 0;
        }
      }
    },

    // 检查并清除测试数据和虚假数据
    checkAndClearTestData() {
      const allData = dataCollectionService.getAllData();
      if (allData && allData.sessions && allData.sessions.length > 0) {
        const now = new Date();

        // 检查是否有测试数据或未来时间的数据
        const hasInvalidData = allData.sessions.some(session => {
          const isTestData = session.id && session.id.startsWith('test_');
          const sessionDate = new Date(session.startTime);
          const isFutureData = sessionDate > now;
          return isTestData || isFutureData;
        });

        if (hasInvalidData) {
          console.log('⚠️ 检测到测试数据或虚假数据，自动清除以显示真实数据');

          // 过滤出有效数据
          const validSessions = allData.sessions.filter(session => {
            const isTestData = session.id && session.id.startsWith('test_');
            const sessionDate = new Date(session.startTime);
            const isFutureData = sessionDate > now;
            return !isTestData && !isFutureData;
          });

          if (validSessions.length === 0) {
            dataCollectionService.clearAllData();
          } else {
            const cleanData = { ...allData, sessions: validSessions };
            localStorage.setItem('adhd_detection_data', JSON.stringify(cleanData));
          }

          this.hasRealData = validSessions.length > 0;
          this.resetAllData();
          return true;
        }
      }
      return false;
    },

    // 手动清除虚假数据
    cleanFakeData() {
      const allData = dataCollectionService.getAllData();
      if (!allData || !allData.sessions || allData.sessions.length === 0) {
        alert('当前没有任何数据');
        return;
      }

      const now = new Date();
      console.log('🔍 当前时间:', now.toLocaleString());
      console.log('🔍 检查所有数据:', allData.sessions);

      // 分析数据
      let testDataCount = 0;
      let futureDataCount = 0;
      let validDataCount = 0;

      allData.sessions.forEach(session => {
        const sessionDate = new Date(session.startTime);
        const isTestData = session.id && session.id.startsWith('test_');
        const isFutureData = sessionDate > now;

        if (isTestData) {
          testDataCount++;
          console.log('❌ 测试数据:', session.id, session.startTime);
        } else if (isFutureData) {
          futureDataCount++;
          console.log('❌ 未来时间数据:', session.startTime, '(当前时间:', now.toLocaleString(), ')');
        } else {
          validDataCount++;
          console.log('✅ 有效数据:', session.startTime);
        }
      });

      if (testDataCount === 0 && futureDataCount === 0) {
        alert('没有发现虚假数据，所有数据都是有效的');
        return;
      }

      const message = `发现虚假数据:\n测试数据: ${testDataCount} 条\n未来时间数据: ${futureDataCount} 条\n有效数据: ${validDataCount} 条\n\n是否清除虚假数据？`;

      if (confirm(message)) {
        // 只保留有效数据
        const validSessions = allData.sessions.filter(session => {
          const sessionDate = new Date(session.startTime);
          const isTestData = session.id && session.id.startsWith('test_');
          const isFutureData = sessionDate > now;
          return !isTestData && !isFutureData;
        });

        if (validSessions.length === 0) {
          dataCollectionService.clearAllData();
          this.hasRealData = false;
        } else {
          const cleanData = { ...allData, sessions: validSessions };
          localStorage.setItem('adhd_detection_data', JSON.stringify(cleanData));
          this.hasRealData = true;
        }

        this.resetAllData();
        this.loadRealData();
        this.updateAllCharts();

        alert(`虚假数据清除完成！\n保留有效数据: ${validSessions.length} 条`);
      }
    },

    // 重置所有数据到初始状态
    resetAllData() {
      this.trainingStats = {
        totalSessions: 0,
        totalHours: 0,
        totalDuration: 0,
        totalDetections: 0
      };
      this.avgAttentionScore = '0.0';
      this.focusSuccessRate = 0;
      this.consecutiveDays = 0;
      this.emotionStabilityIndex = 0;
      this.maxAttentionScore = '0.0';

      this.avgDailyTime = 0;
      this.bestTimeSlot = '--:--';

      this.focusDistribution = {
        high: 0,
        normal: 0,
        low: 0,
        distracted: 0
      };

      // 更新图表显示空数据
      this.updateAllCharts();
    },

    // 清除所有数据
    clearAllData() {
      if (confirm('确定要清除所有检测数据吗？此操作不可恢复！')) {
        try {
          dataCollectionService.clearAllData();

          // 重置界面数据
          this.hasRealData = false;
          this.resetAllData();

          console.log('🗑️ 所有数据已清除');
          alert('所有数据已清除，界面将只显示真实检测数据');

        } catch (error) {
          console.error('清除数据失败:', error);
          alert('清除数据失败，请查看控制台了解详情');
        }
      }
    },



    // 获取注意力趋势数据（仅真实数据）
    getAttentionTrendData(range) {
      const allData = dataCollectionService.getAllData();

      // 如果没有数据，返回空数据
      if (!allData || !allData.sessions || allData.sessions.length === 0) {
        return this.getEmptyTrendData(range);
      }

      switch (range) {
        case 'week':
          return this.getWeeklyTrendData(allData);
        case 'month':
          return this.getMonthlyTrendData(allData);
        case 'year':
          return this.getYearlyTrendData(allData);
        default:
          return this.getWeeklyTrendData(allData);
      }
    },

    // 获取空趋势数据
    getEmptyTrendData(range) {
      switch (range) {
        case 'week':
          return {
            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            data: [0, 0, 0, 0, 0, 0, 0]
          };
        case 'month':
          return {
            labels: ['第1周', '第2周', '第3周', '第4周'],
            data: [0, 0, 0, 0]
          };
        case 'year':
          return {
            labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
            data: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]
          };
        default:
          return {
            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            data: [0, 0, 0, 0, 0, 0, 0]
          };
      }
    },

    // 获取周趋势数据
    getWeeklyTrendData(allData) {
      const labels = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
      const data = new Array(7).fill(0);
      const counts = new Array(7).fill(0);

      // 获取最近7天的数据
      const now = new Date();
      const weekStart = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000);

      console.log('📊 周趋势数据计算:');
      console.log('  当前时间:', now.toISOString());
      console.log('  周开始时间:', weekStart.toISOString());
      console.log('  总会话数:', allData.sessions.length);

      allData.sessions.forEach((session, index) => {
        const sessionDate = new Date(session.startTime);
        console.log(`  会话 ${index + 1}:`, {
          startTime: session.startTime,
          sessionDate: sessionDate.toISOString(),
          isInWeek: sessionDate >= weekStart,
          dayOfWeek: sessionDate.getDay(),
          avgScore: session.statistics.avgAttentionScore
        });

        if (sessionDate >= weekStart) {
          const dayOfWeek = sessionDate.getDay();
          const adjustedDay = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // 调整为周一=0

          data[adjustedDay] += session.statistics.avgAttentionScore || 0;
          counts[adjustedDay]++;

          console.log(`    添加到 ${labels[adjustedDay]} (索引${adjustedDay}):`, {
            score: session.statistics.avgAttentionScore,
            newTotal: data[adjustedDay],
            count: counts[adjustedDay]
          });
        }
      });

      // 计算平均值
      for (let i = 0; i < 7; i++) {
        data[i] = counts[i] > 0 ? Math.round(data[i] / counts[i]) : 0;
      }

      console.log('  最终数据:', { labels, data, counts });
      return { labels, data };
    },

    // 获取月趋势数据
    getMonthlyTrendData(allData) {
      const labels = ['第1周', '第2周', '第3周', '第4周'];
      const data = new Array(4).fill(0);
      const counts = new Array(4).fill(0);

      // 获取最近4周的数据
      const now = new Date();

      allData.sessions.forEach(session => {
        const sessionDate = new Date(session.startTime);
        const weeksDiff = Math.floor((now - sessionDate) / (7 * 24 * 60 * 60 * 1000));

        if (weeksDiff >= 0 && weeksDiff < 4) {
          const weekIndex = 3 - weeksDiff; // 最新的周在最后
          data[weekIndex] += session.statistics.avgAttentionScore || 0;
          counts[weekIndex]++;
        }
      });

      // 计算平均值
      for (let i = 0; i < 4; i++) {
        data[i] = counts[i] > 0 ? Math.round(data[i] / counts[i]) : 0;
      }

      return { labels, data };
    },

    // 获取年趋势数据
    getYearlyTrendData(allData) {
      const labels = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
      const data = new Array(12).fill(0);
      const counts = new Array(12).fill(0);

      allData.sessions.forEach(session => {
        const sessionDate = new Date(session.startTime);
        const month = sessionDate.getMonth();

        data[month] += session.statistics.avgAttentionScore || 0;
        counts[month]++;
      });

      // 计算平均值
      for (let i = 0; i < 12; i++) {
        data[i] = counts[i] > 0 ? Math.round(data[i] / counts[i]) : 0;
      }

      return { labels, data };
    },



    // 订阅数据更新事件
    subscribeToDataUpdates() {
      console.log('📡 数据分析界面订阅数据更新事件...');

      // 订阅检测数据更新
      this.onDetectionUpdate = (event) => {
        console.log('🔄 数据分析界面收到检测数据更新:', event.detail);
        console.log('📊 当前时间:', new Date().toISOString());

        // 立即检查数据状态
        const allData = dataCollectionService.getAllData();
        console.log('📊 当前数据状态:', {
          sessions: allData?.sessions?.length || 0,
          totalDetections: allData?.statistics?.totalDetections || 0,
          lastSession: allData?.sessions?.[allData.sessions.length - 1]?.id || 'none'
        });

        // 延迟更新，确保数据已保存
        setTimeout(() => {
          console.log('🔄 开始更新数据分析界面...');
          this.loadRealData();
          this.updateAvgAttentionScore('week');
          // 简单粗暴：重新创建所有图表
          this.recreateAllCharts();
          console.log('✅ 检测数据界面更新完成');
        }, 100);
      };

      // 订阅会话开始事件
      this.onSessionStart = (event) => {
        console.log('🎯 数据分析界面收到会话开始:', event.detail);
        // 会话开始时更新界面状态
        this.loadRealData();
      };

      // 订阅会话结束事件
      this.onSessionEnd = (event) => {
        console.log('🏁 数据分析界面收到会话结束:', event.detail);

        // 延迟更新，确保会话数据已完全保存
        setTimeout(() => {
          this.loadRealData();
          this.updateAllCharts();
          console.log('✅ 会话结束界面更新完成');
        }, 200);
      };

      // 订阅统计数据更新
      this.onStatisticsUpdate = (event) => {
        console.log('📊 数据分析界面收到统计数据更新:', event.detail);

        setTimeout(() => {
          this.loadRealData();
          this.updateAvgAttentionScore('week');
          console.log('✅ 统计数据界面更新完成');
        }, 100);
      };

      // 注册事件监听器
      dataSync.subscribe('detection-updated', this.onDetectionUpdate);
      dataSync.subscribe('session-started', this.onSessionStart);
      dataSync.subscribe('session-ended', this.onSessionEnd);
      dataSync.subscribe('statistics-updated', this.onStatisticsUpdate);

      console.log('✅ 数据分析界面事件订阅完成');
    },

    // 取消数据更新订阅
    unsubscribeFromDataUpdates() {
      console.log('📡 取消数据分析界面事件订阅...');

      if (this.onDetectionUpdate) {
        dataSync.unsubscribe('detection-updated', this.onDetectionUpdate);
      }
      if (this.onSessionStart) {
        dataSync.unsubscribe('session-started', this.onSessionStart);
      }
      if (this.onSessionEnd) {
        dataSync.unsubscribe('session-ended', this.onSessionEnd);
      }
      if (this.onStatisticsUpdate) {
        dataSync.unsubscribe('statistics-updated', this.onStatisticsUpdate);
      }

      console.log('✅ 数据分析界面事件订阅已取消');
    },

    // 更新所有图表
    updateAllCharts() {
      this.$nextTick(() => {
        console.log('📈 开始更新所有图表...');

        const chartUpdates = [
          { name: '注意力趋势图', method: () => this.updateAttentionChart() },
          { name: '情绪分析图', method: () => this.updateEmotionChart() },
          { name: '专注度分布图', method: () => this.updateFocusDistributionChart() },
          { name: '情绪雷达图', method: () => this.updateEmotionRadarChart() },
          { name: '每日训练图', method: () => this.updateDailyTrainingChart() },
          { name: '进度对比图', method: () => this.updateProgressComparisonChart() }
        ];

        chartUpdates.forEach(chart => {
          try {
            chart.method();
            console.log(`✅ ${chart.name}更新成功`);
          } catch (error) {
            console.error(`❌ ${chart.name}更新失败:`, error);
          }
        });

        console.log('📈 图表更新完成');
      });
    },

    // 刷新数据（手动刷新按钮）
    refreshData() {
      console.log('🔄 手动刷新数据...');

      // 调试数据状态
      this.debugDataStatus();

      this.loadRealData();
      this.updateAllCharts();

      // 显示刷新成功提示
      this.$nextTick(() => {
        console.log('✅ 数据刷新完成');
      });
    },

    // 调试数据状态
    debugDataStatus() {
      console.log('=== 数据分析界面调试信息 ===');

      // 检查localStorage数据
      const allData = dataCollectionService.getAllData();
      const currentSession = dataCollectionService.getCurrentSession();

      console.log('📊 localStorage全部数据:', allData);
      console.log('🎯 当前活动会话:', currentSession);

      if (allData && allData.sessions) {
        console.log('📈 历史会话数量:', allData.sessions.length);
        allData.sessions.forEach((session, index) => {
          console.log(`会话 ${index + 1}:`, {
            id: session.id,
            startTime: session.startTime,
            duration: session.duration,
            detections: session.detections?.length || 0
          });
        });
      }

      if (currentSession) {
        console.log('🔍 当前会话详情:', {
          id: currentSession.id,
          startTime: currentSession.startTime,
          detections: currentSession.detections?.length || 0,
          statistics: currentSession.statistics
        });
      }

      console.log('=== 调试信息结束 ===');
    },

    // 添加页面可见性监听器
    addVisibilityListener() {
      // 监听页面可见性变化
      this.handleVisibilityChange = () => {
        if (!document.hidden) {
          console.log('📱 数据分析页面变为可见，刷新数据...');
          setTimeout(() => {
            this.loadRealData();
            this.updateAllCharts();
          }, 200);
        }
      };

      document.addEventListener('visibilitychange', this.handleVisibilityChange);
      console.log('✅ 页面可见性监听器已添加');
    },

    // 移除页面可见性监听器
    removeVisibilityListener() {
      if (this.handleVisibilityChange) {
        document.removeEventListener('visibilitychange', this.handleVisibilityChange);
        console.log('✅ 页面可见性监听器已移除');
      }
    },

    // 更新注意力趋势图
    updateAttentionChart() {
      console.log(`更新注意力图表，时间范围: ${this.attentionChartRange}`);

      // 检查DOM元素是否存在且可用
      const canvas = this.$refs.attentionTrendChart;
      if (!canvas || typeof canvas.getContext !== 'function') {
        console.warn('注意力趋势图表canvas元素未找到或不可用');
        return;
      }

      // 获取真实数据
      const chartData = this.getAttentionTrendData(this.attentionChartRange);

      // 销毁现有图表
      this.ultraSafeDestroyChart('attentionTrendChart');

      try {
        // 使用安全包装器创建新图表
        this.attentionTrendChart = this.createSafeChart('attentionTrendChart', {
          type: 'line',
          data: {
            labels: chartData.labels,
            datasets: [{
              label: '注意力分数',
              data: chartData.data,
              borderColor: 'rgba(78, 115, 223, 1)',
              backgroundColor: 'rgba(78, 115, 223, 0.1)',
              borderWidth: 2,
              fill: true,
              tension: 0.4
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                max: 100
              }
            }
          }
        }, 'attentionTrendChart');

        if (!this.attentionTrendChart) {
          console.error('创建注意力图表失败');
        }
      } catch (error) {
        console.error('创建注意力图表失败:', error);
      }
    },

    // 更新情绪分布图
    async updateEmotionChart() {
      console.log(`更新情绪图表，时间范围: ${this.emotionChartRange}`);

      // 检查DOM元素是否存在且可用
      let canvas = this.$refs.emotionDistributionChart;
      if (!canvas || typeof canvas.getContext !== 'function') {
        console.warn('情绪分布图表canvas元素未找到或不可用');

        // 使用$nextTick等待DOM更新，避免递归调用
        await this.$nextTick();

        // 再次检查
        canvas = this.$refs.emotionDistributionChart;
        if (!canvas || typeof canvas.getContext !== 'function') {
          console.error('情绪分布图表canvas元素仍然不可用，跳过更新');
          return;
        }
      }

      // 1. 获取真实数据
      const emotionData = this.getEmotionDistributionData();

      // 2. 处理数据
      const labels = emotionData.map(item => item.emotion_type);
      const data = emotionData.map(item => item.count);

      // 3. 再次检查DOM元素（异步操作后可能发生变化）
      if (!this.$refs.emotionDistributionChart) {
        console.warn('异步操作后canvas元素丢失');
        return;
      }

      // 4. 更新或创建图表
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        console.warn('无法获取情绪图表canvas上下文');
        return;
      }

      // 确保canvas元素仍然存在于DOM中
      if (!canvas.parentNode) {
        console.warn('情绪图表Canvas元素已从DOM中移除，跳过图表创建');
        return;
      }

      // 销毁现有图表
      if (this.emotionChart && typeof this.emotionChart.destroy === 'function') {
        try {
          this.emotionChart.destroy();
        } catch (error) {
          console.warn('销毁情绪图表时出错:', error);
        }
        this.emotionChart = null;
      }

      if (!this.emotionChart) {
        this.emotionChart = new Chart(ctx, {
          type: 'doughnut',
          data: {
            labels: labels,
            datasets: [{
              label: '情绪分布',
              data: data,
              backgroundColor: [
                'rgba(78, 115, 223, 0.8)',
                'rgba(66, 184, 131, 0.8)',
                'rgba(246, 194, 62, 0.8)',
                'rgba(246, 130, 62, 0.8)',
                'rgba(231, 74, 59, 0.8)'
              ],
              borderColor: '#fff',
              borderWidth: 2,
              hoverBackgroundColor: [
                'rgba(78, 115, 223, 1)',
                'rgba(66, 184, 131, 1)',
                'rgba(246, 194, 62, 1)',
                'rgba(246, 130, 62, 1)',
                'rgba(231, 74, 59, 1)'
              ],
              hoverBorderWidth: 3,
              clip: false
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '65%',
            plugins: {
              legend: {
                position: 'right',
                labels: {
                  boxWidth: 15,
                  padding: 15,
                  font: { size: 12 }
                }
              },
              tooltip: {
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                titleColor: '#333',
                bodyColor: '#666',
                borderColor: '#ddd',
                borderWidth: 1,
                cornerRadius: 6,
                displayColors: true,
                callbacks: {
                  label: function(context) {
                    const label = context.label || '';
                    const value = context.parsed || 0;
                    const total = context.dataset.data.reduce((acc, val) => acc + val, 0);
                    const percentage = total ? Math.round((value / total) * 100) : 0;
                    return `${label}: ${percentage}%`;
                  }
                }
              }
            },
            animation: {
              duration: 300
            }
          }
        });
      } else {
        // 安全地更新现有图表
        try {
          if (this.emotionChart && this.emotionChart.data && this.emotionChart.data.datasets) {
            // 完全重新设置数据
            this.emotionChart.data.labels = labels;
            this.emotionChart.data.datasets[0] = {
              label: '情绪分布',
              data: data,
              backgroundColor: [
                'rgba(78, 115, 223, 0.8)',
                'rgba(66, 184, 131, 0.8)',
                'rgba(246, 194, 62, 0.8)',
                'rgba(246, 130, 62, 0.8)',
                'rgba(231, 74, 59, 0.8)'
              ],
              borderColor: '#fff',
              borderWidth: 2,
              hoverBackgroundColor: [
                'rgba(78, 115, 223, 1)',
                'rgba(66, 184, 131, 1)',
                'rgba(246, 194, 62, 1)',
                'rgba(246, 130, 62, 1)',
                'rgba(231, 74, 59, 1)'
              ],
              hoverBorderWidth: 3,
              clip: false
            };
            this.emotionChart.update('none');
          }
        } catch (error) {
          console.error('更新情绪图表时出错:', error);
          // 如果更新失败，销毁并重新创建图表
          if (this.emotionChart) {
            this.emotionChart.destroy();
            this.emotionChart = null;
          }
          // 重新调用方法创建新图表
          setTimeout(() => {
            this.updateEmotionChart();
          }, 100);
        }
      }
    },



    // 更新注意力维度分析雷达图
    updateEmotionRadarChart() {
      const success = this.safeUpdateChart('emotionRadarChart', () => {
        const emotionData = this.getEmotionRadarData();
        this.emotionRadarChart.data.datasets[0].data = emotionData.thisWeek;
        this.emotionRadarChart.data.datasets[1].data = emotionData.lastWeek;
      });

      if (!success) {
        console.log('🔄 重新创建注意力维度分析雷达图');
        this.$nextTick(() => {
          this.initEmotionRadarChart();
        });
      }
    },

    // 更新每日训练图
    updateDailyTrainingChart() {
      const success = this.safeUpdateChart('dailyTrainingChart', () => {
        const dailyData = this.getDailyTrainingData();
        this.dailyTrainingChart.data.datasets[0].data = dailyData.durations;
        this.dailyTrainingChart.data.datasets[0].backgroundColor = dailyData.colors;

        // 动态调整Y轴最大值
        const maxDuration = Math.max(...dailyData.durations);
        if (this.dailyTrainingChart.options &&
            this.dailyTrainingChart.options.scales &&
            this.dailyTrainingChart.options.scales.y) {
          this.dailyTrainingChart.options.scales.y.max = Math.max(60, maxDuration + 10);
        }
      });

      if (!success) {
        console.log('🔄 重新创建每日训练图');
        this.$nextTick(() => {
          this.initDailyTrainingChart();
        });
      }
    },

    // 更新进步对比图
    updateProgressComparisonChart() {
      const success = this.safeUpdateChart('progressComparisonChart', () => {
        const progressData = this.getProgressComparisonData();
        this.progressComparisonChart.data.datasets[0].data = progressData.thisWeek;
        this.progressComparisonChart.data.datasets[1].data = progressData.lastWeek;
      });

      if (!success) {
        console.log('🔄 重新创建进步对比图');
        this.$nextTick(() => {
          this.initProgressComparisonChart();
        });
      }
    },

    // 更新平均注意力分数（仅使用真实数据）
    updateAvgAttentionScore(range = 'week') {
      const allData = dataCollectionService.getAllData();

      if (!allData || !allData.sessions || allData.sessions.length === 0) {
        this.avgAttentionScore = '0.0';
        return;
      }

      // 根据时间范围过滤数据
      const filteredSessions = this.getSessionsByRange(allData.sessions, range);

      if (filteredSessions.length === 0) {
        this.avgAttentionScore = '0.0';
        return;
      }

      // 计算平均注意力分数
      const totalScore = filteredSessions.reduce((sum, session) =>
        sum + (session.statistics.avgAttentionScore || 0), 0);
      const avgScore = totalScore / filteredSessions.length;

      this.avgAttentionScore = avgScore.toFixed(1);

      console.log(`📊 ${range}范围内平均注意力分数:`, this.avgAttentionScore);
    },

    // 根据时间范围获取会话数据
    getSessionsByRange(sessions, range) {
      const now = new Date();
      let startDate;

      switch (range) {
        case 'week':
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case 'month':
          startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        case 'year':
          startDate = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
          break;
        default:
          startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      }

      return sessions.filter(session =>
        new Date(session.startTime) >= startDate
      );
    },

    // 调试数据问题
    debugDataIssues() {
      console.log('=== 数据分析界面调试 ===');

      const allData = dataCollectionService.getAllData();
      console.log('📊 所有数据:', allData);

      if (allData && allData.sessions) {
        console.log('📈 会话总数:', allData.sessions.length);
        console.log('📊 统计数据:', allData.statistics);

        // 检查每个会话的详细信息
        allData.sessions.forEach((session, index) => {
          console.log(`会话 ${index + 1}:`, {
            id: session.id,
            startTime: session.startTime,
            endTime: session.endTime,
            duration: session.duration,
            detections: session.detections?.length || 0,
            avgScore: session.statistics?.avgAttentionScore || 0,
            totalDetections: session.statistics?.totalDetections || 0
          });
        });

        // 检查今天的数据
        const today = new Date().toISOString().split('T')[0];
        console.log('📅 今天日期:', today);
        console.log('📅 今天的数据:', allData.dailyData?.[today]);

        // 检查本周数据
        const weekData = this.getWeeklyTrendData(allData);
        console.log('📊 本周趋势数据:', weekData);
      }

      alert('数据调试完成，请查看控制台输出');
    },

    // 清理旧的测试数据
    clearOldTestData() {
      if (confirm('确定要清理可能的测试数据吗？这将删除所有检测次数异常的会话。')) {
        const allData = dataCollectionService.getAllData();
        if (allData && allData.sessions) {
          // 保留检测次数合理的会话（1-100次之间）
          const validSessions = allData.sessions.filter(session => {
            const detections = session.statistics?.totalDetections || 0;
            return detections > 0 && detections <= 100;
          });

          console.log(`清理前会话数: ${allData.sessions.length}`);
          console.log(`清理后会话数: ${validSessions.length}`);

          // 更新数据
          allData.sessions = validSessions;

          // 重新计算统计数据
          allData.statistics = {
            totalSessions: validSessions.length,
            totalDuration: validSessions.reduce((sum, s) => sum + (s.duration || 0), 0),
            totalDetections: validSessions.reduce((sum, s) => sum + (s.statistics?.totalDetections || 0), 0),
            lastSessionDate: validSessions.length > 0 ? validSessions[validSessions.length - 1].startTime : null
          };

          // 保存数据
          localStorage.setItem('adhd_detection_data', JSON.stringify(allData));

          // 刷新界面
          this.loadRealData();
          this.updateAllCharts();

          alert(`数据清理完成！删除了 ${allData.sessions.length - validSessions.length} 个异常会话`);
        }
      }
    },

    // 刷新所有数据
    refreshAllData() {
      console.log('🔄 刷新所有数据...');
      this.loadRealData();
      this.updateAllCharts();
      alert('数据已刷新');
    },

    // 验证数据传输
    validateDataTransmission() {
      console.log('🔍 验证数据传输...');

      const allData = dataCollectionService.getAllData();
      const today = new Date().toISOString().split('T')[0];

      console.log('📊 数据验证结果:', {
        totalSessions: allData?.sessions?.length || 0,
        totalDetections: allData?.statistics?.totalDetections || 0,
        todayData: allData?.dailyData?.[today] || null,
        lastSession: allData?.sessions?.[allData.sessions.length - 1] || null
      });

      // 检查今天是否有数据
      const todayData = allData?.dailyData?.[today];
      if (!todayData) {
        console.warn('⚠️ 今天没有检测数据');
        return false;
      }

      // 检查数据是否与会话匹配
      const todaySessions = allData.sessions.filter(session => {
        const sessionDate = new Date(session.startTime).toISOString().split('T')[0];
        return sessionDate === today;
      });

      console.log('📅 今天的会话:', todaySessions.length);
      console.log('📊 今天的统计:', todayData);

      if (todaySessions.length > 0) {
        console.log('✅ 数据传输验证通过');
        alert(`✅ 数据传输正常！\n📅 今日会话: ${todaySessions.length} 个\n🔢 总检测次数: ${todayData.totalDetections || 0}\n⏱️ 总时长: ${Math.round((todayData.totalDuration || 0) / 60)} 分钟`);

        // 强制刷新图表显示最新数据
        this.forceRefreshCharts();
        return true;
      } else {
        console.warn('⚠️ 今天的会话数据不匹配');
        alert('⚠️ 数据传输异常：今天有统计数据但没有会话记录');
        return false;
      }
    },

    // 强制刷新所有图表
    forceRefreshCharts() {
      console.log('🔄 强制刷新所有图表...');

      // 销毁所有现有图表
      this.destroyChart('focusDistributionChart');
      this.destroyChart('emotionRadarChart');
      this.destroyChart('dailyTrainingChart');
      this.destroyChart('progressComparisonChart');
      this.destroyChart('attentionChart');
      this.destroyChart('emotionChart');

      // 等待DOM更新后重新初始化
      this.$nextTick(() => {
        this.loadRealData();
        this.initAllCharts();
        console.log('✅ 图表刷新完成');
      });
    }
  }
}
</script>

<style scoped>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

/* 核心变量 */
:root {
  --primary-color: #4e73df;
  --primary-light: #e8efff;
  --primary-dark: #3a5bbd;
  --secondary-color: #f8f9fc;
  --success-color: #1cc88a;
  --info-color: #36b9cc;
  --warning-color: #f6c23e;
  --danger-color: #e74a3b;
  --text-color: #4e4e4e;
  --text-light: #7e7e7e;
  --border-color: #e3e6f0;
  --border-radius: 12px;
  --card-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  --transition-speed: 0.3s;
  --header-height: 60px;
}

.data-analysis-page {
  background-color: #f5f7fa;
  color: var(--text-color);
  min-height: 100vh;
}

/* 头部样式 */
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 25px;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  height: var(--header-height);
}

.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
}

.logo i {
  font-size: 24px;
  margin-right: 10px;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #f1f2f6;
  border-radius: 20px;
  padding: 5px 15px;
  width: 300px;
}

.search-bar input {
  border: none;
  background: none;
  outline: none;
  width: 100%;
  padding: 5px;
}

.search-bar button {
  background: none;
  border: none;
  color: #777;
  cursor: pointer;
}

.user-actions {
  display: flex;
  align-items: center;
}

.actions {
  display: flex;
  margin-right: 20px;
}

.icon-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 18px;
  margin-left: 15px;
  position: relative;
  cursor: pointer;
}

.badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--danger-color);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.username {
  margin-right: 10px;
  font-weight: 500;
}

.avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
}

/* 内容区域样式 */
.content-wrapper {
  display: flex;
  height: calc(100vh - var(--header-height) - 40px);
}

/* 导航栏样式 */
nav {
  width: 80px;
  background: linear-gradient(180deg, #434d73 0%, #394366 100%);
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
  transition: width var(--transition-speed);
  overflow-y: auto;
}

nav.expanded {
  width: 180px;
}

.nav-item {
  color: white;
  text-decoration: none;
  margin: 10px 0;
  padding: 12px;
  border-radius: 10px;
  width: 85%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: all var(--transition-speed);
  position: relative;
}

nav.expanded .nav-item {
  flex-direction: row;
  justify-content: flex-start;
  gap: 15px;
  padding: 12px 15px;
}

.nav-item i {
  font-size: 1.3rem;
  margin-bottom: 5px;
}

nav.expanded .nav-item i {
  margin-bottom: 0;
}

.nav-item span {
  font-size: 0.85rem;
  opacity: 0;
  transition: opacity var(--transition-speed);
  white-space: nowrap;
}

nav.expanded .nav-item span {
  opacity: 1;
}

.nav-item:hover, .nav-item.active {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.nav-item::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: white;
  opacity: 0;
  transition: opacity var(--transition-speed);
}

.nav-item:hover::after, .nav-item.active::after {
  opacity: 1;
}

/* 主内容区样式 */
.main-area {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

/* 数据分析容器 */
.data-analysis-container {
  background-color: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: box-shadow 0.3s ease;
}

.data-analysis-container:hover {
  box-shadow: 0 12px 28px rgba(71, 118, 230, 0.15);
}

/* 区域头部样式 */
.area-header {
  padding: 18px 25px;
  background: linear-gradient(to right, var(--primary-light), #f8f9fc);
}

.area-header h3 {
  font-size: 20px;
  color: var(--primary-dark);
  display: flex;
  align-items: center;
  margin: 0;
}

.area-header h3 i {
  margin-right: 12px;
  font-size: 1.3rem;
  color: #4e73df;
  font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  display: inline-block;
  min-width: 22px;
  text-align: center;
  line-height: 1;
}

/* 数据状态指示器 */
.data-status-indicator {
  margin-top: 15px;
  padding: 12px 16px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 14px;
  font-weight: 500;
}

.data-status-indicator.has-data {
  background: rgba(40, 167, 69, 0.15);
  border: 1px solid rgba(40, 167, 69, 0.3);
  color: #155724;
}

.data-status-indicator.no-data {
  background: rgba(255, 193, 7, 0.15);
  border: 1px solid rgba(255, 193, 7, 0.3);
  color: #856404;
}

.refresh-btn {
  margin-left: auto;
  padding: 6px 12px;
  background: #4e73df;
  border: 1px solid #4e73df;
  border-radius: 6px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.refresh-btn:hover {
  background: #3a5bbd;
  transform: translateY(-1px);
}

.action-buttons {
  display: flex;
  gap: 10px;
  margin-left: auto;
}

.test-data-btn {
  padding: 6px 12px;
  background: #28a745;
  border: 1px solid #28a745;
  border-radius: 6px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.test-data-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}

.clean-fake-btn {
  padding: 6px 12px;
  background: #f39c12;
  border: 1px solid #f39c12;
  border-radius: 6px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.clean-fake-btn:hover {
  background: #e67e22;
  transform: translateY(-1px);
}

.clear-data-btn {
  padding: 6px 12px;
  background: #dc3545;
  border: 1px solid #dc3545;
  border-radius: 6px;
  color: white;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.clear-data-btn:hover {
  background: #c82333;
  transform: translateY(-1px);
}

/* 主内容区域 */
.main-content {
  padding: 20px;
  height: 100%;
  overflow-y: auto;
}

/* 数据概览卡片 */
.data-overview-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 30px;
}

.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  padding: 24px;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  position: relative;
  overflow: hidden;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #4e73df, #36b9cc);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.card:hover::before {
  opacity: 1;
}

.card-icon {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  background: linear-gradient(135deg, #4e73df 0%, #36b9cc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 18px;
  box-shadow: 0 4px 12px rgba(78, 115, 223, 0.3);
}

.card-icon i {
  color: white;
  font-size: 22px;
}

.card-content {
  flex: 1;
}

.card-content h3 {
  font-size: 14px;
  color: #6c757d;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 6px 0;
  line-height: 1;
}

.card-trend {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 12px;
  display: inline-block;
}

.card-trend.positive {
  color: #28a745;
  background-color: rgba(40, 167, 69, 0.1);
}

.card-trend.negative {
  color: #dc3545;
  background-color: rgba(220, 53, 69, 0.1);
}

.card-trend.neutral {
  color: #6c757d;
  background-color: rgba(108, 117, 125, 0.1);
}

/* 图表容器 */
.charts-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 30px;
}

.chart-row {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  align-items: stretch;
}

.chart-row:nth-child(2) {
  grid-template-columns: 1fr 1fr;
}

.chart-row:nth-child(3) {
  grid-template-columns: 1fr;
}

.chart-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  padding: 24px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.chart-wrapper:hover {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.chart-wrapper.large {
  min-height: 400px;
}

.chart-wrapper.medium {
  min-height: 350px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f8f9fa;
}

.chart-header h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.chart-header h3 i {
  color: #4e73df;
  font-size: 18px;
}

.chart-controls select {
  padding: 6px 12px;
  border: 1px solid #e3e6f0;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #5a5c69;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.chart-controls select:focus {
  outline: none;
  border-color: #4e73df;
  box-shadow: 0 0 0 2px rgba(78, 115, 223, 0.1);
}

.chart-controls .period-btn {
  padding: 6px 16px;
  margin: 0 4px;
  border: 1px solid #e3e6f0;
  border-radius: 20px;
  background: white;
  color: #5a5c69;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chart-controls .period-btn:hover {
  border-color: #4e73df;
  color: #4e73df;
}

.chart-controls .period-btn.active {
  background: #4e73df;
  border-color: #4e73df;
  color: white;
}

.chart-body {
  height: 280px;
  position: relative;
}

.chart-insights {
  display: flex;
  justify-content: space-around;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.insight-item {
  text-align: center;
}

.insight-label {
  display: block;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 4px;
}

.insight-value {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.insight-value.positive {
  color: #28a745;
}

.chart-legend {
  margin-top: 16px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #5a5c69;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  margin-right: 8px;
}

.legend-color.high-focus {
  background-color: #4CAF50;
}

.legend-color.normal-focus {
  background-color: #2196F3;
}

.legend-color.low-focus {
  background-color: #FF9800;
}

.legend-color.distracted {
  background-color: #F44336;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.chart-header h3 {
  font-size: 16px;
  margin: 0;
}

.chart-controls select {
  padding: 5px 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.chart-body {
  height: 300px;
  position: relative;
}

/* 最近训练记录 */
.recent-training-records {
  background: white;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h3 {
  font-size: 16px;
  margin: 0;
}

.btn-secondary {
  background: #f8f9fc;
  border: 1px solid #ddd;
  padding: 6px 12px;
  border-radius: 4px;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.3s;
}

.btn-secondary:hover {
  background: #eaecf4;
  color: #4e73df;
}

.records-table-container {
  overflow-x: auto;
}

.records-table {
  width: 100%;
  border-collapse: collapse;
}

.records-table th,
.records-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #eee;
}

.records-table th {
  font-weight: 600;
  color: #5a5c69;
  background-color: #f8f9fc;
}

.records-table tbody tr:hover {
  background-color: #f8f9ff;
}

.view-details {
  color: #4e73df;
  text-decoration: none;
}

.view-details:hover {
  text-decoration: underline;
}

/* 页脚样式 */
footer {
  text-align: center;
  padding: 15px;
  color: #777;
  background-color: #fff;
  border-top: 1px solid #eee;
  font-size: 14px;
}

footer a {
  color: var(--primary-color);
  text-decoration: none;
}

footer a:hover {
  text-decoration: underline;
}

/* 响应式布局 */
@media (max-width: 1024px) {
  nav:hover, nav.expanded {
    width: 100%;
  }

  nav {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: row;
    overflow-x: auto;
    padding: 10px;
  }

  nav:hover {
    width: 100%;
  }

  .nav-item {
    padding: 10px 15px;
    margin: 0 5px;
    flex-direction: row;
  }

  nav:hover .nav-item {
    flex-direction: row;
  }

  .nav-item i {
    margin-bottom: 0;
    margin-right: 8px;
  }

  .nav-item span {
    opacity: 1;
  }

  .data-overview-cards {
    grid-template-columns: repeat(2, 1fr);
  }

  .chart-row {
    grid-template-columns: 1fr !important;
    gap: 16px;
  }

  .chart-wrapper {
    min-height: 300px !important;
  }
}

@media (max-width: 768px) {
  header {
    flex-wrap: wrap;
    padding: 10px 15px;
  }

  .search-bar {
    order: 3;
    width: 100%;
    margin: 10px 0 0;
  }

  .user-actions {
    width: 100%;
    justify-content: space-between;
  }

  .main-area {
    padding: 15px;
  }

  .main-content {
    padding: 15px;
  }

  .data-overview-cards {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .chart-row {
    grid-template-columns: 1fr !important;
    gap: 15px;
  }

  .chart-body {
    height: 220px;
  }

  .card {
    padding: 18px;
  }

  .chart-wrapper {
    padding: 18px;
    min-height: 280px !important;
  }

  .chart-header h3 {
    font-size: 14px;
  }

  .chart-insights {
    flex-direction: column;
    gap: 8px;
  }

  .chart-legend {
    grid-template-columns: 1fr;
  }
}
</style>
