 

import cv2
import numpy as np
import time
from collections import deque, Counter

# 检查依赖
try:
    import mediapipe as mp
    from fer import FER
    print("✅ 所有依赖已安装")
except ImportError as e:
    print(f"❌ 缺少依赖: {e}")
    print("请安装: pip install fer tensorflow")
    exit(1)

class SimpleEmotionDetector:
    def __init__(self):
        """初始化简化版情绪检测器"""
        print("😊 初始化简化版情绪检测器...")
        
        # 初始化MediaPipe
        self.mp_face_mesh = mp.solutions.face_mesh
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles
        
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.7
        )
        
        # 初始化FER情绪检测器
        print("🔄 加载预训练情绪模型...")
        self.emotion_detector = FER(mtcnn=False)  # 不使用MTCNN，我们用MediaPipe
        
        # 情绪映射（修复中文显示问题）
        self.emotion_display = {
            'angry': 'Angry',
            'disgust': 'Disgust',
            'fear': 'Fear',
            'happy': 'Happy',
            'sad': 'Sad',
            'surprise': 'Surprise',
            'neutral': 'Neutral'
        }
        
        # 情绪颜色
        self.emotion_colors = {
            'angry': (0, 0, 255),      # 红色
            'disgust': (0, 128, 0),    # 深绿色
            'fear': (128, 0, 128),     # 紫色
            'happy': (0, 255, 0),      # 绿色
            'sad': (255, 0, 0),        # 蓝色
            'surprise': (0, 255, 255), # 黄色
            'neutral': (128, 128, 128) # 灰色
        }
        
        # 初始化摄像头
        self.cap = cv2.VideoCapture(0)
        if not self.cap.isOpened():
            print("❌ 无法打开摄像头")
            exit()
        
        # 显示选项
        self.show_landmarks = True
        self.show_confidence = True
        self.show_history = True
        
        # 情绪历史记录
        self.emotion_history = deque(maxlen=20)
        self.confidence_threshold = 0.3
        
        # FPS计算
        self.prev_time = 0
        self.fps = 0
        
        print("✅ 简化版情绪检测器初始化成功")
        print("📋 操作说明：")
        print("   - 按 'l' 键：切换468个关键点显示")
        print("   - 按 'c' 键：切换置信度显示")
        print("   - 按 'h' 键：切换历史统计显示")
        print("   - 按 's' 键：保存情绪统计")
        print("   - 按 'r' 键：重置历史记录")
        print("   - 按 'q' 键：退出程序")
    
    def detect_emotion(self, face_img):
        """使用FER检测情绪"""
        try:
            # 确保图像大小合适
            if face_img.shape[0] < 48 or face_img.shape[1] < 48:
                face_img = cv2.resize(face_img, (48, 48))
            
            # 使用FER检测情绪
            emotions = self.emotion_detector.detect_emotions(face_img)
            
            if emotions and len(emotions) > 0:
                emotion_dict = emotions[0]['emotions']
                
                # 找到最高概率的情绪
                dominant_emotion = max(emotion_dict, key=emotion_dict.get)
                confidence = emotion_dict[dominant_emotion]
                
                return dominant_emotion, confidence, emotion_dict
            else:
                return 'neutral', 0.0, {}
                
        except Exception as e:
            print(f"情绪检测错误: {e}")
            return 'neutral', 0.0, {}
    
    def smooth_emotion_prediction(self, emotion, confidence):
        """平滑情绪预测"""
        # 只有高置信度的预测才加入历史
        if confidence > self.confidence_threshold:
            self.emotion_history.append(emotion)
        
        if len(self.emotion_history) == 0:
            return emotion, confidence
        
        # 统计最近的情绪
        emotion_counts = Counter(self.emotion_history)
        most_common_emotion = emotion_counts.most_common(1)[0][0]
        
        # 如果当前预测与历史一致，提高置信度
        if emotion == most_common_emotion:
            confidence = min(1.0, confidence * 1.1)
        
        return most_common_emotion, confidence
    
    def draw_emotion_info(self, frame, emotion, confidence, all_emotions, face_box):
        """绘制情绪信息"""
        x, y, w, h = face_box
        
        # 获取情绪显示名称和颜色
        emotion_name = self.emotion_display.get(emotion, emotion)
        color = self.emotion_colors.get(emotion, (255, 255, 255))

        # 主要情绪标签
        main_label = f"{emotion_name}"
        confidence_label = f"置信度: {confidence:.2f}"
        
        # 绘制背景矩形
        label_size = cv2.getTextSize(main_label, cv2.FONT_HERSHEY_SIMPLEX, 0.8, 2)[0]
        bg_width = max(label_size[0] + 20, 180)
        cv2.rectangle(frame, (x, y-70), (x + bg_width, y-10), (0, 0, 0), -1)
        cv2.rectangle(frame, (x, y-70), (x + bg_width, y-10), color, 2)
        
        # 绘制主要情绪
        cv2.putText(frame, main_label, (x + 10, y-45), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, color, 2)
        
        if self.show_confidence:
            cv2.putText(frame, confidence_label, (x + 10, y-20), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # 绘制所有情绪的概率条
        if self.show_confidence and all_emotions:
            bar_width = 120
            bar_height = 8
            start_y = y + h + 20
            
            # 按概率排序
            sorted_emotions = sorted(all_emotions.items(), key=lambda x: x[1], reverse=True)
            
            for i, (emo, prob) in enumerate(sorted_emotions[:5]):  # 只显示前5个
                if prob < 0.01:  # 忽略很小的概率
                    continue
                    
                bar_y = start_y + i * 12
                bar_length = int(bar_width * prob)
                
                # 获取情绪颜色
                emo_color = self.emotion_colors.get(emo, (100, 100, 100))
                emo_name = self.emotion_display.get(emo, emo)
                
                # 绘制概率条背景
                cv2.rectangle(frame, (x, bar_y), (x + bar_width, bar_y + bar_height), (50, 50, 50), -1)
                
                # 绘制概率条
                cv2.rectangle(frame, (x, bar_y), (x + bar_length, bar_y + bar_height), emo_color, -1)
                
                # 绘制标签
                cv2.putText(frame, f"{emo_name}: {prob:.2f}", (x + bar_width + 5, bar_y + 6),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.35, (255, 255, 255), 1)
    
    def draw_emotion_statistics(self, frame):
        """绘制情绪统计"""
        if not self.show_history or len(self.emotion_history) == 0:
            return
        
        # 统计情绪频率
        emotion_counts = Counter(self.emotion_history)
        total_count = len(self.emotion_history)
        
        # 绘制统计背景
        stats_width = 220
        stats_height = min(len(emotion_counts) * 25 + 40, 200)
        start_x = frame.shape[1] - stats_width - 10
        start_y = 30
        
        cv2.rectangle(frame, (start_x, start_y), 
                     (start_x + stats_width, start_y + stats_height), (0, 0, 0), -1)
        cv2.rectangle(frame, (start_x, start_y), 
                     (start_x + stats_width, start_y + stats_height), (255, 255, 255), 1)
        
        # 标题
        cv2.putText(frame, "情绪统计", (start_x + 10, start_y + 25), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # 绘制各情绪统计
        y_offset = 50
        for emotion, count in emotion_counts.most_common():
            percentage = (count / total_count) * 100
            emotion_name = self.emotion_display.get(emotion, emotion)
            color = self.emotion_colors.get(emotion, (255, 255, 255))

            text = f"{emotion_name}: {percentage:.1f}%"
            cv2.putText(frame, text, (start_x + 10, start_y + y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.45, color, 1)
            y_offset += 25
            
            if y_offset > stats_height - 20:
                break
    
    def save_emotion_statistics(self):
        """保存情绪统计"""
        if len(self.emotion_history) == 0:
            print("⚠️ 没有情绪数据可保存")
            return
        
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        filename = f"emotion_stats_{timestamp}.txt"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("情绪检测统计报告\n")
            f.write("=" * 30 + "\n")
            f.write(f"检测时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总检测次数: {len(self.emotion_history)}\n\n")
            
            emotion_counts = Counter(self.emotion_history)
            f.write("Emotion Distribution:\n")
            for emotion, count in emotion_counts.most_common():
                percentage = (count / len(self.emotion_history)) * 100
                emotion_name = self.emotion_display.get(emotion, emotion)
                f.write(f"{emotion_name}: {count} times ({percentage:.1f}%)\n")
        
        print(f"💾 情绪统计已保存到: {filename}")

    def run(self):
        """主运行循环"""
        print("🚀 开始简化版情绪检测...")
        
        while True:
            ret, frame = self.cap.read()
            if not ret:
                print("❌ 无法读取视频帧")
                break
            
            # 翻转图像
            frame = cv2.flip(frame, 1)
            height, width = frame.shape[:2]
            
            # MediaPipe处理
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(rgb_frame)
            
            if results.multi_face_landmarks:
                for face_landmarks in results.multi_face_landmarks:
                    # 获取人脸边界框
                    x_coords = [int(lm.x * width) for lm in face_landmarks.landmark]
                    y_coords = [int(lm.y * height) for lm in face_landmarks.landmark]
                    
                    x_min, x_max = min(x_coords), max(x_coords)
                    y_min, y_max = min(y_coords), max(y_coords)
                    
                    # 添加边距
                    margin = 20
                    x_min = max(0, x_min - margin)
                    y_min = max(0, y_min - margin)
                    x_max = min(width, x_max + margin)
                    y_max = min(height, y_max + margin)
                    
                    face_box = (x_min, y_min, x_max - x_min, y_max - y_min)
                    
                    # 提取人脸区域
                    face_roi = frame[y_min:y_max, x_min:x_max]
                    
                    if face_roi.size > 0:
                        # 情绪检测
                        emotion, confidence, all_emotions = self.detect_emotion(face_roi)
                        
                        # 平滑预测
                        smooth_emotion, smooth_confidence = self.smooth_emotion_prediction(emotion, confidence)

                        # 获取情绪颜色
                        emotion_color = self.emotion_colors.get(smooth_emotion, (255, 255, 255))
                        
                        # 绘制人脸框
                        cv2.rectangle(frame, (x_min, y_min), (x_max, y_max), emotion_color, 3)
                        
                        # 绘制468个关键点
                        if self.show_landmarks:
                            for landmark in face_landmarks.landmark:
                                x = int(landmark.x * width)
                                y = int(landmark.y * height)
                                cv2.circle(frame, (x, y), 1, (0, 255, 0), -1)
                        
                        # 绘制情绪信息
                        self.draw_emotion_info(frame, smooth_emotion, smooth_confidence, 
                                             all_emotions, face_box)
            
            # 绘制情绪统计
            self.draw_emotion_statistics(frame)
            
            # 计算FPS
            current_time = time.time()
            if self.prev_time != 0:
                fps = 1.0 / (current_time - self.prev_time)
                self.fps = 0.9 * self.fps + 0.1 * fps if self.fps else fps
            self.prev_time = current_time
            
            # 显示FPS和检测数量
            cv2.putText(frame, f'FPS: {self.fps:.1f}', (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
            cv2.putText(frame, f'检测次数: {len(self.emotion_history)}', (10, 60), 
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 255), 2)
            
            # 显示操作提示
            cv2.putText(frame, 'Press: l-Landmarks, c-Confidence, h-History, s-Save, r-Reset, q-Quit', 
                       (10, height - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)
            
            # 显示画面
            cv2.imshow('Simple Emotion Detection - FER + MediaPipe', frame)
            
            # 键盘控制
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('l'):
                self.show_landmarks = not self.show_landmarks
                print(f"🎯 关键点显示: {'ON' if self.show_landmarks else 'OFF'}")
            elif key == ord('c'):
                self.show_confidence = not self.show_confidence
                print(f"📊 置信度显示: {'ON' if self.show_confidence else 'OFF'}")
            elif key == ord('h'):
                self.show_history = not self.show_history
                print(f"📈 历史统计显示: {'ON' if self.show_history else 'OFF'}")
            elif key == ord('s'):
                self.save_emotion_statistics()
            elif key == ord('r'):
                self.emotion_history.clear()
                print("🧹 历史记录已清除")
        
        # 清理资源
        self.cap.release()
        cv2.destroyAllWindows()
        print("👋 简化版情绪检测结束")

# 主程序
if __name__ == "__main__":
    print("😊 简化版情绪识别系统")
    print("🎯 FER预训练模型 + MediaPipe 468点")
    print("🚀 即装即用，无需训练")
    
    detector = SimpleEmotionDetector()
    detector.run()
