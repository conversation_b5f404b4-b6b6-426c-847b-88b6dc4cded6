<template>
  <nav 
    :class="{ expanded: isExpanded }" 
    @mouseenter="handleMouseEnter" 
    @mouseleave="handleMouseLeave"
    class="app-sidebar"
  >
    <router-link
      to="/homepage"
      class="nav-item"
      :class="{ active: currentRoute === 'homepage' }"
    >
      <i class="fas fa-tachometer-alt"></i>
      <span>首页</span>
    </router-link>

    <router-link
      to="/"
      class="nav-item"
      :class="{ active: currentRoute === 'adhd-scale' }"
    >
      <i class="fas fa-clipboard-check"></i>
      <span>量表筛查</span>
    </router-link>

    <router-link
      to="/home"
      class="nav-item"
      :class="{ active: currentRoute === 'home' }"
    >
      <i class="fas fa-smile"></i>
      <span>智能筛查</span>
    </router-link>

    <router-link
      to="/ai-rehabilitation"
      class="nav-item"
      :class="{ active: currentRoute === 'ai-rehabilitation' }"
    >
      <i class="fas fa-robot"></i>
      <span>康复计划</span>
    </router-link>
    
    <router-link 
      to="/online-consultation" 
      class="nav-item" 
      :class="{ active: currentRoute === 'online-consultation' }"
    >
      <i class="fas fa-comments"></i>
      <span>在线咨询</span>
    </router-link>
    
    <router-link
      to="/attention-games"
      class="nav-item"
      :class="{ active: currentRoute === 'attention-games' }"
    >
      <i class="fas fa-gamepad"></i>
      <span>注意力游戏</span>
    </router-link>

    <router-link
      to="/knowledge-dissemination"
      class="nav-item"
      :class="{ active: currentRoute === 'knowledge-dissemination' }"
    >
      <i class="fas fa-book"></i>
      <span>知识课程</span>
    </router-link>

    <router-link
      to="/data-analysis"
      class="nav-item"
      :class="{ active: currentRoute === 'data-analysis' }"
    >
      <i class="fas fa-chart-line"></i>
      <span>数据分析</span>
    </router-link>
  </nav>
</template>

<script>
export default {
  name: 'AppSidebar',
  props: {
    currentRoute: {
      type: String,
      default: 'home'
    },
    expanded: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isExpanded: false
    }
  },
  mounted() {
    // 初始化展开状态
    this.isExpanded = this.expanded || localStorage.getItem('navExpanded') === 'true';
  },
  watch: {
    expanded(newVal) {
      this.isExpanded = newVal;
    }
  },
  methods: {
    handleMouseEnter() {
      this.isExpanded = true;
      this.$emit('expand');
      localStorage.setItem('navExpanded', 'true');
    },
    handleMouseLeave(e) {
      const mouseX = e.clientX;
      if (mouseX > 180) {
        this.isExpanded = false;
        this.$emit('collapse');
        localStorage.removeItem('navExpanded');
      }
    }
  }
}
</script>

<style scoped>
.app-sidebar {
  width: 80px;
  background: linear-gradient(180deg, #434d73 0%, #394366 100%);
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
  transition: width var(--transition-speed, 0.3s);
  overflow-y: auto;
  position: relative;
  z-index: 95;
}

.app-sidebar.expanded {
  width: 180px;
}

.nav-item {
  color: white;
  text-decoration: none;
  margin: 10px 0;
  padding: 12px;
  border-radius: 10px;
  width: 85%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: all var(--transition-speed, 0.3s);
  position: relative;
}

.app-sidebar.expanded .nav-item {
  flex-direction: row;
  justify-content: flex-start;
  gap: 15px;
  padding: 12px 15px;
}

.nav-item i {
  font-size: 1.3rem;
  margin-bottom: 5px;
}

.app-sidebar.expanded .nav-item i {
  margin-bottom: 0;
}

.nav-item span {
  font-size: 0.85rem;
  opacity: 0;
  transition: opacity var(--transition-speed, 0.3s);
  white-space: nowrap;
}

.app-sidebar.expanded .nav-item span {
  opacity: 1;
}

.nav-item:hover, 
.nav-item.active {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.nav-item::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: white;
  opacity: 0;
  transition: opacity var(--transition-speed, 0.3s);
}

.nav-item:hover::after, 
.nav-item.active::after {
  opacity: 1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .app-sidebar:hover, 
  .app-sidebar.expanded {
    width: 100%;
  }
  
  .app-sidebar {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: row;
    overflow-x: auto;
    padding: 10px;
  }
  
  .app-sidebar:hover {
    width: 100%;
  }
  
  .nav-item {
    padding: 10px 15px;
    margin: 0 5px;
    flex-direction: row;
  }
  
  .app-sidebar:hover .nav-item {
    flex-direction: row;
  }
  
  .nav-item i {
    margin-bottom: 0;
    margin-right: 8px;
  }
  
  .nav-item span {
    opacity: 1;
  }
}
</style>
