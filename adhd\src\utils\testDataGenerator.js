/**
 * 测试数据生成器
 * 用于生成模拟的检测数据以测试数据收集和分析功能
 */

import dataCollectionService from '@/services/dataCollectionService.js'

class TestDataGenerator {
  constructor() {
    this.emotions = ['focused', 'distracted', 'tired', 'neutral'];
    this.attentionLevels = [0, 1, 2, 3]; // 0=发呆, 1=分心, 2=专注, 3=高度专注
  }

  /**
   * 生成随机检测结果
   */
  generateRandomDetection() {
    const emotion = this.emotions[Math.floor(Math.random() * this.emotions.length)];
    const attentionLevel = this.attentionLevels[Math.floor(Math.random() * this.attentionLevels.length)];
    
    // 根据注意力等级生成相应的分数
    let baseScore;
    switch (attentionLevel) {
      case 0: baseScore = 20; break;  // 发呆
      case 1: baseScore = 45; break;  // 分心
      case 2: baseScore = 75; break;  // 专注
      case 3: baseScore = 90; break;  // 高度专注
      default: baseScore = 60;
    }
    
    // 添加随机变化
    const variation = (Math.random() - 0.5) * 20;
    const attentionScore = Math.max(0, Math.min(100, baseScore + variation));
    
    return {
      emotion: emotion,
      attention_level: attentionLevel,
      attention_score: Math.round(attentionScore),
      eye_score: Math.round(attentionScore + (Math.random() - 0.5) * 10),
      face_score: Math.round(attentionScore + (Math.random() - 0.5) * 15),
      motion_score: Math.round(attentionScore + (Math.random() - 0.5) * 12),
      timestamp: new Date().toISOString(),
      confidence: 0.8 + Math.random() * 0.2
    };
  }

  /**
   * 生成一个完整的测试会话
   */
  generateTestSession(duration = 5, detectionsPerMinute = 4) {
    console.log(`🧪 开始生成测试会话 (${duration}分钟, ${detectionsPerMinute}次/分钟)`);
    
    // 开始会话
    const session = dataCollectionService.startSession();
    
    const totalDetections = duration * detectionsPerMinute;
    const detectionInterval = (60 * 1000) / detectionsPerMinute; // 毫秒
    
    let detectionCount = 0;
    
    const generateDetection = () => {
      if (detectionCount >= totalDetections) {
        // 结束会话
        const completedSession = dataCollectionService.endSession();
        console.log(`✅ 测试会话完成:`, {
          id: completedSession.id,
          duration: completedSession.duration,
          detections: completedSession.statistics.totalDetections,
          avgAttention: completedSession.statistics.avgAttentionScore
        });
        return;
      }
      
      // 生成并记录检测数据
      const detection = this.generateRandomDetection();
      dataCollectionService.recordDetection(detection);
      
      detectionCount++;
      console.log(`📊 记录检测 ${detectionCount}/${totalDetections}:`, {
        emotion: detection.emotion,
        attention: detection.attention_score,
        level: detection.attention_level
      });
      
      // 继续下一次检测
      setTimeout(generateDetection, detectionInterval);
    };
    
    // 开始生成检测数据
    setTimeout(generateDetection, 1000);
    
    return session;
  }

  /**
   * 生成多个历史会话
   */
  generateHistoricalSessions(sessionCount = 5) {
    console.log(`🧪 开始生成 ${sessionCount} 个历史会话`);
    
    const sessions = [];
    
    for (let i = 0; i < sessionCount; i++) {
      // 生成过去几天的时间戳
      const daysAgo = Math.floor(Math.random() * 7) + 1;
      const sessionDate = new Date();
      sessionDate.setDate(sessionDate.getDate() - daysAgo);
      sessionDate.setHours(9 + Math.floor(Math.random() * 8)); // 9-17点
      sessionDate.setMinutes(Math.floor(Math.random() * 60));
      
      // 创建历史会话
      const session = this.createHistoricalSession(sessionDate, 3 + Math.random() * 7); // 3-10分钟
      sessions.push(session);
    }
    
    console.log(`✅ 生成了 ${sessions.length} 个历史会话`);
    return sessions;
  }

  /**
   * 创建历史会话数据
   */
  createHistoricalSession(startTime, duration) {
    const sessionId = 'test_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    const detectionsCount = Math.floor(duration * 4); // 每分钟4次检测
    
    const detections = [];
    const emotions = {};
    const attentionLevels = { 0: 0, 1: 0, 2: 0, 3: 0 };
    let totalAttention = 0;
    
    for (let i = 0; i < detectionsCount; i++) {
      const detection = this.generateRandomDetection();
      detection.timestamp = new Date(startTime.getTime() + (i * (duration * 60 * 1000) / detectionsCount)).toISOString();
      
      detections.push(detection);
      emotions[detection.emotion] = (emotions[detection.emotion] || 0) + 1;
      attentionLevels[detection.attention_level]++;
      totalAttention += detection.attention_score;
    }
    
    const endTime = new Date(startTime.getTime() + duration * 60 * 1000);
    
    const sessionData = {
      id: sessionId,
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      duration: Math.round(duration),
      detections: detections,
      statistics: {
        totalDetections: detectionsCount,
        avgAttentionScore: Math.round(totalAttention / detectionsCount),
        avgEyeScore: Math.round(totalAttention / detectionsCount + (Math.random() - 0.5) * 10),
        avgFaceScore: Math.round(totalAttention / detectionsCount + (Math.random() - 0.5) * 15),
        avgMotionScore: Math.round(totalAttention / detectionsCount + (Math.random() - 0.5) * 12),
        emotions: emotions,
        attentionLevels: attentionLevels
      }
    };
    
    // 直接添加到数据存储中
    dataCollectionService.addHistoricalSession(sessionData);
    
    return sessionData;
  }

  /**
   * 清除所有测试数据
   */
  clearTestData() {
    console.log('🧹 清除所有测试数据');
    dataCollectionService.clearAllData();
  }

  /**
   * 快速生成完整的测试数据集
   */
  generateCompleteTestDataset() {
    console.log('🚀 开始生成完整的测试数据集');
    
    // 清除现有数据
    this.clearTestData();
    
    // 生成历史会话
    this.generateHistoricalSessions(8);
    
    console.log('✅ 完整测试数据集生成完成');
    
    // 返回数据概览
    const allData = dataCollectionService.getAllData();
    return {
      sessions: allData.sessions.length,
      totalDetections: allData.statistics.totalDetections,
      totalDuration: allData.statistics.totalDuration
    };
  }
}

// 创建单例实例
const testDataGenerator = new TestDataGenerator();

export default testDataGenerator;
