/**
 * 量表相关API服务 - 专门处理医疗量表提交和分析
 */

import { request, formatters } from './axios.js'

// 提交量表数据API
export async function submitScaleData(scaleData) {
  console.log('📊 提交量表数据到RuoYi后端...')
  console.log('📤 原始量表数据:', scaleData)

  // 使用formatters确保数据类型和格式正确，匹配ScaleTestRecords实体类
  const formattedData = {
    // 基本用户信息（映射到@TableField(exist = false)字段）
    userId: formatters.ensureString(scaleData.userId),
    username: formatters.ensureString(scaleData.username),
    age: formatters.ensureNumber(scaleData.age, null),
    gender: scaleData.gender === '男' ? '0' : scaleData.gender === '女' ? '1' : '2', // 🔥 格式化性别字段为数字
    scaleName: formatters.ensureString(scaleData.scaleName),
    scaleType: formatters.ensureString(scaleData.scaleType),

    // 🔥 分数信息（映射到数据库实际字段）
    totalScore: formatters.ensureNumber(scaleData.totalScore, 0), // 量表总分
    maxTotalScore: formatters.ensureNumber(scaleData.maxTotalScore, 0), // 量表满分
    
    // 时间字段
    startTime: formatters.formatDateForISO(scaleData.startTime),
    submitTime: formatters.formatDateForISO(new Date()), // 使用formatters确保格式正确
    
    // 🔥 量表详细数据（JSON格式存储到scaleData字段）
    scaleData: formatters.ensureString(scaleData.scaleData),

    // 🔥 AI分析相关字段
    analysisMethod: formatters.ensureString(scaleData.analysisMethod || 'traditional'), // AI分析 或 传统分析
    aiConfidence: formatters.ensureNumber(scaleData.aiConfidence, null), // AI分析置信度
    severityLevel: formatters.ensureString(scaleData.severityLevel), // 严重程度

    // 时间和风险信息
    endTime: formatters.formatDateForISO(scaleData.endTime),
    duration: formatters.ensureNumber(scaleData.duration, 0),
    riskLevel: formatters.formatRiskLevel(scaleData.riskLevel), // 🔥 格式化风险等级字段

    // 🔥 详细分析结果（JSON格式存储）
    analysisDetails: formatters.ensureString(scaleData.analysisDetails),

    // 🔥 专业医疗建议（医生端使用）
    professionalAdvice: formatters.ensureString(scaleData.professionalAdvice),

    // 🔥 使用的模型信息
    modelUsed: formatters.ensureString(scaleData.modelUsed),
    
    // 状态字段
    status: 'completed'

    // createdAt由后端自动生成
  }

  console.log('📤 格式化后的量表数据:', formattedData)

  try {
    const result = await request.post('/system/scaleRecords/submit', formattedData)
    console.log('📥 量表提交响应结果:', result)
    return result
  } catch (error) {
    console.error('❌ 量表提交异常:', error)
    return {
      success: false,
      message: error.message || '量表提交失败',
      error
    }
  }
}

// 获取AI分析结果
export async function getAIAnalysis(recordId) {
  console.log('🤖 获取AI分析结果...')
  console.log('📋 记录ID:', recordId)

  try {
    const response = await request.get(`/system/scaleRecords/${recordId}`)
    console.log('📥 AI分析响应:', response)

    if (response.code === 200 && response.data) {
      return {
        success: true,
        data: {
          professionalAdvice: response.data.professionalAdvice,
          aiConfidence: response.data.aiConfidence,
          analysisMethod: response.data.analysisMethod,
          modelUsed: response.data.modelUsed,
          analysisDetails: response.data.analysisDetails,
          severityLevel: response.data.severityLevel
        }
      }
    } else {
      return {
        success: false,
        message: response.msg || '获取AI分析失败'
      }
    }
  } catch (error) {
    console.error('❌ 获取AI分析失败:', error)
    return {
      success: false,
      message: error.message || '获取AI分析失败',
      error: error
    }
  }
}

// 轮询检查AI分析状态
export async function pollAIAnalysisStatus(recordId, maxAttempts = 15, interval = 3000) {
  console.log('🔄 开始轮询AI分析状态...')
  console.log(`📋 记录ID: ${recordId}, 最大尝试次数: ${maxAttempts}, 间隔: ${interval}ms`)

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    console.log(`🔍 第${attempt}次检查AI分析状态...`)

    const result = await getAIAnalysis(recordId)

    if (result.success && result.data.professionalAdvice) {
      console.log('✅ AI分析完成!')
      return result
    }

    if (attempt < maxAttempts) {
      console.log(`⏳ AI分析进行中，${interval/1000}秒后重试...`)
      await new Promise(resolve => setTimeout(resolve, interval))
    }
  }

  console.log('⏰ AI分析超时')
  return {
    success: false,
    message: 'AI分析超时，请稍后在个人中心查看结果'
  }
}

// 优化的AI分析提交方法
export async function submitScaleWithAIAnalysis(scaleData) {
  console.log('🚀 提交量表并请求AI分析...')

  try {
    // 1. 先提交量表数据
    const submitResult = await submitScaleData(scaleData)

    if (!submitResult.success) {
      throw new Error(submitResult.message || '量表提交失败')
    }

    console.log('✅ 量表提交成功，记录ID:', submitResult.data?.id)

    // 2. 如果有记录ID，开始轮询AI分析结果
    if (submitResult.data?.id) {
      console.log('🤖 开始等待AI分析结果...')

      // 异步轮询，不阻塞用户界面
      const aiResult = await pollAIAnalysisStatus(submitResult.data.id)

      return {
        success: true,
        message: '量表提交成功',
        data: {
          ...submitResult.data,
          aiAnalysis: aiResult.success ? aiResult.data : null
        },
        aiAnalysisStatus: aiResult.success ? 'completed' : 'pending'
      }
    } else {
      return {
        success: true,
        message: '量表提交成功，AI分析将在后台进行',
        data: submitResult.data,
        aiAnalysisStatus: 'pending'
      }
    }
  } catch (error) {
    console.error('❌ 提交量表或AI分析失败:', error)
    return {
      success: false,
      message: error.message || '提交失败',
      error: error
    }
  }
}

// 获取用户量表记录列表
export async function getUserScaleRecords(userId, pageNum = 1, pageSize = 10) {
  console.log('📋 获取用户量表记录列表...')
  console.log('👤 用户ID:', userId)

  try {
    const result = await request.get('/system/scaleRecords/list', {
      userId: userId,
      pageNum: pageNum,
      pageSize: pageSize
    })
    
    console.log('📥 量表记录查询结果:', result)
    return result
  } catch (error) {
    console.error('❌ 获取量表记录异常:', error)
    return {
      success: false,
      message: error.message || '获取量表记录失败',
      error
    }
  }
}

// 获取单个量表记录详情
export async function getScaleRecordDetail(recordId) {
  console.log('📋 获取量表记录详情...')
  console.log('📄 记录ID:', recordId)

  try {
    const result = await request.get(`/system/scaleRecords/${recordId}`)
    console.log('📥 量表记录详情:', result)
    return result
  } catch (error) {
    console.error('❌ 获取量表记录详情异常:', error)
    return {
      success: false,
      message: error.message || '获取量表记录详情失败',
      error
    }
  }
}

// 删除量表记录
export async function deleteScaleRecord(recordId) {
  console.log('🗑️ 删除量表记录...')
  console.log('📄 记录ID:', recordId)

  try {
    const result = await request.delete(`/system/scaleRecords/${recordId}`)
    console.log('📥 删除结果:', result)
    return result
  } catch (error) {
    console.error('❌ 删除量表记录异常:', error)
    return {
      success: false,
      message: error.message || '删除量表记录失败',
      error
    }
  }
}

// 获取量表统计信息
export async function getScaleStatistics(userId, dateRange = null) {
  console.log('📊 获取量表统计信息...')
  console.log('👤 用户ID:', userId)

  try {
    const params = { userId }
    if (dateRange) {
      params.startDate = dateRange.startDate
      params.endDate = dateRange.endDate
    }

    const result = await request.get('/system/scaleRecords/statistics', params)
    console.log('📥 统计信息结果:', result)
    return result
  } catch (error) {
    console.error('❌ 获取统计信息异常:', error)
    return {
      success: false,
      message: error.message || '获取统计信息失败',
      error
    }
  }
}
