/**
 * Express.js后端服务器
 * 用于管理Python WebRTC服务器
 */

const express = require('express');
const cors = require('cors');
const PythonServerManager = require('./pythonServerManager');

const app = express();
const port = 3001;

// 创建Python服务器管理器实例
const pythonManager = new PythonServerManager();

// 中间件
app.use(cors());
app.use(express.json());

// 日志中间件
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

/**
 * 自动启动端点 - 用于前端一键启动
 */
app.post('/api/auto-start', async (req, res) => {
  try {
    console.log('🚀 收到前端一键启动请求');

    // 自动启动Python服务器
    const result = await pythonManager.startServer();

    res.json({
      success: true,
      message: '一键启动成功！Python服务器已启动',
      backend_status: 'running',
      python_status: result.success ? 'running' : 'failed',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('一键启动失败:', error);
    res.status(500).json({
      success: false,
      message: '一键启动失败: ' + error.message,
      backend_status: 'running',
      python_status: 'failed',
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * 启动Python服务器
 */
app.post('/api/python-server/start', async (req, res) => {
  try {
    console.log('📡 收到启动Python服务器请求');
    const result = await pythonManager.startServer();
    
    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        status: pythonManager.getStatus()
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message,
        status: pythonManager.getStatus()
      });
    }
  } catch (error) {
    console.error('启动Python服务器API错误:', error);
    res.status(500).json({
      success: false,
      message: error.message,
      status: pythonManager.getStatus()
    });
  }
});

/**
 * 停止Python服务器
 */
app.post('/api/python-server/stop', async (req, res) => {
  try {
    console.log('📡 收到停止Python服务器请求');
    const result = await pythonManager.stopServer();
    
    res.json({
      success: result.success,
      message: result.message,
      status: pythonManager.getStatus()
    });
  } catch (error) {
    console.error('停止Python服务器API错误:', error);
    res.status(500).json({
      success: false,
      message: error.message,
      status: pythonManager.getStatus()
    });
  }
});

/**
 * 重启Python服务器
 */
app.post('/api/python-server/restart', async (req, res) => {
  try {
    console.log('📡 收到重启Python服务器请求');
    const result = await pythonManager.restartServer();
    
    if (result.success) {
      res.json({
        success: true,
        message: result.message,
        status: pythonManager.getStatus()
      });
    } else {
      res.status(500).json({
        success: false,
        message: result.message,
        status: pythonManager.getStatus()
      });
    }
  } catch (error) {
    console.error('重启Python服务器API错误:', error);
    res.status(500).json({
      success: false,
      message: error.message,
      status: pythonManager.getStatus()
    });
  }
});

/**
 * 获取Python服务器状态
 */
app.get('/api/python-server/status', async (req, res) => {
  try {
    const status = pythonManager.getStatus();
    const isRunning = await pythonManager.isServerRunning();
    
    res.json({
      success: true,
      status: {
        ...status,
        isHealthy: isRunning
      }
    });
  } catch (error) {
    console.error('获取Python服务器状态错误:', error);
    res.status(500).json({
      success: false,
      message: error.message,
      status: pythonManager.getStatus()
    });
  }
});

/**
 * 健康检查
 */
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Backend API服务器运行正常',
    timestamp: new Date().toISOString(),
    pythonServerStatus: pythonManager.getStatus()
  });
});

// ============================
// 量表相关API路由
// ============================

/**
 * 提交量表数据 - 模拟RuoYi后端API
 */
app.post('/system/scaleRecords/submit', (req, res) => {
  try {
    console.log('📊 收到量表数据提交请求');
    console.log('📤 提交的数据:', req.body);

    // 模拟数据验证
    const { scaleName, totalScore, userId, username } = req.body;
    
    if (!scaleName || typeof totalScore !== 'number' || !userId) {
      return res.status(400).json({
        code: 400,
        msg: '必填字段缺失：scaleName, totalScore, userId',
        data: null
      });
    }

    // 模拟数据库保存
    const mockScaleRecord = {
      id: Date.now(), // 模拟自增ID
      ...req.body,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      status: 'completed'
    };

    console.log('✅ 量表数据保存成功，记录ID:', mockScaleRecord.id);

    // 返回RuoYi标准格式响应
    res.json({
      code: 200,
      msg: '量表数据提交成功',
      data: mockScaleRecord
    });

  } catch (error) {
    console.error('❌ 量表数据提交失败:', error);
    res.status(500).json({
      code: 500,
      msg: '服务器内部错误: ' + error.message,
      data: null
    });
  }
});

/**
 * 获取用户的量表记录
 */
app.get('/system/scaleRecords/user/:userId', (req, res) => {
  try {
    const { userId } = req.params;
    console.log(`📊 获取用户 ${userId} 的量表记录`);

    // 模拟返回用户的量表记录
    const mockRecords = [
      {
        id: 1,
        userId: userId,
        scaleName: 'ADHD-RS-IV量表',
        totalScore: 28,
        riskLevel: 'high',
        createdAt: new Date(Date.now() - 86400000).toISOString()
      },
      {
        id: 2,
        userId: userId,
        scaleName: 'CDI-2 儿童抑郁量表',
        totalScore: 15,
        riskLevel: 'medium',
        createdAt: new Date().toISOString()
      }
    ];

    res.json({
      code: 200,
      msg: '获取量表记录成功',
      data: mockRecords
    });

  } catch (error) {
    console.error('❌ 获取量表记录失败:', error);
    res.status(500).json({
      code: 500,
      msg: '服务器内部错误: ' + error.message,
      data: null
    });
  }
});

/**
 * 健康检查
 */
app.get('/actuator/health', (req, res) => {
  res.json({
    status: 'UP',
    components: {
      diskSpace: { status: 'UP' },
      ping: { status: 'UP' }
    }
  });
});

/**
 * 代理Python服务器的健康检查
 */
app.get('/api/python-server/health', async (req, res) => {
  try {
    const isRunning = await pythonManager.isServerRunning();
    
    if (isRunning) {
      res.json({
        success: true,
        message: 'Python服务器运行正常',
        status: 'healthy'
      });
    } else {
      res.status(503).json({
        success: false,
        message: 'Python服务器未运行',
        status: 'unhealthy'
      });
    }
  } catch (error) {
    res.status(500).json({
      success: false,
      message: error.message,
      status: 'error'
    });
  }
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('API错误:', error);
  res.status(500).json({
    success: false,
    message: '服务器内部错误',
    error: error.message
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: '接口不存在',
    path: req.path
  });
});

// 启动服务器
app.listen(port, () => {
  console.log('🚀 Backend API服务器启动成功');
  console.log(`📡 服务器地址: http://localhost:${port}`);
  console.log('📋 可用接口:');
  console.log('  POST /api/python-server/start   - 启动Python服务器');
  console.log('  POST /api/python-server/stop    - 停止Python服务器');
  console.log('  POST /api/python-server/restart - 重启Python服务器');
  console.log('  GET  /api/python-server/status  - 获取Python服务器状态');
  console.log('  GET  /api/python-server/health  - Python服务器健康检查');
  console.log('  GET  /api/health                - Backend API健康检查');
  console.log('');
  console.log('🐍 Python服务器管理器已就绪');
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n🛑 收到关闭信号，正在优雅关闭...');
  
  try {
    await pythonManager.stopServer();
    console.log('✅ Python服务器已停止');
  } catch (error) {
    console.error('停止Python服务器失败:', error);
  }
  
  process.exit(0);
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 收到终止信号，正在优雅关闭...');
  
  try {
    await pythonManager.stopServer();
    console.log('✅ Python服务器已停止');
  } catch (error) {
    console.error('停止Python服务器失败:', error);
  }
  
  process.exit(0);
});

module.exports = app;
