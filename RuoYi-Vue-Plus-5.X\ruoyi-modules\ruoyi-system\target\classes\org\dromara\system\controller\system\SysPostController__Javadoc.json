{"doc": " 岗位信息操作处理\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取岗位列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出岗位列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 根据岗位编号获取详细信息\n\n @param postId 岗位ID\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": " 新增岗位\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysPostBo"], "doc": " 修改岗位\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除岗位\n\n @param postIds 岗位ID串\n"}, {"name": "optionselect", "paramTypes": ["java.lang.Long[]", "java.lang.Long"], "doc": " 获取岗位选择框列表\n\n @param postIds 岗位ID串\n @param deptId  部门id\n"}], "constructors": []}