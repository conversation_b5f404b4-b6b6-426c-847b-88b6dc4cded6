<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>听词复述游戏</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            color: #495057;
            overflow: hidden;
            position: relative;
        }

        /* 🔥 添加医院风格的背景装饰 */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 123, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(40, 167, 69, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(255, 193, 7, 0.05) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        .sound-waves {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
        }

        .wave {
            position: absolute;
            border-radius: 50%;
            border: 2px solid rgba(255, 255, 255, 0.1);
            animation: waveExpand 3s infinite ease-out;
        }

        @keyframes waveExpand {
            0% { transform: scale(0); opacity: 1; }
            100% { transform: scale(4); opacity: 0; }
        }

        .game-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 25px;
            padding: 40px;
            text-align: center;
            box-shadow:
                0 20px 40px rgba(0, 0, 0, 0.1),
                0 0 0 1px rgba(255, 255, 255, 0.5),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            max-width: 900px;
            width: 95%;
            border: 2px solid rgba(0, 123, 255, 0.1);
            position: relative;
            z-index: 2;
            animation: containerFadeIn 1s ease-out, containerFloat 6s ease-in-out infinite;
        }

        @keyframes containerFloat {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            25% { transform: translateY(-5px) rotate(0.5deg); }
            50% { transform: translateY(0px) rotate(0deg); }
            75% { transform: translateY(-3px) rotate(-0.5deg); }
        }

        @keyframes containerFadeIn {
            from { opacity: 0; transform: scale(0.9); }
            to { opacity: 1; transform: scale(1); }
        }

        .game-title {
            font-size: 3rem;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #007bff, #0056b3, #28a745);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: titlePulse 3s ease-in-out infinite alternate, titleSway 4s ease-in-out infinite;
            position: relative;
            display: inline-block;
        }

        .game-title::before {
            content: '🎵';
            position: absolute;
            left: -60px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2rem;
            animation: iconBounce 2s ease-in-out infinite;
        }

        .game-title::after {
            content: '🎧';
            position: absolute;
            right: -60px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 2rem;
            animation: iconBounce 2s ease-in-out infinite 0.5s;
        }

        @keyframes titlePulse {
            from { filter: drop-shadow(0 0 10px rgba(0, 123, 255, 0.3)); }
            to { filter: drop-shadow(0 0 20px rgba(0, 123, 255, 0.6)); }
        }

        @keyframes titleSway {
            0%, 100% { transform: rotate(0deg); }
            25% { transform: rotate(1deg); }
            75% { transform: rotate(-1deg); }
        }

        @keyframes iconBounce {
            0%, 100% { transform: translateY(-50%) scale(1); }
            50% { transform: translateY(-60%) scale(1.1); }
        }

        .game-description {
            font-size: 1.3rem;
            margin-bottom: 30px;
            color: rgba(255, 255, 255, 0.9);
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        }

        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 25px 0;
        }

        .stat-item {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
            padding: 20px;
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: bold;
            color: #4ecdc4;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
        }

        .stat-label {
            font-size: 0.9rem;
            margin-top: 5px;
            opacity: 0.8;
        }

        .round-progress {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 20px;
            margin: 25px 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .progress-text {
            font-size: 1.2rem;
            margin-bottom: 15px;
            font-weight: 600;
        }

        .progress-bar {
            width: 100%;
            height: 12px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4ecdc4, #44a08d);
            border-radius: 6px;
            transition: width 0.8s ease;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .audio-display {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.15));
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 20px;
            padding: 50px;
            margin: 30px 0;
            min-height: 180px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .audio-icon {
            font-size: 4rem;
            margin-bottom: 20px;
            color: #4ecdc4;
            animation: audioFloat 2s ease-in-out infinite;
        }

        @keyframes audioFloat {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .audio-text {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.9);
            text-align: center;
        }

        .play-button {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            color: white;
            border: none;
            padding: 20px 40px;
            border-radius: 50px;
            font-size: 1.5rem;
            cursor: pointer;
            margin: 20px 0;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(78, 205, 196, 0.3);
            position: relative;
            overflow: hidden;
        }

        .play-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .play-button:hover::before {
            left: 100%;
        }

        .play-button:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 25px rgba(78, 205, 196, 0.4);
        }

        .play-button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            background: #6b7280;
        }

        .play-count {
            font-size: 1rem;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 10px;
        }

        .input-container {
            margin: 30px 0;
            position: relative;
        }

        .word-input {
            background: rgba(255, 255, 255, 0.9);
            border: 3px solid rgba(255, 255, 255, 0.5);
            border-radius: 15px;
            padding: 20px 25px;
            font-size: 1.8rem;
            color: #333;
            text-align: center;
            width: 400px;
            font-weight: bold;
            transition: all 0.3s ease;
        }

        .word-input:focus {
            outline: none;
            border-color: #4ecdc4;
            box-shadow: 0 0 30px rgba(78, 205, 196, 0.5);
            transform: scale(1.05);
        }

        .word-input::placeholder {
            color: #666;
            font-weight: normal;
        }

        .btn {
            background: linear-gradient(45deg, #4ade80, #22c55e);
            color: white;
            border: none;
            padding: 18px 35px;
            border-radius: 30px;
            font-size: 1.2rem;
            font-weight: bold;
            cursor: pointer;
            margin: 0 15px;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(34, 197, 94, 0.3);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 25px rgba(34, 197, 94, 0.4);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
            background: #6b7280;
        }

        .feedback {
            margin: 25px 0;
            font-size: 1.5rem;
            font-weight: bold;
            min-height: 40px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .correct {
            color: #4ade80;
            animation: bounce 0.6s ease-in-out;
        }

        .incorrect {
            color: #ff6b6b;
            animation: shake 0.6s ease-in-out;
        }

        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            80% { transform: translateY(-5px); }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }

        .level-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4ecdc4, #44a08d);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-weight: bold;
            font-size: 1.1rem;
            box-shadow: 0 5px 15px rgba(78, 205, 196, 0.3);
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        @media (max-width: 768px) {
            .game-container {
                padding: 25px;
                margin: 10px;
            }
            
            .game-title {
                font-size: 2.2rem;
            }
            
            .word-input {
                width: 90%;
                font-size: 1.5rem;
            }
            
            .stats-container {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="sound-waves" id="soundWaves"></div>
    
    <div class="level-badge">
        关卡 <span id="levelDisplay">1</span>
    </div>

    <div class="game-container">
        <h1 class="game-title">🎧 听词复述游戏</h1>
        <p class="game-description">仔细听取词语然后准确复述，十轮挑战提升听觉注意力！</p>

        <div class="stats-container">
            <div class="stat-item">
                <div class="stat-value" id="score">0</div>
                <div class="stat-label">得分</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="streak">0</div>
                <div class="stat-label">连击</div>
            </div>
            <div class="stat-item">
                <div class="stat-value" id="accuracy">0%</div>
                <div class="stat-label">正确率</div>
            </div>
        </div>

        <div class="round-progress">
            <div class="progress-text" id="progressText">准备开始 - 第1轮/共10轮</div>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
        </div>

        <div class="audio-display">
            <div class="audio-icon" id="audioIcon">🔊</div>
            <div class="audio-text" id="audioText">点击开始游戏</div>
            <button class="play-button" id="playButton" onclick="playWord()" disabled>
                🎵 播放词语
            </button>
            <div class="play-count" id="playCount"></div>
        </div>

        <div class="input-container">
            <input type="text" class="word-input" id="wordInput" 
                   placeholder="请输入听到的词语..." disabled>
        </div>

        <div class="feedback" id="feedback"></div>

        <div class="game-controls">
            <button class="btn" id="startBtn" onclick="startGame()">🚀 开始游戏</button>
            <button class="btn" id="submitBtn" onclick="checkAnswer()" disabled>✅ 提交答案</button>
            <button class="btn" id="nextBtn" onclick="nextRound()" disabled style="display: none;">➡️ 下一题</button>
        </div>
    </div>

    <script>
        // 创建声波动画
        function createSoundWaves() {
            const container = document.getElementById('soundWaves');
            for (let i = 0; i < 10; i++) {
                const wave = document.createElement('div');
                wave.className = 'wave';
                wave.style.left = Math.random() * 100 + '%';
                wave.style.top = Math.random() * 100 + '%';
                wave.style.width = wave.style.height = Math.random() * 100 + 50 + 'px';
                wave.style.animationDelay = Math.random() * 3 + 's';
                wave.style.animationDuration = (Math.random() * 2 + 2) + 's';
                container.appendChild(wave);
            }
        }

        // 游戏状态
        let gameState = {
            score: 0,
            streak: 0,
            level: 1,
            gameActive: false,
            currentRound: 1,
            totalRounds: 10,
            currentWord: '',
            currentCategory: '',
            totalQuestions: 0,
            correctAnswers: 0,
            playCount: 0,
            maxPlays: 3
        };

        // 词语数据库
        const wordCategories = {
            '动物': ['小猫', '小狗', '小鸟', '小鱼', '兔子', '老虎', '狮子', '大象', '熊猫', '猴子'],
            '水果': ['苹果', '香蕉', '橙子', '葡萄', '草莓', '西瓜', '桃子', '梨子', '樱桃', '柠檬'],
            '颜色': ['红色', '蓝色', '绿色', '黄色', '紫色', '橙色', '粉色', '黑色', '白色', '灰色'],
            '交通': ['汽车', '飞机', '火车', '轮船', '自行车', '摩托车', '公交车', '地铁', '出租车', '卡车'],
            '食物': ['米饭', '面条', '包子', '饺子', '蛋糕', '面包', '牛奶', '鸡蛋', '蔬菜', '肉类']
        };

        // 生成当前轮次的词语
        function generateWord() {
            const categories = Object.keys(wordCategories);
            const category = categories[Math.floor(Math.random() * categories.length)];
            const words = wordCategories[category];
            const word = words[Math.floor(Math.random() * words.length)];

            gameState.currentWord = word;
            gameState.currentCategory = category;
            gameState.playCount = 0;
        }

        // 语音合成播放词语
        function playWord() {
            if (gameState.playCount >= gameState.maxPlays) {
                return;
            }

            gameState.playCount++;

            // 创建语音合成
            const utterance = new SpeechSynthesisUtterance(gameState.currentWord);
            utterance.lang = 'zh-CN';
            utterance.rate = 0.8;
            utterance.pitch = 1.2;
            utterance.volume = 1;

            // 播放动画效果
            const audioIcon = document.getElementById('audioIcon');
            const audioText = document.getElementById('audioText');

            audioIcon.style.animation = 'none';
            audioIcon.offsetHeight; // 触发重排
            audioIcon.style.animation = 'audioFloat 0.5s ease-in-out 3';

            audioText.textContent = '正在播放...';

            utterance.onstart = function() {
                createSoundEffect();
            };

            utterance.onend = function() {
                audioText.textContent = `播放完成 (${gameState.playCount}/${gameState.maxPlays})`;
                updatePlayButton();
            };

            speechSynthesis.speak(utterance);

            updatePlayCount();
        }

        // 创建声音效果
        function createSoundEffect() {
            const container = document.querySelector('.audio-display');
            for (let i = 0; i < 8; i++) {
                const soundRing = document.createElement('div');
                soundRing.style.position = 'absolute';
                soundRing.style.left = '50%';
                soundRing.style.top = '50%';
                soundRing.style.width = soundRing.style.height = '20px';
                soundRing.style.border = '2px solid #4ecdc4';
                soundRing.style.borderRadius = '50%';
                soundRing.style.transform = 'translate(-50%, -50%)';
                soundRing.style.animation = `soundRing ${0.8 + i * 0.1}s ease-out forwards`;
                soundRing.style.pointerEvents = 'none';
                container.appendChild(soundRing);

                setTimeout(() => {
                    soundRing.remove();
                }, 1000);
            }
        }

        // 更新播放按钮状态
        function updatePlayButton() {
            const playButton = document.getElementById('playButton');
            if (gameState.playCount >= gameState.maxPlays) {
                playButton.disabled = true;
                playButton.textContent = '🚫 播放次数已用完';
            } else {
                playButton.textContent = `🎵 播放词语 (${gameState.maxPlays - gameState.playCount}次)`;
            }
        }

        // 更新播放次数显示
        function updatePlayCount() {
            const playCount = document.getElementById('playCount');
            playCount.textContent = `剩余播放次数: ${gameState.maxPlays - gameState.playCount}`;
        }

        // 开始游戏
        function startGame() {
            gameState.gameActive = true;
            gameState.score = 0;
            gameState.streak = 0;
            gameState.level = 1;
            gameState.currentRound = 1;
            gameState.totalQuestions = 0;
            gameState.correctAnswers = 0;

            document.getElementById('startBtn').disabled = true;
            nextRound();
        }

        // 下一轮
        function nextRound() {
            if (gameState.currentRound > gameState.totalRounds) {
                endGame();
                return;
            }

            generateWord();

            document.getElementById('progressText').textContent =
                `第${gameState.currentRound}轮/共${gameState.totalRounds}轮 - ${gameState.currentCategory}类词语`;

            document.getElementById('audioText').textContent = '点击播放按钮听词语';
            document.getElementById('wordInput').value = '';
            document.getElementById('feedback').textContent = '';
            document.getElementById('nextBtn').style.display = 'none';

            document.getElementById('playButton').disabled = false;
            document.getElementById('submitBtn').disabled = false;
            document.getElementById('wordInput').disabled = false;

            updatePlayButton();
            updatePlayCount();
            updateDisplay();
        }

        // 检查答案
        function checkAnswer() {
            const userAnswer = document.getElementById('wordInput').value.trim();
            const feedback = document.getElementById('feedback');

            gameState.totalQuestions++;

            // 使用更宽松的匹配，去除空格和标点符号
            const normalizeText = (text) => text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, '').toLowerCase();
            const normalizedUserAnswer = normalizeText(userAnswer);
            const normalizedCorrectAnswer = normalizeText(gameState.currentWord);

            if (normalizedUserAnswer === normalizedCorrectAnswer) {
                let points = 10 * gameState.currentRound;
                // 首次播放奖励
                if (gameState.playCount === 1) {
                    points = Math.floor(points * 1.5);
                    feedback.textContent = `🎉 太棒了！首次播放奖励 +${points}分`;
                } else {
                    feedback.textContent = `✅ 正确！+${points}分`;
                }

                gameState.score += points;
                gameState.streak++;
                gameState.correctAnswers++;
                feedback.className = 'feedback correct';
                createSuccessParticles();
            } else {
                gameState.streak = 0;
                feedback.textContent = `❌ 错误！正确答案是: ${gameState.currentWord}`;
                feedback.className = 'feedback incorrect';
            }

            gameState.currentRound++;

            document.getElementById('playButton').disabled = true;
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('wordInput').disabled = true;
            document.getElementById('nextBtn').style.display = 'inline-block';
            document.getElementById('nextBtn').disabled = false;

            updateDisplay();
        }

        // 创建成功粒子效果
        function createSuccessParticles() {
            const container = document.querySelector('.game-container');
            for (let i = 0; i < 12; i++) {
                const particle = document.createElement('div');
                particle.style.position = 'absolute';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.width = particle.style.height = Math.random() * 8 + 4 + 'px';
                particle.style.background = '#4ecdc4';
                particle.style.borderRadius = '50%';
                particle.style.pointerEvents = 'none';
                particle.style.animation = 'particleFloat 2s ease-out forwards';
                container.appendChild(particle);

                setTimeout(() => {
                    particle.remove();
                }, 2000);
            }
        }

        // 更新显示
        function updateDisplay() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('streak').textContent = gameState.streak;
            document.getElementById('levelDisplay').textContent = gameState.level;

            const accuracy = gameState.totalQuestions > 0 ?
                Math.round((gameState.correctAnswers / gameState.totalQuestions) * 100) : 0;
            document.getElementById('accuracy').textContent = accuracy + '%';

            const progress = ((gameState.currentRound - 1) / gameState.totalRounds) * 100;
            document.getElementById('progressFill').style.width = progress + '%';
        }

        // 结束游戏
        function endGame() {
            gameState.gameActive = false;

            const accuracy = gameState.totalQuestions > 0 ?
                Math.round((gameState.correctAnswers / gameState.totalQuestions) * 100) : 0;

            document.getElementById('audioText').textContent = '🎊 游戏结束！';
            document.getElementById('progressText').textContent =
                `游戏完成！正确率: ${accuracy}% (${gameState.correctAnswers}/${gameState.totalQuestions})`;
            document.getElementById('feedback').textContent = `🏆 最终得分: ${gameState.score}分`;
            document.getElementById('feedback').className = 'feedback correct';

            document.getElementById('startBtn').disabled = false;
            document.getElementById('playButton').disabled = true;
            document.getElementById('submitBtn').disabled = true;
            document.getElementById('wordInput').disabled = true;
            document.getElementById('nextBtn').style.display = 'none';

            createSuccessParticles();

            if (window.opener) {
                window.opener.postMessage({
                    type: 'gameEnd',
                    score: gameState.score
                }, '*');
            }
        }

        // 添加CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes soundRing {
                0% { transform: translate(-50%, -50%) scale(0); opacity: 1; }
                100% { transform: translate(-50%, -50%) scale(3); opacity: 0; }
            }

            @keyframes particleFloat {
                0% { transform: translateY(0) rotate(0deg); opacity: 1; }
                100% { transform: translateY(-150px) rotate(360deg); opacity: 0; }
            }
        `;
        document.head.appendChild(style);

        // 键盘事件
        document.getElementById('wordInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter' && !document.getElementById('submitBtn').disabled) {
                checkAnswer();
            }
        });

        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            createSoundWaves();
            updateDisplay();

            // 检查浏览器是否支持语音合成
            if (!('speechSynthesis' in window)) {
                alert('您的浏览器不支持语音合成功能，请使用现代浏览器！');
            }
        });
    </script>
</body>
</html>
