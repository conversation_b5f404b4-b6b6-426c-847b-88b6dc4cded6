/**
 * 后台监控服务 - 全局注意力检测监控
 * 支持跨页面持续监控，只有手动停止才会结束
 */

import { reactive } from 'vue'
import dataCollectionService from './dataCollectionService'
import pythonServerAPI from '@/api/pythonServerAPI'

class BackgroundMonitorService {
  constructor() {
    // 响应式状态
    this.state = reactive({
      isMonitoring: false,
      isConnected: false,
      currentEmotion: {
        text: '未检测',
        icon: 'fas fa-meh'
      },
      attentionScore: 0,
      attentionLevel: 'normal',
      sessionId: null,
      sessionStartTime: null,
      sessionDuration: 0,
      pythonServerStatus: 'stopped', // stopped, starting, running, error
      lastDetectionTime: null
    })

    // WebRTC客户端实例
    this.webrtcClient = null

    // 视频元素引用
    this.videoElement = null

    // 会话计时器
    this.sessionTimer = null

    // 事件监听器
    this.listeners = new Map()

    // Python服务器进程
    this.pythonProcess = null
    
    console.log('🔧 后台监控服务已初始化')
  }

  /**
   * 设置视频元素引用
   */
  setVideoElement(videoElement) {
    this.videoElement = videoElement
    console.log('📹 视频元素已设置到后台监控服务')
  }

  /**
   * 启动后台监控
   */
  async startMonitoring() {
    if (this.state.isMonitoring) {
      console.log('⚠️ 监控已在运行中')
      return { success: true, message: '监控已在运行中' }
    }

    try {
      console.log('🚀 启动后台监控...')
      
      // 1. 启动Python服务器
      const serverResult = await this.startPythonServer()
      if (!serverResult.success) {
        throw new Error(serverResult.message)
      }

      // 2. 等待服务器启动
      await this.waitForServerReady()

      // 3. 启动WebRTC连接
      await this.startWebRTCConnection()

      // 4. 开始数据收集会话
      this.startDataCollectionSession()

      // 5. 启动会话计时器
      this.startSessionTimer()

      this.state.isMonitoring = true
      this.emit('monitoringStarted')
      
      console.log('✅ 后台监控启动成功')
      return { success: true, message: '后台监控启动成功' }

    } catch (error) {
      console.error('❌ 后台监控启动失败:', error)
      this.state.pythonServerStatus = 'error'
      return { success: false, message: error.message }
    }
  }

  /**
   * 停止后台监控
   */
  async stopMonitoring() {
    if (!this.state.isMonitoring) {
      console.log('⚠️ 监控未在运行')
      return { success: true, message: '监控未在运行' }
    }

    try {
      console.log('🛑 停止后台监控...')

      // 1. 停止WebRTC连接
      if (this.webrtcClient) {
        this.webrtcClient.stopAnalysis()
        this.webrtcClient = null
      }

      // 2. 结束数据收集会话
      this.endDataCollectionSession()

      // 3. 停止会话计时器
      this.stopSessionTimer()

      // 4. 停止Python服务器
      await this.stopPythonServer()

      // 5. 重置状态
      this.resetState()

      this.state.isMonitoring = false
      this.emit('monitoringStopped')

      console.log('✅ 后台监控已停止')
      return { success: true, message: '后台监控已停止' }

    } catch (error) {
      console.error('❌ 停止监控失败:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 启动Python服务器
   */
  async startPythonServer() {
    try {
      this.state.pythonServerStatus = 'starting'
      console.log('🐍 启动Python WebRTC服务器...')

      // 使用Python服务器API启动
      const result = await pythonServerAPI.autoStartServer()

      if (result.success) {
        this.state.pythonServerStatus = 'running'
        return result
      } else {
        this.state.pythonServerStatus = 'error'
        return result
      }

    } catch (error) {
      console.error('Python服务器启动失败:', error)
      this.state.pythonServerStatus = 'error'
      return {
        success: false,
        message: `启动失败: ${error.message}`
      }
    }
  }

  /**
   * 停止Python服务器
   */
  async stopPythonServer() {
    try {
      console.log('🛑 停止Python服务器...')

      // 使用Python服务器API停止
      const result = await pythonServerAPI.stopServer()
      this.state.pythonServerStatus = 'stopped'
      return result

    } catch (error) {
      console.error('停止Python服务器失败:', error)
      this.state.pythonServerStatus = 'stopped'
      return { success: false, message: error.message }
    }
  }

  /**
   * 检查服务器健康状态
   */
  async checkServerHealth() {
    return await pythonServerAPI.checkServerHealth()
  }

  /**
   * 等待服务器就绪
   */
  async waitForServerReady(maxAttempts = 10) {
    return await pythonServerAPI.waitForServerReady(maxAttempts)
  }

  /**
   * 启动WebRTC连接
   */
  async startWebRTCConnection() {
    // 动态导入WebRTC客户端
    const { default: WebRTCAttentionClient } = await import('@/utils/webrtcAttentionClient')

    this.webrtcClient = new WebRTCAttentionClient()

    // 设置视频元素（如果已设置）
    if (this.videoElement) {
      this.webrtcClient.setVideoElement(this.videoElement)
      console.log('📹 视频元素已设置到WebRTC客户端')
    }

    // 设置结果回调
    this.webrtcClient.setResultCallback((result) => {
      this.updateDetectionResult(result)
    })

    // 设置状态回调，监听连接状态变化
    this.webrtcClient.setStatusCallback = (message, type) => {
      console.log('WebRTC状态更新:', message, type)

      // 根据消息内容更新连接状态
      if (message.includes('注意力分析已启动') || message.includes('启动成功') || message.includes('WebRTC连接成功')) {
        this.state.isConnected = true
        console.log('✅ WebRTC连接状态已更新为已连接')
      } else if (message.includes('连接失败') || message.includes('启动失败')) {
        this.state.isConnected = false
        console.log('❌ WebRTC连接状态已更新为未连接')
      }
    }

    // 启动分析
    await this.webrtcClient.startAnalysis()

    // 等待一段时间让连接建立
    setTimeout(() => {
      if (this.webrtcClient && this.webrtcClient.isAnalysisRunning && this.webrtcClient.isAnalysisRunning()) {
        this.state.isConnected = true
        console.log('✅ WebRTC连接检查完成，状态已更新')
      }
    }, 3000)
  }

  /**
   * 更新检测结果
   */
  updateDetectionResult(result) {
    console.log('🔍 后台监控收到检测结果:', result);

    this.state.currentEmotion = {
      text: result.level_name || '未检测',
      icon: this.getAttentionIcon(result.attention_level)
    }

    this.state.attentionScore = result.attention_score || 0
    this.state.attentionLevel = result.attention_level || 'normal'
    this.state.lastDetectionTime = new Date()

    // 添加到数据收集服务
    if (this.state.sessionId) {
      console.log('📊 向数据收集服务发送数据，会话ID:', this.state.sessionId);
      const recordResult = dataCollectionService.recordDetection({
        attention_score: result.attention_score,
        attention_level: result.attention_level,
        emotion: result.emotion,
        level_name: result.level_name,
        confidence: result.confidence,
        details: result.details
      });
      console.log('📊 数据记录结果:', recordResult);
    } else {
      console.warn('⚠️ 没有活动的会话ID，无法记录数据');
    }

    // 触发事件
    console.log('📡 触发detectionResult事件，监听器数量:', this.listeners.get('detectionResult')?.length || 0);
    this.emit('detectionResult', result)
  }

  /**
   * 获取注意力图标
   */
  getAttentionIcon(level) {
    const iconMap = {
      'high': 'fas fa-brain',
      'focused': 'fas fa-eye',
      'normal': 'fas fa-meh',
      'distracted': 'fas fa-frown',
      'low': 'fas fa-tired'
    }
    return iconMap[level] || 'fas fa-meh'
  }

  /**
   * 开始数据收集会话
   */
  startDataCollectionSession() {
    const session = dataCollectionService.startSession()
    this.state.sessionId = session.id
    this.state.sessionStartTime = new Date()
    console.log('📊 数据收集会话已开始:', this.state.sessionId)
  }

  /**
   * 结束数据收集会话
   */
  endDataCollectionSession() {
    if (this.state.sessionId) {
      dataCollectionService.endSession()
      console.log('📊 数据收集会话已结束:', this.state.sessionId)
      this.state.sessionId = null
    }
  }

  /**
   * 启动会话计时器
   */
  startSessionTimer() {
    this.sessionTimer = setInterval(() => {
      if (this.state.sessionStartTime) {
        this.state.sessionDuration = Math.floor(
          (new Date() - this.state.sessionStartTime) / 1000
        )
      }
    }, 1000)
  }

  /**
   * 停止会话计时器
   */
  stopSessionTimer() {
    if (this.sessionTimer) {
      clearInterval(this.sessionTimer)
      this.sessionTimer = null
    }
  }

  /**
   * 重置状态
   */
  resetState() {
    this.state.isConnected = false
    this.state.currentEmotion = {
      text: '未检测',
      icon: 'fas fa-meh'
    }
    this.state.attentionScore = 0
    this.state.attentionLevel = 'normal'
    this.state.sessionId = null
    this.state.sessionStartTime = null
    this.state.sessionDuration = 0
    this.state.lastDetectionTime = null
  }

  /**
   * 事件监听
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  /**
   * 移除事件监听
   */
  off(event, callback) {
    if (this.listeners.has(event)) {
      const callbacks = this.listeners.get(event)
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 触发事件
   */
  emit(event, data) {
    console.log(`📡 触发事件: ${event}, 监听器数量: ${this.listeners.get(event)?.length || 0}`);
    if (this.listeners.has(event)) {
      this.listeners.get(event).forEach((callback, index) => {
        try {
          console.log(`📡 执行监听器 ${index + 1}/${this.listeners.get(event).length}`);
          callback(data)
        } catch (error) {
          console.error('事件回调执行失败:', error)
        }
      })
    } else {
      console.warn(`⚠️ 没有找到事件 ${event} 的监听器`);
    }
  }

  /**
   * 获取当前状态
   */
  getState() {
    return this.state
  }

  /**
   * 获取会话时长（格式化）
   */
  getFormattedDuration() {
    const duration = this.state.sessionDuration
    const minutes = Math.floor(duration / 60)
    const seconds = duration % 60
    return `${minutes}:${seconds.toString().padStart(2, '0')}`
  }
}

// 创建单例实例
const backgroundMonitorService = new BackgroundMonitorService()

export default backgroundMonitorService
