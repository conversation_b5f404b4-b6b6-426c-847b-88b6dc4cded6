{"doc": " 工作流设计器获取任务执行人\n\n <AUTHOR> Li\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectRolesByTaskAssigneeList", "paramTypes": ["org.dromara.common.core.domain.model.TaskAssigneeBody"], "doc": " 查询角色并返回任务指派的列表，支持分页\n\n @param taskQuery 查询条件\n @return 办理人\n"}, {"name": "selectPostsByTaskAssigneeList", "paramTypes": ["org.dromara.common.core.domain.model.TaskAssigneeBody"], "doc": " 查询岗位并返回任务指派的列表，支持分页\n\n @param taskQuery 查询条件\n @return 办理人\n"}, {"name": "selectDeptsByTaskAssigneeList", "paramTypes": ["org.dromara.common.core.domain.model.TaskAssigneeBody"], "doc": " 查询部门并返回任务指派的列表，支持分页\n\n @param taskQuery 查询条件\n @return 办理人\n"}, {"name": "selectUsersByTaskAssigneeList", "paramTypes": ["org.dromara.common.core.domain.model.TaskAssigneeBody"], "doc": " 查询用户并返回任务指派的列表，支持分页\n\n @param taskQuery 查询条件\n @return 办理人\n"}], "constructors": []}