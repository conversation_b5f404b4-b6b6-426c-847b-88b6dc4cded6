package org.dromara.patient.service;

import org.dromara.patient.domain.MedicalRecord;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;

/**
 * 医疗分析服务接口
 *
 * <AUTHOR> System
 * @date 2025-07-19
 */
public interface IMedicalAnalysisService {

    /**
     * 查询医疗分析记录列表
     *
     * @param medicalRecord 医疗分析记录
     * @param pageQuery     分页查询
     * @return 医疗分析记录集合
     */
    TableDataInfo<MedicalRecord> queryPageList(MedicalRecord medicalRecord, PageQuery pageQuery);

    /**
     * 查询医疗分析记录列表
     *
     * @param medicalRecord 医疗分析记录
     * @return 医疗分析记录集合
     */
    List<MedicalRecord> queryList(MedicalRecord medicalRecord);

    /**
     * 根据主键查询医疗分析记录
     *
     * @param id 主键
     * @return 医疗分析记录
     */
    MedicalRecord queryById(Long id);

    /**
     * 新增医疗分析记录
     *
     * @param medicalRecord 医疗分析记录
     * @return 结果
     */
    Boolean insertByBo(MedicalRecord medicalRecord);

    /**
     * 修改医疗分析记录
     *
     * @param medicalRecord 医疗分析记录
     * @return 结果
     */
    Boolean updateByBo(MedicalRecord medicalRecord);

    /**
     * 校验并批量删除医疗分析记录信息
     *
     * @param ids 主键集合
     * @return 结果
     */
    Boolean deleteWithValidByIds(List<Long> ids);

    /**
     * 执行AI分析患者数据
     *
     * @param patientData 患者数据
     * @return 分析结果
     * @throws Exception 分析异常
     */
    MedicalRecord analyzePatientData(Map<String, Object> patientData) throws Exception;

    /**
     * 获取风险统计数据
     *
     * @return 统计数据
     * @throws Exception 统计异常
     */
    Map<String, Object> getRiskStatistics() throws Exception;

    /**
     * 生成AI诊断建议
     *
     * @param recordId 记录ID
     * @return 诊断建议
     * @throws Exception 生成异常
     */
    Map<String, Object> generateDiagnosticSuggestion(Long recordId) throws Exception;

    /**
     * 生成个性化干预方案
     *
     * @param recordId 记录ID
     * @return 干预方案
     * @throws Exception 生成异常
     */
    Map<String, Object> generateInterventionPlan(Long recordId) throws Exception;

    /**
     * 获取AI模型状态
     *
     * @return 模型状态
     * @throws Exception 获取异常
     */
    Map<String, Object> getAIModelStatus() throws Exception;

}
