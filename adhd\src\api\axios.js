/**
 * Axios 统一配置
 * 解决前后端数据传输问题
 */

import axios from 'axios'

// 创建axios实例
const service = axios.create({
  baseURL: 'http://localhost:80', // RuoYi-Vue-Plus-5.X后端地址
  timeout: 15000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    console.log('📤 发送请求:', {
      method: config.method?.toUpperCase(),
      url: config.url,
      data: config.data,
      params: config.params
    })
    
    // 可以在这里添加token等认证信息
    // const token = localStorage.getItem('token')
    // if (token) {
    //   config.headers['Authorization'] = `Bearer ${token}`
    // }
    
    return config
  },
  error => {
    console.error('❌ 请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    console.log('📥 收到响应:', {
      status: response.status,
      url: response.config.url,
      data: response.data
    })
    
    // 统一处理响应数据
    const res = response.data
    
    // RuoYi框架的标准响应格式
    if (res.code !== undefined) {
      if (res.code === 200) {
        // 成功响应
        return {
          success: true,
          data: res.data || res,
          message: res.msg || '操作成功',
          code: res.code
        }
      } else {
        // 业务错误
        console.warn('⚠️ 业务错误:', res.msg || '操作失败')
        return {
          success: false,
          data: null,
          message: res.msg || '操作失败',
          code: res.code
        }
      }
    }
    
    // 非标准响应格式，直接返回
    return {
      success: true,
      data: res,
      message: '操作成功'
    }
  },
  error => {
    console.error('❌ 响应错误:', error)
    
    let message = '网络错误'
    let code = 0
    
    if (error.response) {
      // 服务器响应了错误状态码
      const { status, data } = error.response
      code = status
      
      switch (status) {
        case 400:
          message = data?.msg || '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 可以在这里处理登录过期
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = data?.msg || '服务器内部错误'
          break
        default:
          message = data?.msg || `服务器错误 ${status}`
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      message = '网络连接失败，请检查网络'
    } else {
      // 其他错误
      message = error.message || '请求失败'
    }
    
    return {
      success: false,
      data: null,
      message,
      code,
      error: error.response?.data || error.message
    }
  }
)

// 导出常用的请求方法
export default service

// 封装常用请求方法
export const request = {
  // GET请求
  get(url, params = {}) {
    return service({
      method: 'GET',
      url,
      params
    })
  },
  
  // POST请求
  post(url, data = {}) {
    return service({
      method: 'POST',
      url,
      data
    })
  },
  
  // PUT请求
  put(url, data = {}) {
    return service({
      method: 'PUT',
      url,
      data
    })
  },
  
  // DELETE请求
  delete(url, params = {}) {
    return service({
      method: 'DELETE',
      url,
      params
    })
  }
}

// 专门用于文件上传的请求
export const uploadRequest = axios.create({
  baseURL: 'http://localhost:80',
  timeout: 30000, // 文件上传超时时间更长
  headers: {
    'Content-Type': 'multipart/form-data'
  }
})

// 检查服务器连接状态
export const checkServerConnection = async () => {
  try {
    const response = await service.get('/actuator/health')
    return {
      success: true,
      message: '服务器连接正常',
      data: response.data
    }
  } catch (error) {
    return {
      success: false,
      message: '无法连接到服务器',
      error: error.message
    }
  }
}

// 统一错误处理函数
export const handleApiError = (error, defaultMessage = '操作失败') => {
  const message = error.message || defaultMessage
  console.error('API错误:', message, error)
  
  // 可以在这里添加全局错误提示
  // 例如：Message.error(message)
  
  return {
    success: false,
    message,
    error
  }
}

// 数据格式化工具
export const formatters = {
  // 格式化日期为RuoYi后端格式
  formatDateForRuoYi(date) {
    if (!date) return null

    const d = new Date(date)
    if (isNaN(d.getTime())) return null

    // 对于DetectionSessions等使用@JsonFormat注解的实体类，需要特定格式
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  },

  // 格式化日期为ISO格式（用于ScaleTestRecords等）
  formatDateForISO(date) {
    if (!date) return null

    const d = new Date(date)
    if (isNaN(d.getTime())) return null

    // MySQL datetime 格式：yyyy-MM-dd HH:mm:ss
    const year = d.getFullYear()
    const month = String(d.getMonth() + 1).padStart(2, '0')
    const day = String(d.getDate()).padStart(2, '0')
    const hours = String(d.getHours()).padStart(2, '0')
    const minutes = String(d.getMinutes()).padStart(2, '0')
    const seconds = String(d.getSeconds()).padStart(2, '0')

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
  },
  
  // 确保数值类型
  ensureNumber(value, defaultValue = 0) {
    const num = Number(value)
    return isNaN(num) ? defaultValue : num
  },
  
  // 确保字符串类型
  ensureString(value, defaultValue = '') {
    return value ? String(value) : defaultValue
  },

  // 格式化性别字段，将中文转换为英文
  formatGender(gender) {
    if (!gender) return ''

    const genderStr = String(gender).toLowerCase()

    // 中文到英文的映射
    if (genderStr.includes('男') || genderStr === 'male' || genderStr === 'm') {
      return 'male'
    } else if (genderStr.includes('女') || genderStr === 'female' || genderStr === 'f') {
      return 'female'
    } else {
      return genderStr.substring(0, 10) // 限制长度避免数据截断
    }
  },

  // 格式化风险等级字段，将中文转换为英文并限制长度
  formatRiskLevel(riskLevel) {
    if (!riskLevel) return ''

    const riskStr = String(riskLevel).toLowerCase()

    // 中文到英文的映射
    if (riskStr.includes('低') || riskStr.includes('low')) {
      return 'low'
    } else if (riskStr.includes('中') || riskStr.includes('medium') || riskStr.includes('moderate')) {
      return 'medium'
    } else if (riskStr.includes('高') || riskStr.includes('high')) {
      return 'high'
    } else if (riskStr.includes('极高') || riskStr.includes('critical') || riskStr.includes('severe')) {
      return 'critical'
    } else {
      // 如果是其他值，截断到20个字符以内
      return riskStr.substring(0, 20)
    }
  }
}

console.log('✅ Axios配置已加载')
