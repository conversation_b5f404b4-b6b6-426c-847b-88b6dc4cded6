{"name": "adhd-backend-server", "version": "1.0.0", "description": "ADHD项目后端API服务器，用于管理Python WebRTC服务器", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["adhd", "webrtc", "python", "server", "api"], "author": "ADHD Project Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0"}}