<template>
  <div class="page-container">
    <!-- 公共头部组件 -->
    <AppHeader
      :user="currentUser"
      current-route="ai-rehabilitation"
      @search="handleSearch"
      @show-notifications="showNotifications"
      @show-messages="showMessages"
      @show-settings="showSettings"
      @show-user-menu="showUserMenu"
    />

    <!-- 内容包装区 -->
    <div class="content-wrapper">
      <!-- 公共侧边栏组件 -->
      <AppSidebar
        current-route="ai-rehabilitation"
        :expanded="navExpanded"
        @expand="expandNav"
        @collapse="collapseNav"
      />

      <!-- 主内容区 -->
      <div class="main-area">
        <div class="page-main-container">
          <div class="area-header">
            <h3><i class="fas fa-robot"></i> AI定制康复计划</h3>
          </div>

          <div class="rehab-content">
            <!-- 左侧：用户信息和选项 -->
            <div class="input-section">
              <div class="input-card">
                <h4>基本信息</h4>
                <div class="info-group">
                  <label>姓名：</label>
                  <input type="text" v-model="userInfo.name" readonly>
                </div>
                <div class="info-group">
                  <label>年龄：</label>
                  <select v-model="userInfo.age">
                    <option value="">请选择</option>
                    <option value="child">6-12岁</option>
                    <option value="teen">13-17岁</option>
                    <option value="adult">18岁以上</option>
                  </select>
                </div>
                <div class="info-group">
                  <label>ADHD类型：</label>
                  <select v-model="userInfo.adhdType">
                    <option value="">请选择</option>
                    <option value="inattentive">注意力缺陷为主</option>
                    <option value="hyperactive">多动冲动为主</option>
                    <option value="combined">混合型</option>
                  </select>
                </div>
              </div>

              <div class="input-card">
                <h4>症状严重程度</h4>
                <div class="severity-slider">
                  <label>注意力问题：</label>
                  <input type="range" v-model="severity.attention" min="1" max="10">
                  <span class="severity-value">{{ severity.attention }}</span>
                </div>
                <div class="severity-slider">
                  <label>多动/冲动：</label>
                  <input type="range" v-model="severity.hyperactivity" min="1" max="10">
                  <span class="severity-value">{{ severity.hyperactivity }}</span>
                </div>
                <div class="severity-slider">
                  <label>情绪调节：</label>
                  <input type="range" v-model="severity.emotion" min="1" max="10">
                  <span class="severity-value">{{ severity.emotion }}</span>
                </div>
              </div>

              <div class="input-card">
                <h4>康复重点领域</h4>
                <div class="focus-options">
                  <label class="checkbox-option">
                    <input type="checkbox" value="academic" v-model="focusAreas" checked> 学业表现
                  </label>
                  <label class="checkbox-option">
                    <input type="checkbox" value="social" v-model="focusAreas"> 社交能力
                  </label>
                  <label class="checkbox-option">
                    <input type="checkbox" value="emotion" v-model="focusAreas"> 情绪管理
                  </label>
                  <label class="checkbox-option">
                    <input type="checkbox" value="executive" v-model="focusAreas"> 执行功能
                  </label>
                  <label class="checkbox-option">
                    <input type="checkbox" value="daily" v-model="focusAreas"> 日常生活
                  </label>
                </div>
              </div>

              <!-- Ollama状态显示 -->
              <div class="ollama-status" v-if="ollamaAvailable">
                <div class="status-indicator">
                  <i class="fas fa-robot text-success"></i>
                  <span class="status-text">AI大模型已连接</span>
                </div>
              </div>
              <div class="ollama-status" v-else>
                <div class="status-indicator">
                  <i class="fas fa-exclamation-triangle text-warning"></i>
                  <span class="status-text">AI大模型未连接，将使用本地生成</span>
                </div>
              </div>

              <!-- 错误信息显示 -->
              <div v-if="errorMessage" class="error-message">
                <i class="fas fa-exclamation-circle"></i>
                {{ errorMessage }}
              </div>

              <button @click="generatePlan" class="primary-btn" :disabled="isGenerating">
                <i class="fas fa-magic"></i> {{ isGenerating ? '生成中...' : '生成康复计划' }}
              </button>
            </div>

            <!-- 右侧：生成的康复计划 -->
            <div class="plan-section">
              <div v-if="!planGenerated" class="plan-status">
                <p v-if="!isGenerating">请填写左侧信息并点击"生成康复计划"按钮</p>
                <p v-else><i class="fas fa-spinner fa-spin"></i> 正在生成个性化康复计划，请稍候...</p>
              </div>

              <div v-if="planGenerated" class="plan-content">
                <div class="plan-header">
                  <h3>{{ planTitle }}</h3>
                  <div class="plan-meta">
                    <span>创建日期: <span>{{ planDate }}</span></span>
                    <span>推荐周期: <span>{{ planDuration }}</span></span>
                  </div>
                  <div class="plan-actions">
                    <button class="action-btn-new" @click="savePlan">
                      <span class="btn-icon">💾</span>
                      <span class="btn-text">保存</span>
                    </button>
                    <button class="action-btn-new" @click="printPlan">
                      <span class="btn-icon">🖨️</span>
                      <span class="btn-text">打印</span>
                    </button>
                    <button class="action-btn-new" @click="sharePlan">
                      <span class="btn-icon">📤</span>
                      <span class="btn-text">分享</span>
                    </button>
                  </div>
                </div>

                <div class="plan-overview">
                  <h4>计划概述</h4>
                  <p>{{ planOverview }}</p>
                </div>

                <div class="plan-sections">
                  <div class="plan-section-item">
                    <h4>💡 认知训练</h4>
                    <div class="content-list-new">
                      <div v-for="(item, index) in cognitiveTraining" :key="index" class="content-item-new">
                        <span class="check-icon">•</span>
                        <span class="content-text">{{ item }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="plan-section-item">
                    <h4>🎯 行为策略</h4>
                    <div class="content-list-new">
                      <div v-for="(item, index) in behavioralStrategies" :key="index" class="content-item-new">
                        <span class="check-icon">•</span>
                        <span class="content-text">{{ item }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="plan-section-item">
                    <h4>🌱 生活方式调整</h4>
                    <div class="content-list-new">
                      <div v-for="(item, index) in lifestyleAdjustments" :key="index" class="content-item-new">
                        <span class="check-icon">•</span>
                        <span class="content-text">{{ item }}</span>
                      </div>
                    </div>
                  </div>

                  <div class="plan-section-item">
                    <h4>🤝 社交支持</h4>
                    <div class="content-list-new">
                      <div v-for="(item, index) in socialSupport" :key="index" class="content-item-new">
                        <span class="check-icon">•</span>
                        <span class="content-text">{{ item }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="plan-progress">
                  <h4>进度跟踪</h4>
                  <div class="progress-chart">
                    <div class="week-markers">
                      <span>第1周</span>
                      <span>第2周</span>
                      <span>第4周</span>
                      <span>第6周</span>
                      <span>第8周</span>
                    </div>
                    <div class="milestones">
                      <div class="milestone" v-for="milestone in milestones" :key="milestone">
                        <div class="milestone-dot"></div>
                        <span>{{ milestone }}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="plan-resources">
                  <h4>📚 推荐资源</h4>
                  <div class="resources-list-new">
                    <div v-for="(resource, index) in resources" :key="index" class="resource-item-new">
                      <span class="resource-icon">⭐</span>
                      <span class="resource-text">{{ resource }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <footer>
      <p>© 2025 Healthit - 智能健康管理平台 | <a href="#">使用条款</a> | <a href="#">隐私政策</a></p>
    </footer>
  </div>
</template>

<script>
import AppHeader from '@/components/common/AppHeader.vue'
import AppSidebar from '@/components/common/AppSidebar.vue'
import ollamaService from '@/services/ollamaService.js'

export default {
  name: 'AIIntelligentRehabilitation',
  components: {
    AppHeader,
    AppSidebar
  },
  data() {
    return {
      // 搜索查询
      searchQuery: '',

      // 导航状态
      navExpanded: false,

      // 当前用户信息
      currentUser: {
        name: '张同学',
        avatar: 'https://randomuser.me/api/portraits/men/44.jpg'
      },
      
      // 用户信息
      userInfo: {
        name: '张同学',
        age: '',
        adhdType: ''
      },
      
      // 症状严重程度
      severity: {
        attention: 5,
        hyperactivity: 5,
        emotion: 5
      },
      
      // 重点领域
      focusAreas: ['academic'],
      
      // 计划生成状态
      isGenerating: false,
      planGenerated: false,

      // Ollama服务状态
      ollamaAvailable: false,
      ollamaModels: [],
      selectedModel: 'llama3-chinese',

      // 错误状态
      errorMessage: '',
      
      // 计划内容
      planTitle: '',
      planDate: '',
      planDuration: '8周',
      planOverview: '',
      cognitiveTraining: [],
      behavioralStrategies: [],
      lifestyleAdjustments: [],
      socialSupport: [],
      milestones: ['初步适应', '建立习惯', '技能提升', '稳定表现', '阶段评估'],
      resources: []
    }
  },
  
  async mounted() {
    // 初始化导航状态
    if (localStorage.getItem('navExpanded') === 'true') {
      this.navExpanded = true;
    }

    // 检查Ollama服务状态
    await this.checkOllamaService();
  },
  
  methods: {
    // AppHeader 事件处理
    handleSearch(query) {
      console.log('搜索:', query);
      // 实现搜索逻辑
    },

    showNotifications() {
      console.log('显示通知');
      // 实现通知逻辑
    },

    showMessages() {
      console.log('显示消息');
      // 实现消息逻辑
    },

    showSettings() {
      console.log('显示设置');
      // 实现设置逻辑
    },

    showUserMenu() {
      console.log('显示用户菜单');
      // 实现用户菜单逻辑
    },

    // 导航展开
    expandNav() {
      this.navExpanded = true;
      localStorage.setItem('navExpanded', 'true');
    },
    
    // 导航收起
    collapseNav(e) {
      // 检查事件对象是否存在
      if (e && e.clientX !== undefined) {
        const mouseX = e.clientX;
        if (mouseX > 180) {
          this.navExpanded = false;
          localStorage.removeItem('navExpanded');
        }
      } else {
        // 如果没有事件对象，直接收起导航
        this.navExpanded = false;
        localStorage.removeItem('navExpanded');
      }
    },

    // 生成康复计划
    async generatePlan() {
      // 验证必填字段
      if (!this.userInfo.age || !this.userInfo.adhdType) {
        alert('请选择年龄段和ADHD类型');
        return;
      }

      // 检查是否选择了至少一个重点领域
      if (this.focusAreas.length === 0) {
        alert('请至少选择一个康复重点领域');
        return;
      }

      // 显示加载状态
      this.isGenerating = true;
      this.errorMessage = '';

      try {
        if (this.ollamaAvailable) {
          // 使用Ollama生成康复计划
          await this.generatePlanWithOllama();
        } else {
          // 使用本地模拟生成
          this.generateRehabilitationPlan();
        }
        this.planGenerated = true;
      } catch (error) {
        console.error('生成康复计划失败:', error);
        this.errorMessage = error.message || '生成康复计划失败，请稍后重试';
        // 如果AI生成失败，回退到本地生成
        this.generateRehabilitationPlan();
        this.planGenerated = true;
      } finally {
        this.isGenerating = false;
      }
    },

    // 检查Ollama服务状态
    async checkOllamaService() {
      try {
        this.ollamaAvailable = await ollamaService.checkHealth();
        if (this.ollamaAvailable) {
          this.ollamaModels = await ollamaService.getModels();
          console.log('Ollama服务可用，可用模型:', this.ollamaModels.map(m => m.name));
        } else {
          console.warn('Ollama服务不可用，将使用本地模拟生成');
        }
      } catch (error) {
        console.error('检查Ollama服务失败:', error);
        this.ollamaAvailable = false;
      }
    },

    // 使用Ollama生成康复计划
    async generatePlanWithOllama() {
      const userProfile = {
        name: this.userInfo.name,
        age: this.userInfo.age,
        adhdType: this.userInfo.adhdType
      };

      const planData = await ollamaService.generateRehabilitationPlan(
        userProfile,
        this.severity,
        this.focusAreas
      );

      // 应用生成的计划数据
      this.planTitle = planData.planTitle;
      this.planDate = new Date().toLocaleDateString();
      this.planDuration = planData.planDuration;
      this.planOverview = planData.planOverview;
      this.cognitiveTraining = planData.cognitiveTraining;
      this.behavioralStrategies = planData.behavioralStrategies;
      this.lifestyleAdjustments = planData.lifestyleAdjustments;
      this.socialSupport = planData.socialSupport;
      this.resources = planData.resources;

      // 如果有里程碑数据，使用它；否则使用默认值
      if (planData.milestones && planData.milestones.length > 0) {
        this.milestones = planData.milestones;
      }
    },

    // 生成康复计划内容
    generateRehabilitationPlan() {
      const age = this.userInfo.age;
      const type = this.userInfo.adhdType;
      const attentionScore = parseInt(this.severity.attention);
      const hyperactivityScore = parseInt(this.severity.hyperactivity);
      const emotionScore = parseInt(this.severity.emotion);

      // 设置计划标题和日期
      this.planTitle = this.getAgeSpecificTitle(age) + ' ADHD康复计划';
      this.planDate = new Date().toLocaleDateString();

      // 生成概述内容
      this.planOverview = this.generateOverviewText(age, type, attentionScore, hyperactivityScore, emotionScore);

      // 生成各部分内容
      this.cognitiveTraining = this.generateCognitiveTraining(age, type, attentionScore);
      this.behavioralStrategies = this.generateBehavioralStrategies(age, type, hyperactivityScore);
      this.lifestyleAdjustments = this.generateLifestyleAdjustments(age, emotionScore);
      this.socialSupport = this.generateSocialSupport(age, this.focusAreas);
      this.resources = this.generateResources(age, type, this.focusAreas);
    },

    
    
     
 
     

     
    // 保存计划
    savePlan() {
      try {
        const planData = {
          title: this.planTitle,
          date: this.planDate,
          duration: this.planDuration,
          overview: this.planOverview,
          cognitiveTraining: this.cognitiveTraining,
          behavioralStrategies: this.behavioralStrategies,
          lifestyleAdjustments: this.lifestyleAdjustments,
          socialSupport: this.socialSupport,
          resources: this.resources,
          milestones: this.milestones
        };

        // 保存到localStorage
        const savedPlans = JSON.parse(localStorage.getItem('savedPlans') || '[]');
        savedPlans.push({
          id: Date.now(),
          ...planData,
          savedAt: new Date().toISOString()
        });
        localStorage.setItem('savedPlans', JSON.stringify(savedPlans));

        alert('✅ 康复计划已成功保存到本地！');
      } catch (error) {
        console.error('保存计划失败:', error);
        alert('❌ 保存失败，请稍后重试');
      }
    },

    // 打印计划
    printPlan() {
      try {
        // 创建打印样式
        const printStyles = `
          <style>
            @media print {
              body * { visibility: hidden; }
              .plan-content, .plan-content * { visibility: visible; }
              .plan-content {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                background: white;
                padding: 20px;
              }
              .plan-actions { display: none !important; }
              .action-btn { display: none !important; }
              h3, h4 { color: #333 !important; }
              .content-item {
                border: 1px solid #ddd;
                margin-bottom: 8px;
                padding: 8px;
                page-break-inside: avoid;
              }
              .plan-section-item {
                page-break-inside: avoid;
                margin-bottom: 20px;
              }
            }
          </style>
        `;

        // 添加打印样式到head
        const styleElement = document.createElement('style');
        styleElement.innerHTML = printStyles;
        document.head.appendChild(styleElement);

        // 执行打印
        window.print();

        // 打印完成后移除样式
        setTimeout(() => {
          document.head.removeChild(styleElement);
        }, 1000);

      } catch (error) {
        console.error('打印失败:', error);
        alert('❌ 打印失败，请稍后重试');
      }
    },

    // 分享计划
    sharePlan() {
      try {
        const planText = `
${this.planTitle}
创建日期: ${this.planDate}
推荐周期: ${this.planDuration}

计划概述:
${this.planOverview}

认知训练:
${this.cognitiveTraining.map(item => `• ${item}`).join('\n')}

行为策略:
${this.behavioralStrategies.map(item => `• ${item}`).join('\n')}

生活方式调整:
${this.lifestyleAdjustments.map(item => `• ${item}`).join('\n')}

社交支持:
${this.socialSupport.map(item => `• ${item}`).join('\n')}

推荐资源:
${this.resources.map(item => `• ${item}`).join('\n')}
        `.trim();

        // 尝试使用现代的分享API
        if (navigator.share) {
          navigator.share({
            title: this.planTitle,
            text: planText,
          }).then(() => {
            console.log('分享成功');
          }).catch((error) => {
            console.log('分享取消或失败:', error);
            this.fallbackShare(planText);
          });
        } else {
          this.fallbackShare(planText);
        }
      } catch (error) {
        console.error('分享失败:', error);
        alert('❌ 分享失败，请稍后重试');
      }
    },

    // 备用分享方法
    fallbackShare(text) {
      try {
        // 复制到剪贴板
        navigator.clipboard.writeText(text).then(() => {
          alert('✅ 康复计划内容已复制到剪贴板！您可以粘贴到任何地方分享。');
        }).catch(() => {
          // 如果剪贴板API不可用，使用传统方法
          const textArea = document.createElement('textarea');
          textArea.value = text;
          document.body.appendChild(textArea);
          textArea.select();
          document.execCommand('copy');
          document.body.removeChild(textArea);
          alert('✅ 康复计划内容已复制到剪贴板！您可以粘贴到任何地方分享。');
        });
      } catch (error) {
        console.error('复制到剪贴板失败:', error);
        alert('❌ 复制失败，请手动选择内容进行复制');
      }
    }
  }
}
</script>

<style scoped>
/* 全局样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
}

/* 核心变量 */
:root {
  --primary-color: #4e73df;
  --primary-light: #e8efff;
  --primary-dark: #3a5bbd;
  --secondary-color: #f8f9fc;
  --success-color: #1cc88a;
  --info-color: #36b9cc;
  --warning-color: #f6c23e;
  --danger-color: #e74a3b;
  --text-color: #4e4e4e;
  --text-light: #7e7e7e;
  --border-color: #e3e6f0;
  --border-radius: 12px;
  --card-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  --card-shadow-hover: 0 12px 28px rgba(71, 118, 230, 0.15);
  --transition-speed: 0.3s;
  --header-height: 60px;
}

.ai-rehabilitation-page {
  background-color: #f5f7fa;
  color: var(--text-color);
  min-height: 100vh;
}

/* 头部样式 */
header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 25px;
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
  height: var(--header-height);
}

.logo {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
}

.logo i {
  font-size: 24px;
  margin-right: 10px;
}

.search-bar {
  display: flex;
  align-items: center;
  background-color: #f1f2f6;
  border-radius: 20px;
  padding: 5px 15px;
  width: 300px;
  transition: all var(--transition-speed);
}

.search-bar:focus-within {
  box-shadow: 0 0 0 2px var(--primary-light);
}

.search-bar input {
  border: none;
  background: none;
  outline: none;
  width: 100%;
  padding: 8px;
  font-size: 14px;
}

.search-bar button {
  background: none;
  border: none;
  color: var(--primary-color);
  cursor: pointer;
  transition: transform var(--transition-speed);
}

.search-bar button:hover {
  transform: scale(1.1);
}

.user-actions {
  display: flex;
  align-items: center;
}

.actions {
  display: flex;
  margin-right: 20px;
}

.icon-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 18px;
  margin-left: 15px;
  position: relative;
  cursor: pointer;
  transition: color var(--transition-speed);
}

.icon-btn:hover {
  color: var(--primary-color);
}

.badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: var(--danger-color);
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.username {
  margin-right: 10px;
  font-weight: 500;
}

.avatar {
  width: 38px;
  height: 38px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--primary-light);
  transition: transform var(--transition-speed);
}

.avatar:hover {
  transform: scale(1.05);
}

/* 内容区域样式 */
.content-wrapper {
  display: flex;
  height: calc(100vh - var(--header-height) - 40px);
}

/* 导航栏样式 */
nav {
  width: 80px;
  background: linear-gradient(180deg, #434d73 0%, #394366 100%);
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  box-shadow: 5px 0 15px rgba(0, 0, 0, 0.1);
  transition: width var(--transition-speed);
  overflow-y: auto;
  position: relative;
  z-index: 95;
}

nav.expanded {
  width: 180px;
}

.nav-item {
  color: white;
  text-decoration: none;
  margin: 10px 0;
  padding: 12px;
  border-radius: 10px;
  width: 85%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: all var(--transition-speed);
  position: relative;
}

nav.expanded .nav-item {
  flex-direction: row;
  justify-content: flex-start;
  gap: 15px;
  padding: 12px 15px;
}

.nav-item i {
  font-size: 1.3rem;
  margin-bottom: 5px;
}

nav.expanded .nav-item i {
  margin-bottom: 0;
}

.nav-item span {
  font-size: 0.85rem;
  opacity: 0;
  transition: opacity var(--transition-speed);
  white-space: nowrap;
}

nav.expanded .nav-item span {
  opacity: 1;
}

.nav-item:hover, .nav-item.active {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px);
}

.nav-item::after {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: white;
  opacity: 0;
  transition: opacity var(--transition-speed);
}

.nav-item:hover::after, .nav-item.active::after {
  opacity: 1;
}

/* 主内容区样式 */
.main-area {
  flex: 1;
  padding: 25px;
  overflow-y: auto;
}

/* 康复计划容器 */
.rehab-container {
  background-color: #fff;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: box-shadow var(--transition-speed);
}

.rehab-container:hover {
  box-shadow: var(--card-shadow-hover);
}

/* 区域头部调整 */
.area-header {
  padding: 18px 25px;
  background: linear-gradient(to right, var(--primary-light), #f8f9fc);
}

.area-header h3 {
  font-size: 20px;
  color: var(--primary-dark);
  display: flex;
  align-items: center;
}

.area-header h3 i {
  margin-right: 12px;
  font-size: 1.3rem; /* 与导航栏图标大小一致 */
  color: #4e73df; /* 与导航栏图标颜色一致 */
  font-family: "Font Awesome 6 Free", "Font Awesome 5 Free", "FontAwesome";
  font-weight: 900;
  display: inline-block;
  min-width: 22px;
  text-align: center;
  line-height: 1;
}

/* 康复内容布局 */
.rehab-content {
  display: flex;
  padding: 25px;
  gap: 30px;
}

/* 左侧输入部分 */
.input-section {
  flex: 0 0 350px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.input-card {
  background-color: white;
  border-radius: var(--border-radius);
  padding: 20px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--border-color);
  transition: transform var(--transition-speed), box-shadow var(--transition-speed);
  position: relative;
  overflow: hidden;
}

.input-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 5px;
  height: 100%;
  background: var(--primary-color);
}

.input-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.input-card h4 {
  margin-bottom: 18px;
  color: var(--primary-color);
  font-size: 18px;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px dashed var(--border-color);
}

.input-card h4 i {
  margin-right: 10px;
  font-size: 20px;
}

.info-group {
  margin-bottom: 16px;
  display: flex;
  align-items: center;
}

.info-group label {
  width: 110px;
  color: var(--text-color);
  font-weight: 500;
}

.info-group input,
.info-group select {
  flex: 1;
  padding: 10px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  font-size: 14px;
  transition: border-color var(--transition-speed), box-shadow var(--transition-speed);
}

.info-group input:focus,
.info-group select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px var(--primary-light);
  outline: none;
}

.severity-slider {
  margin-bottom: 20px;
}

.severity-slider label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-color);
  font-weight: 500;
}

.severity-slider input[type="range"] {
  width: 100%;
  margin-bottom: 8px;
  height: 6px;
  border-radius: 3px;
  -webkit-appearance: none;
  appearance: none;
  background: #e5e7eb;
  outline: none;
  transition: background var(--transition-speed);
}

.severity-slider input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  transition: transform var(--transition-speed);
}

.severity-slider input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.2);
}

.severity-slider input[type="range"]::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  transition: transform var(--transition-speed);
  border: none;
}

.severity-slider input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.2);
}

.severity-value {
  display: inline-block;
  width: 35px;
  text-align: center;
  background-color: var(--primary-color);
  color: white;
  border-radius: 4px;
  padding: 3px 0;
  font-size: 14px;
  font-weight: 500;
}

.focus-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.checkbox-option {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 10px;
  border-radius: 6px;
  transition: background-color var(--transition-speed);
}

.checkbox-option:hover {
  background-color: var(--primary-light);
}

.checkbox-option input {
  margin-right: 10px;
  accent-color: var(--primary-color);
  width: 18px;
  height: 18px;
}

.primary-btn {
  background: linear-gradient(to right, var(--primary-color), var(--primary-dark));
  color: white;
  border: none;
  padding: 14px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all var(--transition-speed);
  box-shadow: 0 4px 10px rgba(71, 118, 230, 0.2);
  margin-top: 15px;
}

.primary-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.primary-btn i {
  margin-right: 10px;
  font-size: 18px;
}

.primary-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(71, 118, 230, 0.3);
}

.primary-btn:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 2px 5px rgba(71, 118, 230, 0.2);
}

/* 右侧计划显示部分 */
.plan-section {
  flex: 1;
  background-color: #fff;
  border-radius: var(--border-radius);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  border: 1px solid var(--border-color);
  position: relative;
}

.plan-section::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 100%;
  height: 5px;
  background: linear-gradient(to right, var(--primary-color), var(--success-color));
}

.plan-status {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: var(--text-light);
  font-size: 16px;
  padding: 40px;
  text-align: center;
  background: url('data:image/svg+xml;utf8,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><path d="M0 20 L40 20" stroke="%23f1f1f1" stroke-width="1"/><path d="M20 0 L20 40" stroke="%23f1f1f1" stroke-width="1"/></svg>') repeat;
}

.plan-status i {
  font-size: 48px;
  color: var(--primary-light);
  margin-bottom: 20px;
}

.plan-content {
  padding: 25px;
}

.plan-header {
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-color);
}

.plan-header h3 {
  font-size: 22px;
  color: var(--primary-dark);
  margin-bottom: 12px;
}

.plan-meta {
  display: flex;
  gap: 20px;
  color: var(--text-light);
  font-size: 14px;
  margin-bottom: 15px;
}

.plan-meta span {
  display: flex;
  align-items: center;
}

.plan-meta span i {
  margin-right: 5px;
  color: var(--primary-color);
}

.plan-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  background-color: #f1f2f6;
  color: #555;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s;
  position: relative;
}

/* 强制移除任何可能的伪元素 */
.action-btn::before,
.action-btn::after {
  display: none !important;
  content: none !important;
}

.action-btn i {
  margin-right: 5px;
  position: relative;
}

/* 强制移除图标的伪元素 */
.action-btn i::before,
.action-btn i::after {
  display: none !important;
  content: none !important;
}

.action-btn:hover {
  background-color: var(--primary-light);
  color: var(--primary-color);
}

.plan-overview {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 25px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
  border: 1px solid var(--border-color);
}

.plan-overview h4 {
  color: var(--primary-dark);
  margin-bottom: 12px;
  font-size: 18px;
  display: flex;
  align-items: center;
}

.plan-overview h4 i {
  margin-right: 8px;
}

.plan-overview p {
  color: var(--text-color);
  line-height: 1.7;
}

.plan-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 25px;
}

.plan-section-item {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
  border: 1px solid var(--border-color);
  transition: transform var(--transition-speed), box-shadow var(--transition-speed);
}

.plan-section-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
}

.plan-section-item h4 {
  color: var(--primary-dark);
  margin-bottom: 15px;
  font-size: 18px;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px dashed var(--border-color);
}

.plan-section-item h4 i {
  margin-right: 10px;
  font-size: 20px;
  color: var(--primary-color);
}

/* 新的内容列表样式 - 使用emoji图标避免FontAwesome冲突 */
.content-list-new {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
}

.content-item-new {
  display: flex !important;
  align-items: flex-start !important;
  gap: 12px !important;
  padding: 12px !important;
  background-color: #f8f9fa !important;
  border-radius: 8px !important;
  border-left: 3px solid var(--primary-color) !important;
  transition: all 0.3s ease !important;
  margin-bottom: 8px !important;
}

.content-item-new:hover {
  background-color: #e9ecef !important;
  transform: translateX(5px) !important;
}

.check-icon {
  font-size: 20px !important;
  margin-top: 2px !important;
  flex-shrink: 0 !important;
  color: var(--primary-color) !important;
  font-weight: bold !important;
}

.content-text {
  color: var(--text-color) !important;
  line-height: 1.5 !important;
  font-size: 14px !important;
}

/* 新的按钮样式 */
.action-btn-new {
  background-color: #f1f2f6 !important;
  color: #555 !important;
  border: none !important;
  padding: 10px 15px !important;
  border-radius: 6px !important;
  font-size: 14px !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  transition: all 0.3s !important;
  margin-right: 10px !important;
}

.action-btn-new:hover {
  background-color: var(--primary-light) !important;
  color: var(--primary-color) !important;
  transform: translateY(-2px) !important;
}

.btn-icon {
  font-size: 16px !important;
}

.btn-text {
  font-size: 14px !important;
}

/* 新的资源列表样式 */
.resources-list-new {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
}

.resource-item-new {
  display: flex !important;
  align-items: flex-start !important;
  gap: 12px !important;
  background-color: #f8f9ff !important;
  padding: 15px !important;
  border-radius: 8px !important;
  border-left: 4px solid var(--primary-color) !important;
  transition: transform 0.3s ease !important;
}

.resource-item-new:hover {
  transform: translateX(5px) !important;
}

.resource-icon {
  font-size: 16px !important;
  margin-top: 2px !important;
  flex-shrink: 0 !important;
}

.resource-text {
  color: var(--text-color) !important;
  font-size: 14px !important;
  line-height: 1.5 !important;
}

/* 隐藏原有的ul/li样式，使用新的content-list */
.plan-section-item ul {
  display: none !important;
}

.plan-section-item li {
  display: none !important;
}

.plan-section-item li::before {
  display: none !important;
  content: none !important;
}

/* 确保content-list样式优先级更高 */
.plan-section-item .content-list {
  display: flex !important;
  flex-direction: column !important;
  gap: 12px !important;
}

.plan-section-item .content-item {
  display: flex !important;
  align-items: flex-start !important;
  gap: 12px !important;
}

.plan-progress {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  margin-bottom: 25px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
  border: 1px solid var(--border-color);
}

.plan-progress h4 {
  color: var(--primary-dark);
  margin-bottom: 15px;
  font-size: 18px;
  display: flex;
  align-items: center;
}

.plan-progress h4 i {
  margin-right: 10px;
  color: var(--primary-color);
}

.progress-chart {
  position: relative;
  padding: 20px 0;
}

.week-markers {
  display: flex;
  justify-content: space-between;
  position: relative;
  margin-bottom: 30px;
}

.week-markers::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(to right, var(--primary-color), var(--success-color));
  z-index: 1;
  border-radius: 1.5px;
}

.week-markers span {
  background-color: white;
  padding: 0 10px;
  color: var(--text-color);
  font-size: 14px;
  position: relative;
  z-index: 2;
  font-weight: 500;
}

.milestones {
  display: flex;
  justify-content: space-between;
}

.milestone {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 80px;
  text-align: center;
  transition: transform var(--transition-speed);
}

.milestone:hover {
  transform: translateY(-5px);
}

.milestone-dot {
  width: 16px;
  height: 16px;
  background-color: var(--primary-color);
  border-radius: 50%;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
  box-shadow: 0 0 0 5px var(--primary-light);
  transition: all var(--transition-speed);
}

.milestone:hover .milestone-dot {
  transform: scale(1.2);
  box-shadow: 0 0 0 8px var(--primary-light);
}

.milestone span {
  font-size: 14px;
  color: var(--text-color);
  font-weight: 500;
}

.plan-resources {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.03);
  border: 1px solid var(--border-color);
}

.plan-resources h4 {
  color: var(--primary-dark);
  margin-bottom: 15px;
  font-size: 18px;
  display: flex;
  align-items: center;
  padding-bottom: 10px;
  border-bottom: 1px dashed var(--border-color);
}

.plan-resources h4 i {
  margin-right: 10px;
  color: var(--primary-color);
}

.resources-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.resource-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background-color: #f8f9ff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  transition: transform var(--transition-speed), box-shadow var(--transition-speed);
  border-left: 4px solid var(--primary-color);
}

.resource-item:hover {
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.resource-item i {
  color: #ffc107;
  font-size: 16px;
  margin-top: 2px;
  flex-shrink: 0;
}

.resource-item span {
  color: var(--text-color);
  font-size: 14px;
  line-height: 1.5;
}

.resource-info p {
  font-size: 14px;
  color: var(--text-light);
}

/* 页脚样式 */
footer {
  text-align: center;
  padding: 15px;
  color: #777;
  background-color: #fff;
  border-top: 1px solid #eee;
  font-size: 14px;
}

footer a {
  color: var(--primary-color);
  text-decoration: none;
}

footer a:hover {
  text-decoration: underline;
}

/* Ollama状态样式 */
.ollama-status {
  margin: 15px 0;
  padding: 10px;
  border-radius: 6px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  font-size: 14px;
  color: #495057;
}

.text-success {
  color: #28a745 !important;
}

.text-warning {
  color: #ffc107 !important;
}

/* 错误信息样式 */
.error-message {
  margin: 15px 0;
  padding: 10px;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 6px;
  color: #721c24;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 响应式特定调整 */
@media (max-width: 1024px) {
  .rehab-content {
    flex-direction: column;
  }

  .input-section {
    flex: none;
    width: 100%;
  }

  .plan-sections {
    grid-template-columns: 1fr;
  }

  nav:hover, nav.expanded {
    width: 100%;
  }

  nav {
    width: 100%;
    height: auto;
    display: flex;
    flex-direction: row;
    overflow-x: auto;
    padding: 10px;
  }

  nav:hover {
    width: 100%;
  }

  .nav-item {
    padding: 10px 15px;
    margin: 0 5px;
    flex-direction: row;
  }

  nav:hover .nav-item {
    flex-direction: row;
  }

  .nav-item i {
    margin-bottom: 0;
    margin-right: 8px;
  }

  .nav-item span {
    opacity: 1;
  }
}

@media (max-width: 768px) {
  header {
    flex-wrap: wrap;
    padding: 10px 15px;
  }

  .search-bar {
    order: 3;
    width: 100%;
    margin: 10px 0 0;
  }

  .user-actions {
    width: 100%;
    justify-content: space-between;
  }

  .main-area {
    padding: 15px;
  }

  .input-card h4,
  .plan-section-item h4,
  .plan-resources h4,
  .plan-progress h4,
  .plan-overview h4 {
    font-size: 16px;
  }

  .plan-header h3 {
    font-size: 18px;
  }

  .primary-btn {
    padding: 12px;
  }
}
</style>
