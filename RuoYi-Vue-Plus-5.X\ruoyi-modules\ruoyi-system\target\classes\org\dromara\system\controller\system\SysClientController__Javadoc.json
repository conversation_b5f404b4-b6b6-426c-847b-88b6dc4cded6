{"doc": " 客户端管理\n\n <AUTHOR>\n @date 2023-06-18\n", "fields": [], "enumConstants": [], "methods": [{"name": "list", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询客户端管理列表\n"}, {"name": "export", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo", "jakarta.servlet.http.HttpServletResponse"], "doc": " 导出客户端管理列表\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 获取客户端管理详细信息\n\n @param id 主键\n"}, {"name": "add", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo"], "doc": " 新增客户端管理\n"}, {"name": "edit", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo"], "doc": " 修改客户端管理\n"}, {"name": "changeStatus", "paramTypes": ["org.dromara.system.domain.bo.SysClientBo"], "doc": " 状态修改\n"}, {"name": "remove", "paramTypes": ["java.lang.Long[]"], "doc": " 删除客户端管理\n\n @param ids 主键串\n"}], "constructors": []}