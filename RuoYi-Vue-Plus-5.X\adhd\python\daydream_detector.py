"""
增强版发呆检测器
主要基于眼动模型 + MediaPipe面部关键点 + 面部识别
动作识别作为辅助检测
"""

import cv2
import numpy as np
import mediapipe as mp
import time
import math
from collections import deque
from typing import Dict, List, Tuple, Optional
import logging
from dataclasses import dataclass
from PIL import Image, ImageDraw, ImageFont
import os

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def put_chinese_text(img, text, position, font_size=30, color=(255, 255, 255)):
    """
    在OpenCV图像上绘制中文文本
    """
    try:
        # 转换为PIL图像
        img_pil = Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))
        draw = ImageDraw.Draw(img_pil)

        # 尝试加载中文字体
        font_path = None
        possible_fonts = [
            "C:/Windows/Fonts/simhei.ttf",  # 黑体
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/msyh.ttc",    # 微软雅黑
            "/System/Library/Fonts/PingFang.ttc",  # macOS
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",  # Linux
        ]

        for font_file in possible_fonts:
            if os.path.exists(font_file):
                font_path = font_file
                break

        if font_path:
            font = ImageFont.truetype(font_path, font_size)
        else:
            # 使用默认字体
            font = ImageFont.load_default()

        # 绘制文本
        draw.text(position, text, font=font, fill=color)

        # 转换回OpenCV格式
        img_cv = cv2.cvtColor(np.array(img_pil), cv2.COLOR_RGB2BGR)
        return img_cv
    except Exception as e:
        # 如果出错，回退到英文显示
        logger.warning(f"中文字体加载失败，使用英文显示: {e}")
        cv2.putText(img, text.encode('ascii', 'ignore').decode('ascii'),
                   position, cv2.FONT_HERSHEY_SIMPLEX,
                   font_size/30, color, 2)
        return img

@dataclass
class EyeMetrics:
    """眼部指标"""
    blink_rate: float = 0.0
    gaze_stability: float = 0.0
    eye_openness: float = 0.0
    pupil_movement: float = 0.0
    fixation_duration: float = 0.0

@dataclass
class FaceMetrics:
    """面部指标"""
    expression_change: float = 0.0
    head_pose_stability: float = 0.0
    micro_expression_count: int = 0
    facial_tension: float = 0.0

@dataclass
class AttentionResult:
    """4级注意力检测结果"""
    attention_level: int = 1  # 0:高度专注, 1:专注, 2:分心, 3:发呆
    level_name: str = "专注"
    confidence: float = 0.0
    eye_score: float = 0.0
    face_score: float = 0.0
    motion_score: float = 0.0
    details: Dict = None

    # 兼容性属性
    @property
    def is_daydreaming(self) -> bool:
        """兼容旧接口：是否发呆"""
        return self.attention_level == 3

class EnhancedEyeTracker:
    """增强版眼动追踪器"""
    
    def __init__(self, history_size: int = 30):
        self.history_size = history_size
        self.eye_positions = deque(maxlen=history_size)
        self.blink_history = deque(maxlen=history_size)
        self.gaze_points = deque(maxlen=history_size)
        self.last_blink_time = 0
        self.blink_count = 0
        self.fixation_start_time = None
        
        # 眼部关键点索引 (MediaPipe 468点模型)
        self.LEFT_EYE_INDICES = [33, 7, 163, 144, 145, 153, 154, 155, 133, 173, 157, 158, 159, 160, 161, 246]
        self.RIGHT_EYE_INDICES = [362, 382, 381, 380, 374, 373, 390, 249, 263, 466, 388, 387, 386, 385, 384, 398]
        self.LEFT_IRIS_INDICES = [474, 475, 476, 477]
        self.RIGHT_IRIS_INDICES = [469, 470, 471, 472]
    
    def extract_eye_landmarks(self, face_landmarks) -> Dict:
        """提取眼部关键点"""
        if not face_landmarks:
            return {}
        
        landmarks = face_landmarks.landmark
        h, w = 480, 640  # 假设图像尺寸
        
        # 提取左眼关键点
        left_eye = []
        for idx in self.LEFT_EYE_INDICES:
            if idx < len(landmarks):
                point = landmarks[idx]
                left_eye.append([point.x * w, point.y * h])
        
        # 提取右眼关键点
        right_eye = []
        for idx in self.RIGHT_EYE_INDICES:
            if idx < len(landmarks):
                point = landmarks[idx]
                right_eye.append([point.x * w, point.y * h])
        
        # 提取虹膜关键点
        left_iris = []
        for idx in self.LEFT_IRIS_INDICES:
            if idx < len(landmarks):
                point = landmarks[idx]
                left_iris.append([point.x * w, point.y * h])
        
        right_iris = []
        for idx in self.RIGHT_IRIS_INDICES:
            if idx < len(landmarks):
                point = landmarks[idx]
                right_iris.append([point.x * w, point.y * h])
        
        return {
            'left_eye': np.array(left_eye),
            'right_eye': np.array(right_eye),
            'left_iris': np.array(left_iris),
            'right_iris': np.array(right_iris)
        }
    
    def calculate_eye_aspect_ratio(self, eye_points: np.ndarray) -> float:
        """计算眼睛长宽比 (EAR)"""
        if len(eye_points) < 6:
            return 0.0
        
        # 计算垂直距离
        vertical_1 = np.linalg.norm(eye_points[1] - eye_points[5])
        vertical_2 = np.linalg.norm(eye_points[2] - eye_points[4])
        
        # 计算水平距离
        horizontal = np.linalg.norm(eye_points[0] - eye_points[3])
        
        if horizontal == 0:
            return 0.0
        
        # EAR = (vertical_1 + vertical_2) / (2 * horizontal)
        ear = (vertical_1 + vertical_2) / (2.0 * horizontal)
        return ear
    
    def detect_blink(self, ear_left: float, ear_right: float, threshold: float = 0.25) -> bool:
        """检测眨眼"""
        avg_ear = (ear_left + ear_right) / 2.0
        return avg_ear < threshold
    
    def calculate_gaze_direction(self, eye_landmarks: Dict) -> Tuple[float, float]:
        """计算注视方向"""
        if 'left_iris' not in eye_landmarks or 'right_iris' not in eye_landmarks:
            return 0.0, 0.0
        
        left_iris = eye_landmarks['left_iris']
        right_iris = eye_landmarks['right_iris']
        
        if len(left_iris) == 0 or len(right_iris) == 0:
            return 0.0, 0.0
        
        # 计算虹膜中心
        left_center = np.mean(left_iris, axis=0)
        right_center = np.mean(right_iris, axis=0)
        
        # 计算注视方向 (简化版)
        gaze_x = (left_center[0] + right_center[0]) / 2
        gaze_y = (left_center[1] + right_center[1]) / 2
        
        return gaze_x, gaze_y
    
    def update(self, face_landmarks) -> EyeMetrics:
        """更新眼动数据并计算指标"""
        current_time = time.time()
        
        # 提取眼部关键点
        eye_landmarks = self.extract_eye_landmarks(face_landmarks)
        
        if not eye_landmarks:
            return EyeMetrics()
        
        # 计算EAR
        ear_left = self.calculate_eye_aspect_ratio(eye_landmarks.get('left_eye', np.array([])))
        ear_right = self.calculate_eye_aspect_ratio(eye_landmarks.get('right_eye', np.array([])))
        
        # 检测眨眼
        is_blinking = self.detect_blink(ear_left, ear_right)
        self.blink_history.append(is_blinking)
        
        if is_blinking and (current_time - self.last_blink_time) > 0.3:
            self.blink_count += 1
            self.last_blink_time = current_time
        
        # 计算注视方向
        gaze_x, gaze_y = self.calculate_gaze_direction(eye_landmarks)
        self.gaze_points.append((gaze_x, gaze_y))
        
        # 计算眼睛开合度
        eye_openness = (ear_left + ear_right) / 2.0
        
        # 计算注视稳定性
        gaze_stability = self._calculate_gaze_stability()
        
        # 计算眨眼频率 (每分钟)
        time_window = 60.0  # 60秒
        recent_blinks = sum(1 for i, blink in enumerate(self.blink_history) 
                          if blink and i > len(self.blink_history) - time_window)
        blink_rate = recent_blinks
        
        # 计算瞳孔运动
        pupil_movement = self._calculate_pupil_movement()
        
        # 计算注视持续时间
        fixation_duration = self._calculate_fixation_duration(gaze_x, gaze_y)
        
        return EyeMetrics(
            blink_rate=blink_rate,
            gaze_stability=gaze_stability,
            eye_openness=eye_openness,
            pupil_movement=pupil_movement,
            fixation_duration=fixation_duration
        )
    
    def _calculate_gaze_stability(self) -> float:
        """计算注视稳定性"""
        if len(self.gaze_points) < 2:
            return 1.0
        
        # 计算注视点的标准差
        points = np.array(list(self.gaze_points))
        if len(points) == 0:
            return 1.0
        
        std_x = np.std(points[:, 0])
        std_y = np.std(points[:, 1])
        
        # 稳定性越高，标准差越小
        stability = 1.0 / (1.0 + std_x + std_y)
        return stability
    
    def _calculate_pupil_movement(self) -> float:
        """计算瞳孔运动量"""
        if len(self.gaze_points) < 2:
            return 0.0
        
        points = list(self.gaze_points)
        total_movement = 0.0
        
        for i in range(1, len(points)):
            dx = points[i][0] - points[i-1][0]
            dy = points[i][1] - points[i-1][1]
            movement = math.sqrt(dx*dx + dy*dy)
            total_movement += movement
        
        return total_movement / len(points) if len(points) > 0 else 0.0
    
    def _calculate_fixation_duration(self, gaze_x: float, gaze_y: float) -> float:
        """计算注视持续时间"""
        current_time = time.time()
        
        # 如果注视点变化较大，重置注视开始时间
        if len(self.gaze_points) > 1:
            last_gaze = self.gaze_points[-2]
            distance = math.sqrt((gaze_x - last_gaze[0])**2 + (gaze_y - last_gaze[1])**2)
            
            if distance > 20:  # 阈值可调
                self.fixation_start_time = current_time
                return 0.0
        
        if self.fixation_start_time is None:
            self.fixation_start_time = current_time
            return 0.0
        
        return current_time - self.fixation_start_time

class FaceAnalyzer:
    """面部分析器"""
    
    def __init__(self, history_size: int = 30):
        self.history_size = history_size
        self.expression_history = deque(maxlen=history_size)
        self.head_pose_history = deque(maxlen=history_size)
        
        # 面部关键区域索引
        self.MOUTH_INDICES = [61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318]
        self.EYEBROW_INDICES = [70, 63, 105, 66, 107, 55, 65, 52, 53, 46]
        
    def extract_head_pose(self, face_landmarks) -> Tuple[float, float, float]:
        """提取头部姿态"""
        if not face_landmarks:
            return 0.0, 0.0, 0.0
        
        landmarks = face_landmarks.landmark
        
        # 使用特定关键点计算头部姿态
        nose_tip = landmarks[1]
        chin = landmarks[175]
        left_eye = landmarks[33]
        right_eye = landmarks[362]
        
        # 计算俯仰角 (pitch)
        pitch = math.atan2(chin.y - nose_tip.y, chin.z - nose_tip.z)
        
        # 计算偏航角 (yaw)
        yaw = math.atan2(right_eye.x - left_eye.x, right_eye.z - left_eye.z)
        
        # 计算翻滚角 (roll)
        roll = math.atan2(right_eye.y - left_eye.y, right_eye.x - left_eye.x)
        
        return pitch, yaw, roll
    
    def analyze_expression(self, face_landmarks) -> float:
        """分析表情变化"""
        if not face_landmarks:
            return 0.0
        
        landmarks = face_landmarks.landmark
        
        # 计算嘴部特征
        mouth_points = []
        for idx in self.MOUTH_INDICES:
            if idx < len(landmarks):
                point = landmarks[idx]
                mouth_points.append([point.x, point.y])
        
        # 计算眉毛特征
        eyebrow_points = []
        for idx in self.EYEBROW_INDICES:
            if idx < len(landmarks):
                point = landmarks[idx]
                eyebrow_points.append([point.x, point.y])
        
        # 简化的表情变化计算
        if mouth_points and eyebrow_points:
            mouth_center = np.mean(mouth_points, axis=0)
            eyebrow_center = np.mean(eyebrow_points, axis=0)
            expression_vector = mouth_center - eyebrow_center
            expression_magnitude = np.linalg.norm(expression_vector)
            return expression_magnitude
        
        return 0.0
    
    def update(self, face_landmarks) -> FaceMetrics:
        """更新面部分析数据"""
        # 提取头部姿态
        pitch, yaw, roll = self.extract_head_pose(face_landmarks)
        self.head_pose_history.append((pitch, yaw, roll))
        
        # 分析表情
        expression_change = self.analyze_expression(face_landmarks)
        self.expression_history.append(expression_change)
        
        # 计算头部姿态稳定性
        head_pose_stability = self._calculate_head_pose_stability()
        
        # 计算表情变化
        avg_expression_change = np.mean(list(self.expression_history)) if self.expression_history else 0.0
        
        return FaceMetrics(
            expression_change=avg_expression_change,
            head_pose_stability=head_pose_stability,
            micro_expression_count=0,  # 简化版暂不实现
            facial_tension=0.0  # 简化版暂不实现
        )
    
    def _calculate_head_pose_stability(self) -> float:
        """计算头部姿态稳定性"""
        if len(self.head_pose_history) < 2:
            return 1.0
        
        poses = np.array(list(self.head_pose_history))
        std_pitch = np.std(poses[:, 0])
        std_yaw = np.std(poses[:, 1])
        std_roll = np.std(poses[:, 2])
        
        # 稳定性越高，标准差越小
        stability = 1.0 / (1.0 + std_pitch + std_yaw + std_roll)
        return stability

class EnhancedAttentionDetector:
    """增强版4级注意力检测器（规则模型）"""

    def __init__(self):
        # 初始化MediaPipe
        self.mp_face_mesh = mp.solutions.face_mesh
        self.face_mesh = self.mp_face_mesh.FaceMesh(
            static_image_mode=False,
            max_num_faces=1,
            refine_landmarks=True,
            min_detection_confidence=0.7,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        self.mp_drawing_styles = mp.solutions.drawing_styles

        # 初始化分析器
        self.eye_tracker = EnhancedEyeTracker()
        self.face_analyzer = FaceAnalyzer()

        # 检测参数
        self.detection_history = deque(maxlen=10)

        # 4级注意力阈值设置
        self.attention_thresholds = {
            'highly_focused': 0.2,   # 高度专注：综合评分 < 0.2
            'focused': 0.4,          # 专注：0.2 <= 评分 < 0.4
            'distracted': 0.7,       # 分心：0.4 <= 评分 < 0.7
            'daydreaming': 1.0       # 发呆：评分 >= 0.7
        }

        # 注意力等级映射
        self.level_names = {
            0: '高度专注',
            1: '专注',
            2: '分心',
            3: '发呆'
        }

        self.level_colors = {
            0: (0, 255, 0),      # 绿色 - 高度专注
            1: (0, 200, 0),      # 浅绿色 - 专注
            2: (0, 165, 255),    # 橙色 - 分心
            3: (0, 0, 255)       # 红色 - 发呆
        }

        # 注意力检测权重
        self.weights = {
            'eye_score': 0.5,      # 眼动模型权重最高
            'face_score': 0.3,     # 面部识别权重
            'motion_score': 0.2    # 动作识别权重最低
        }

    def calculate_attention_score(self, eye_metrics: EyeMetrics, face_metrics: FaceMetrics) -> Tuple[float, Dict]:
        """计算注意力分散评分（分数越高越分心/发呆）"""

        # 眼部评分 (权重最高)
        eye_score = 0.0

        # 1. 眨眼频率异常 (正常12-20次/分钟)
        if eye_metrics.blink_rate < 8 or eye_metrics.blink_rate > 25:
            eye_score += 0.3

        # 2. 注视稳定性过高 (发呆时注视点固定)
        if eye_metrics.gaze_stability > 0.8:
            eye_score += 0.4

        # 3. 注视持续时间过长 (>3秒)
        if eye_metrics.fixation_duration > 3.0:
            eye_score += 0.3

        # 4. 瞳孔运动减少
        if eye_metrics.pupil_movement < 5.0:
            eye_score += 0.2

        # 5. 眼睛开合度异常
        if eye_metrics.eye_openness < 0.2 or eye_metrics.eye_openness > 0.8:
            eye_score += 0.1

        eye_score = min(eye_score, 1.0)

        # 面部评分
        face_score = 0.0

        # 1. 表情变化减少
        if face_metrics.expression_change < 0.1:
            face_score += 0.4

        # 2. 头部姿态过于稳定
        if face_metrics.head_pose_stability > 0.9:
            face_score += 0.4

        # 3. 面部紧张度低
        if face_metrics.facial_tension < 0.3:
            face_score += 0.2

        face_score = min(face_score, 1.0)

        # 动作评分 (简化版)
        motion_score = 0.0
        if face_metrics.head_pose_stability > 0.85:
            motion_score = 0.6

        # 综合评分
        total_score = (
            eye_score * self.weights['eye_score'] +
            face_score * self.weights['face_score'] +
            motion_score * self.weights['motion_score']
        )

        details = {
            'eye_metrics': {
                'blink_rate': eye_metrics.blink_rate,
                'gaze_stability': eye_metrics.gaze_stability,
                'fixation_duration': eye_metrics.fixation_duration,
                'pupil_movement': eye_metrics.pupil_movement,
                'eye_openness': eye_metrics.eye_openness
            },
            'face_metrics': {
                'expression_change': face_metrics.expression_change,
                'head_pose_stability': face_metrics.head_pose_stability,
                'facial_tension': face_metrics.facial_tension
            },
            'scores': {
                'eye_score': eye_score,
                'face_score': face_score,
                'motion_score': motion_score,
                'total_score': total_score
            }
        }

        return total_score, details

    def detect(self, frame: np.ndarray) -> AttentionResult:
        """检测4级注意力状态"""
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.face_mesh.process(rgb_frame)

        if not results.multi_face_landmarks:
            return AttentionResult(
                attention_level=1,
                level_name="专注",
                confidence=0.0,
                details={'error': 'No face detected'}
            )

        # 获取第一个检测到的面部
        face_landmarks = results.multi_face_landmarks[0]

        # 更新分析器
        eye_metrics = self.eye_tracker.update(face_landmarks)
        face_metrics = self.face_analyzer.update(face_landmarks)

        # 计算注意力分散评分
        total_score, details = self.calculate_attention_score(eye_metrics, face_metrics)

        # 添加到历史记录
        self.detection_history.append(total_score)

        # 使用滑动窗口平滑结果
        avg_score = np.mean(list(self.detection_history))

        # 根据评分确定注意力等级
        attention_level = self._classify_attention_level(avg_score)
        level_name = self.level_names[attention_level]

        return AttentionResult(
            attention_level=attention_level,
            level_name=level_name,
            confidence=avg_score,
            eye_score=details['scores']['eye_score'],
            face_score=details['scores']['face_score'],
            motion_score=details['scores']['motion_score'],
            details=details
        )

    def _classify_attention_level(self, score: float) -> int:
        """根据评分分类注意力等级"""
        if score < self.attention_thresholds['highly_focused']:
            return 0  # 高度专注
        elif score < self.attention_thresholds['focused']:
            return 1  # 专注
        elif score < self.attention_thresholds['distracted']:
            return 2  # 分心
        else:
            return 3  # 发呆

    def draw_landmarks(self, frame: np.ndarray, face_landmarks) -> np.ndarray:
        """绘制面部关键点 (包括MediaPipe的绿点)"""
        if face_landmarks is None:
            return frame

        # 绘制面部网格
        self.mp_drawing.draw_landmarks(
            frame,
            face_landmarks,
            self.mp_face_mesh.FACEMESH_CONTOURS,
            landmark_drawing_spec=None,
            connection_drawing_spec=self.mp_drawing_styles.get_default_face_mesh_contours_style()
        )

        # 绘制眼部关键点 (绿色)
        eye_indices = self.eye_tracker.LEFT_EYE_INDICES + self.eye_tracker.RIGHT_EYE_INDICES
        for idx in eye_indices:
            if idx < len(face_landmarks.landmark):
                landmark = face_landmarks.landmark[idx]
                x = int(landmark.x * frame.shape[1])
                y = int(landmark.y * frame.shape[0])
                cv2.circle(frame, (x, y), 2, (0, 255, 0), -1)  # 绿色点

        # 绘制虹膜关键点 (红色)
        iris_indices = self.eye_tracker.LEFT_IRIS_INDICES + self.eye_tracker.RIGHT_IRIS_INDICES
        for idx in iris_indices:
            if idx < len(face_landmarks.landmark):
                landmark = face_landmarks.landmark[idx]
                x = int(landmark.x * frame.shape[1])
                y = int(landmark.y * frame.shape[0])
                cv2.circle(frame, (x, y), 3, (0, 0, 255), -1)  # 红色点

        return frame

    def draw_clean_landmarks(self, frame: np.ndarray, result: AttentionResult) -> np.ndarray:
        """只绘制面部特征点，不显示文字信息（用于WebRTC）"""
        # 只返回带有面部特征点的干净图像
        return frame

    def draw_detection_info(self, frame: np.ndarray, result: AttentionResult) -> np.ndarray:
        """绘制4级注意力检测信息 - WebRTC模式下不绘制任何文字"""
        # WebRTC模式下保持视频流干净，不绘制任何文字信息
        return frame

def main():
    """主函数 - 演示4级注意力检测系统"""
    detector = EnhancedAttentionDetector()
    cap = cv2.VideoCapture(0)

    logger.info("启动4级注意力检测系统（规则模型）...")
    logger.info("注意力等级: 0=高度专注, 1=专注, 2=分心, 3=发呆")
    logger.info("按 'q' 退出")

    while True:
        ret, frame = cap.read()
        if not ret:
            break

        # 检测注意力状态
        result = detector.detect(frame)

        # 绘制关键点和检测信息
        if result.details and 'error' not in result.details:
            # 重新获取面部关键点用于绘制
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = detector.face_mesh.process(rgb_frame)
            if results.multi_face_landmarks:
                frame = detector.draw_landmarks(frame, results.multi_face_landmarks[0])

        frame = detector.draw_detection_info(frame, result)

        cv2.imshow('4级注意力检测系统 - 规则模型', frame)

        if cv2.waitKey(1) & 0xFF == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
