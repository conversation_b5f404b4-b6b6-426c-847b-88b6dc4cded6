/**
 * Python服务器管理器
 * 用于启动、停止和监控Python WebRTC服务器
 */

const { spawn, exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const axios = require('axios');

class PythonServerManager {
  constructor() {
    this.pythonProcess = null;
    this.status = 'stopped'; // stopped, starting, running, error
    this.port = 8765;
    this.host = 'localhost';
    this.pythonPath = this.findPythonPath();
    this.serverScript = path.join(__dirname, '..', 'python', 'start_webrtc_server.py');
    
    console.log('🐍 Python服务器管理器初始化');
    console.log(`Python路径: ${this.pythonPath}`);
    console.log(`服务器脚本: ${this.serverScript}`);
  }

  /**
   * 查找Python可执行文件路径
   */
  findPythonPath() {
    const { execSync } = require('child_process');

    const possiblePaths = [
      'python',
      'python3',
      'py',
      'C:\\Python312\\python.exe',
      'C:\\Python311\\python.exe',
      'C:\\Python310\\python.exe',
      'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python312\\python.exe',
      'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python.exe',
      'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python310\\python.exe',
      '/usr/bin/python3',
      '/usr/local/bin/python3'
    ];

    for (const pythonPath of possiblePaths) {
      try {
        const versionOutput = execSync(`"${pythonPath}" --version`, {
          encoding: 'utf8',
          timeout: 5000
        }).trim();

        console.log(`🔍 测试Python路径: ${pythonPath}`);
        console.log(`📋 版本信息: ${versionOutput}`);

        // 检查是否是Python 3.12
        if (versionOutput.includes('Python 3.12')) {
          console.log(`✅ 找到Python 3.12: ${pythonPath}`);
          return pythonPath;
        }

        // 如果不是3.12但是可用，记录但继续寻找
        console.log(`⚠️ 找到Python但版本不是3.12: ${pythonPath} (${versionOutput})`);

      } catch (error) {
        // 继续尝试下一个路径
        console.log(`❌ Python路径无效: ${pythonPath}`);
      }
    }

    // 如果没找到3.12，使用第一个可用的Python
    for (const pythonPath of possiblePaths) {
      try {
        execSync(`"${pythonPath}" --version`, { stdio: 'ignore', timeout: 5000 });
        console.log(`⚠️ 未找到Python 3.12，使用: ${pythonPath}`);
        return pythonPath;
      } catch (error) {
        // 继续尝试下一个路径
      }
    }

    console.warn('⚠️ 未找到任何Python，使用默认路径: python');
    return 'python';
  }

  /**
   * 检查Python依赖是否安装
   */
  async checkDependencies() {
    return new Promise((resolve) => {
      console.log(`🔍 使用Python路径检查依赖: ${this.pythonPath}`);

      const checkScript = `
import sys
import os
print(f"Python version: {sys.version}")
print(f"Python path: {sys.executable}")
print(f"Working directory: {os.getcwd()}")
print("Checking dependencies...")

missing_deps = []
required_packages = [
    ('aiohttp', 'aiohttp'),
    ('aiohttp_cors', 'aiohttp_cors'),
    ('aiortc', 'aiortc'),
    ('cv2', 'opencv-python'),
    ('mediapipe', 'mediapipe'),
    ('numpy', 'numpy'),
    ('PIL', 'Pillow')
]

for import_name, package_name in required_packages:
    try:
        __import__(import_name)
        print(f"OK {package_name} installed")
    except ImportError as e:
        print(f"MISSING {package_name}: {e}")
        missing_deps.append(package_name)

if missing_deps:
    print(f"Missing dependencies: {', '.join(missing_deps)}")
    print("Please run: pip install -r python/requirements_webrtc.txt")
    sys.exit(1)
else:
    print("DEPENDENCIES_OK")
`;

      const pythonProcess = spawn(this.pythonPath, ['-c', checkScript], {
        cwd: path.join(__dirname, '..'),
        env: { ...process.env, PYTHONIOENCODING: 'utf-8' }
      });
      let output = '';
      let errorOutput = '';

      pythonProcess.stdout.on('data', (data) => {
        const text = data.toString();
        output += text;
        console.log(`[Python依赖检查] ${text.trim()}`);
      });

      pythonProcess.stderr.on('data', (data) => {
        const text = data.toString();
        errorOutput += text;
        console.error(`[Python依赖检查错误] ${text.trim()}`);
      });

      pythonProcess.on('close', (code) => {
        if (code === 0 && output.includes('DEPENDENCIES_OK')) {
          console.log('✅ Python依赖检查通过');
          resolve(true);
        } else {
          console.error('❌ Python依赖检查失败');
          console.error('输出:', output);
          console.error('错误:', errorOutput);
          resolve(false);
        }
      });

      pythonProcess.on('error', (error) => {
        console.error('❌ Python进程启动失败:', error);
        resolve(false);
      });
    });
  }

  /**
   * 检查服务器是否正在运行
   */
  async isServerRunning() {
    try {
      const response = await axios.get(`http://${this.host}:${this.port}/health`, {
        timeout: 3000
      });
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  /**
   * 启动Python服务器
   */
  async startServer() {
    if (this.status === 'running') {
      console.log('⚠️ Python服务器已在运行');
      return { success: true, message: 'Python服务器已在运行' };
    }

    if (this.status === 'starting') {
      console.log('⚠️ Python服务器正在启动中');
      return { success: false, message: 'Python服务器正在启动中，请稍候' };
    }

    try {
      console.log('🚀 启动Python WebRTC服务器...');
      this.status = 'starting';

      // 检查服务器脚本是否存在
      if (!fs.existsSync(this.serverScript)) {
        throw new Error(`服务器脚本不存在: ${this.serverScript}`);
      }

      // 检查依赖
      const depsOk = await this.checkDependencies();
      if (!depsOk) {
        throw new Error('Python依赖检查失败，请运行: pip install -r python/requirements_webrtc.txt');
      }

      // 启动Python进程
      this.pythonProcess = spawn(this.pythonPath, [this.serverScript], {
        cwd: path.join(__dirname, '..'),
        stdio: ['pipe', 'pipe', 'pipe'],
        env: { ...process.env, PYTHONIOENCODING: 'utf-8' }
      });

      // 监听输出
      this.pythonProcess.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(`[Python服务器] ${output.trim()}`);
        
        // 检查启动成功标志
        if (output.includes('等待前端连接') || output.includes('WebRTC注意力分析服务器')) {
          this.status = 'running';
          console.log('✅ Python服务器启动成功');
        }
      });

      this.pythonProcess.stderr.on('data', (data) => {
        const error = data.toString();
        console.error(`[Python服务器错误] ${error.trim()}`);
      });

      this.pythonProcess.on('close', (code) => {
        console.log(`Python服务器进程退出，代码: ${code}`);
        this.status = 'stopped';
        this.pythonProcess = null;
      });

      this.pythonProcess.on('error', (error) => {
        console.error('Python服务器启动失败:', error);
        this.status = 'error';
        this.pythonProcess = null;
      });

      // 等待服务器启动
      await this.waitForServerReady();

      return { success: true, message: 'Python服务器启动成功' };

    } catch (error) {
      console.error('❌ 启动Python服务器失败:', error);
      this.status = 'error';
      return { success: false, message: error.message };
    }
  }

  /**
   * 停止Python服务器
   */
  async stopServer() {
    try {
      console.log('🛑 停止Python服务器...');

      if (this.pythonProcess) {
        // 发送终止信号
        this.pythonProcess.kill('SIGTERM');
        
        // 等待进程结束
        await new Promise((resolve) => {
          const timeout = setTimeout(() => {
            // 强制杀死进程
            if (this.pythonProcess) {
              this.pythonProcess.kill('SIGKILL');
            }
            resolve();
          }, 5000);

          if (this.pythonProcess) {
            this.pythonProcess.on('close', () => {
              clearTimeout(timeout);
              resolve();
            });
          } else {
            clearTimeout(timeout);
            resolve();
          }
        });
      }

      this.status = 'stopped';
      this.pythonProcess = null;
      console.log('✅ Python服务器已停止');
      
      return { success: true, message: 'Python服务器已停止' };

    } catch (error) {
      console.error('❌ 停止Python服务器失败:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * 等待服务器就绪
   */
  async waitForServerReady(maxAttempts = 15, interval = 1000) {
    console.log(`⏳ 等待Python服务器就绪 (最多${maxAttempts}秒)...`);
    
    for (let i = 0; i < maxAttempts; i++) {
      const isReady = await this.isServerRunning();
      if (isReady) {
        console.log('✅ Python服务器已就绪');
        this.status = 'running';
        return true;
      }
      
      console.log(`⏳ 等待中... (${i + 1}/${maxAttempts})`);
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    console.error('❌ Python服务器启动超时');
    this.status = 'error';
    throw new Error('Python服务器启动超时');
  }

  /**
   * 获取服务器状态
   */
  getStatus() {
    return {
      status: this.status,
      port: this.port,
      host: this.host,
      processId: this.pythonProcess ? this.pythonProcess.pid : null
    };
  }

  /**
   * 重启服务器
   */
  async restartServer() {
    console.log('🔄 重启Python服务器...');
    
    await this.stopServer();
    await new Promise(resolve => setTimeout(resolve, 2000)); // 等待2秒
    return await this.startServer();
  }
}

module.exports = PythonServerManager;
