<template>
  <div class="profile-test-helper">
    <div class="test-panel">
      <h3>个人中心测试工具</h3>
      
      <!-- 模拟用户数据 -->
      <div class="test-section">
        <h4>模拟用户数据</h4>
        <div class="test-buttons">
          <button @click="simulateNewUser" class="btn btn-primary">
            <i class="fas fa-user-plus"></i>
            模拟新用户
          </button>
          <button @click="simulatePartialUser" class="btn btn-warning">
            <i class="fas fa-user-edit"></i>
            模拟部分信息用户
          </button>
          <button @click="simulateCompleteUser" class="btn btn-success">
            <i class="fas fa-user-check"></i>
            模拟完整信息用户
          </button>
        </div>
      </div>

      <!-- 测试API连接 -->
      <div class="test-section">
        <h4>API连接测试</h4>
        <div class="test-buttons">
          <button @click="testPatientAPI" class="btn btn-info" :disabled="isTestingAPI">
            <i :class="isTestingAPI ? 'fas fa-spinner fa-spin' : 'fas fa-plug'"></i>
            {{ isTestingAPI ? '测试中...' : '测试患者API' }}
          </button>
          <button @click="testServerConnection" class="btn btn-secondary" :disabled="isTestingConnection">
            <i :class="isTestingConnection ? 'fas fa-spinner fa-spin' : 'fas fa-server'"></i>
            {{ isTestingConnection ? '连接中...' : '测试服务器连接' }}
          </button>
        </div>
      </div>

      <!-- 测试结果 -->
      <div v-if="testResults.length > 0" class="test-results">
        <h4>测试结果</h4>
        <div class="results-list">
          <div 
            v-for="(result, index) in testResults" 
            :key="index"
            class="result-item"
            :class="result.type"
          >
            <i :class="getResultIcon(result.type)"></i>
            <span class="result-time">{{ formatTime(result.time) }}</span>
            <span class="result-message">{{ result.message }}</span>
          </div>
        </div>
        <button @click="clearResults" class="btn btn-outline">
          <i class="fas fa-trash"></i>
          清除结果
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { patientAPI } from '@/api/patientAPI.js'
import { localStorageUtils } from '@/api/authAPI.js'

export default {
  name: 'ProfileTestHelper',
  data() {
    return {
      isTestingAPI: false,
      isTestingConnection: false,
      testResults: []
    }
  },
  
  methods: {
    // 模拟新用户（最少信息）
    simulateNewUser() {
      const newUser = {
        userId: 'test_new_' + Date.now(),
        username: '新用户',
        isLoggedIn: true
      }
      
      localStorageUtils.saveUser(newUser, true)
      this.addTestResult('info', '已模拟新用户登录，请刷新个人中心页面')
      
      // 触发页面刷新事件
      this.$emit('user-changed', newUser)
    },

    // 模拟部分信息用户
    simulatePartialUser() {
      const partialUser = {
        userId: 'test_partial_' + Date.now(),
        username: '部分信息用户',
        age: 25,
        gender: 'male',
        phone: '13800138000',
        isLoggedIn: true
      }
      
      localStorageUtils.saveUser(partialUser, true)
      this.addTestResult('warning', '已模拟部分信息用户登录，缺少身份证、地址等信息')
      
      this.$emit('user-changed', partialUser)
    },

    // 模拟完整信息用户
    simulateCompleteUser() {
      const completeUser = {
        userId: 'test_complete_' + Date.now(),
        username: '完整信息用户',
        patientName: '张三',
        age: 30,
        gender: 'female',
        phone: '13900139000',
        phoneNumber: '13900139000',
        idCard: '110101199001011234',
        address: '北京市朝阳区测试街道123号',
        emergencyContact: '李四',
        emergencyPhone: '13800138001',
        bloodType: 'A',
        allergies: '无',
        medicalHistory: '无',
        isLoggedIn: true
      }
      
      localStorageUtils.saveUser(completeUser, true)
      this.addTestResult('success', '已模拟完整信息用户登录，所有必填字段已填写')
      
      this.$emit('user-changed', completeUser)
    },

    // 测试患者API
    async testPatientAPI() {
      this.isTestingAPI = true
      try {
        // 测试获取患者列表
        const result = await patientAPI.getPatientList({ pageSize: 1 })
        
        if (result.success) {
          this.addTestResult('success', `患者API连接成功，共找到 ${result.total} 条记录`)
        } else {
          this.addTestResult('error', `患者API测试失败: ${result.message}`)
        }
      } catch (error) {
        this.addTestResult('error', `患者API测试异常: ${error.message}`)
      } finally {
        this.isTestingAPI = false
      }
    },

    // 测试服务器连接
    async testServerConnection() {
      this.isTestingConnection = true
      try {
        const response = await fetch('http://localhost:80/actuator/health', {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        })

        if (response.ok) {
          const result = await response.json()
          this.addTestResult('success', '服务器连接正常')
        } else {
          this.addTestResult('warning', `服务器响应异常: ${response.status}`)
        }
      } catch (error) {
        this.addTestResult('error', `服务器连接失败: ${error.message}`)
      } finally {
        this.isTestingConnection = false
      }
    },

    // 添加测试结果
    addTestResult(type, message) {
      this.testResults.unshift({
        type,
        message,
        time: new Date()
      })
      
      // 限制结果数量
      if (this.testResults.length > 10) {
        this.testResults = this.testResults.slice(0, 10)
      }
    },

    // 清除测试结果
    clearResults() {
      this.testResults = []
    },

    // 获取结果图标
    getResultIcon(type) {
      const icons = {
        success: 'fas fa-check-circle',
        error: 'fas fa-times-circle',
        warning: 'fas fa-exclamation-triangle',
        info: 'fas fa-info-circle'
      }
      return icons[type] || 'fas fa-circle'
    },

    // 格式化时间
    formatTime(time) {
      return time.toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
  }
}
</script>

<style scoped>
.profile-test-helper {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 350px;
}

.test-panel {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.test-panel h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  text-align: center;
}

.test-section {
  margin-bottom: 20px;
}

.test-section h4 {
  margin: 0 0 10px 0;
  color: #666;
  font-size: 14px;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.btn {
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary { background: #007bff; color: white; }
.btn-warning { background: #ffc107; color: #212529; }
.btn-success { background: #28a745; color: white; }
.btn-info { background: #17a2b8; color: white; }
.btn-secondary { background: #6c757d; color: white; }
.btn-outline { background: transparent; border: 1px solid #6c757d; color: #6c757d; }

.test-results {
  margin-top: 20px;
}

.results-list {
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 10px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 8px;
  margin-bottom: 4px;
  border-radius: 4px;
  font-size: 12px;
}

.result-item.success { background: #d4edda; color: #155724; }
.result-item.error { background: #f8d7da; color: #721c24; }
.result-item.warning { background: #fff3cd; color: #856404; }
.result-item.info { background: #d1ecf1; color: #0c5460; }

.result-time {
  font-weight: 500;
  min-width: 60px;
}

.result-message {
  flex: 1;
  word-break: break-word;
}
</style>
