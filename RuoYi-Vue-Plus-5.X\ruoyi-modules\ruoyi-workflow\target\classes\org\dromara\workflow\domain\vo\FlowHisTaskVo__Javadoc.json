{"doc": " 历史任务视图\n\n <AUTHOR>\n", "fields": [{"name": "createTime", "doc": " 创建时间\n"}, {"name": "updateTime", "doc": " 更新时间\n"}, {"name": "tenantId", "doc": " 租户ID\n"}, {"name": "delFlag", "doc": " 删除标记\n"}, {"name": "definitionId", "doc": " 对应flow_definition表的id\n"}, {"name": "flowName", "doc": " 流程定义名称\n"}, {"name": "instanceId", "doc": " 流程实例表id\n"}, {"name": "taskId", "doc": " 任务表id\n"}, {"name": "cooperateType", "doc": " 协作方式(1审批 2转办 3委派 4会签 5票签 6加签 7减签)\n"}, {"name": "cooperateTypeName", "doc": " 协作方式(1审批 2转办 3委派 4会签 5票签 6加签 7减签)\n"}, {"name": "businessId", "doc": " 业务id\n"}, {"name": "nodeCode", "doc": " 开始节点编码\n"}, {"name": "nodeName", "doc": " 开始节点名称\n"}, {"name": "nodeType", "doc": " 开始节点类型（0开始节点 1中间节点 2结束节点 3互斥网关 4并行网关）\n"}, {"name": "targetNodeCode", "doc": " 目标节点编码\n"}, {"name": "targetNodeName", "doc": " 结束节点名称\n"}, {"name": "approver", "doc": " 审批者\n"}, {"name": "approveName", "doc": " 审批者\n"}, {"name": "collaborator", "doc": " 协作人(只有转办、会签、票签、委派)\n"}, {"name": "permissionList", "doc": " 权限标识 permissionFlag的list形式\n"}, {"name": "skipType", "doc": " 跳转类型（PASS通过 REJECT退回 NONE无动作）\n"}, {"name": "flowStatus", "doc": " 流程状态\n"}, {"name": "flowTaskStatus", "doc": " 任务状态\n"}, {"name": "flowStatusName", "doc": " 流程状态\n"}, {"name": "message", "doc": " 审批意见\n"}, {"name": "ext", "doc": " 业务详情 存业务类的json\n"}, {"name": "createBy", "doc": " 创建者\n"}, {"name": "createByName", "doc": " 申请人\n"}, {"name": "category", "doc": " 流程分类id\n"}, {"name": "categoryName", "doc": " 流程分类名称\n"}, {"name": "formCustom", "doc": " 审批表单是否自定义（Y是 N否）\n"}, {"name": "formPath", "doc": " 审批表单路径\n"}, {"name": "flowCode", "doc": " 流程定义编码\n"}, {"name": "version", "doc": " 流程版本号\n"}, {"name": "runDuration", "doc": " 运行时长\n"}], "enumConstants": [], "methods": [{"name": "setCreateTime", "paramTypes": ["java.util.Date"], "doc": " 设置创建时间并计算任务运行时长\n\n @param createTime 创建时间\n"}, {"name": "setUpdateTime", "paramTypes": ["java.util.Date"], "doc": " 设置更新时间并计算任务运行时长\n\n @param updateTime 更新时间\n"}, {"name": "updateRunDuration", "paramTypes": [], "doc": " 更新运行时长\n"}, {"name": "setCooperateType", "paramTypes": ["java.lang.Integer"], "doc": " 设置协作方式，并通过协作方式获取名称\n"}], "constructors": []}