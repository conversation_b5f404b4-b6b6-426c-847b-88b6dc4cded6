package org.dromara.patient.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.StringUtils;
import org.dromara.common.json.utils.JsonUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.patient.domain.MedicalRecord;
import org.dromara.patient.mapper.MedicalRecordMapper;
import org.dromara.patient.service.IMedicalAnalysisService;
// import org.dromara.patient.service.IAIAnalysisService;
// import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 医疗分析Service业务层处理
 *
 * <AUTHOR> System
 * @date 2025-07-19
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class MedicalAnalysisServiceImpl implements IMedicalAnalysisService {

    private final MedicalRecordMapper baseMapper;

    // @Qualifier("ollamaAIAnalysisService")
    // private final IAIAnalysisService aiAnalysisService;

    @Override
    public TableDataInfo<MedicalRecord> queryPageList(MedicalRecord medicalRecord, PageQuery pageQuery) {
        LambdaQueryWrapper<MedicalRecord> lqw = buildQueryWrapper(medicalRecord);
        Page<MedicalRecord> result = baseMapper.selectPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<MedicalRecord> queryList(MedicalRecord medicalRecord) {
        LambdaQueryWrapper<MedicalRecord> lqw = buildQueryWrapper(medicalRecord);
        return baseMapper.selectList(lqw);
    }

    @Override
    public MedicalRecord queryById(Long id) {
        return baseMapper.selectById(id);
    }

    @Override
    public Boolean insertByBo(MedicalRecord medicalRecord) {
        return baseMapper.insert(medicalRecord) > 0;
    }

    @Override
    public Boolean updateByBo(MedicalRecord medicalRecord) {
        return baseMapper.updateById(medicalRecord) > 0;
    }

    @Override
    public Boolean deleteWithValidByIds(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public MedicalRecord analyzePatientData(Map<String, Object> patientData) throws Exception {
        log.info("开始执行医疗分析，患者数据: {}", patientData);

        try {
            // 创建医疗记录
            MedicalRecord medicalRecord = new MedicalRecord();
            medicalRecord.setUserId((String) patientData.get("userId"));
            medicalRecord.setPatientData(JsonUtils.toJsonString(patientData));
            medicalRecord.setAnalysisTimestamp(new Date());

            // 使用传统方法进行基础分析
            medicalRecord.setAnalysisResult("基于患者数据的传统分析，建议专业医生进一步评估");
            medicalRecord.setRiskLevel("MEDIUM");
            medicalRecord.setRequiresImmediateAttention(false);
            medicalRecord.setAiModelUsed("traditional_analysis");

            log.info("✅ 传统医疗分析完成 - 风险等级: {}", medicalRecord.getRiskLevel());

            // 保存记录
            baseMapper.insert(medicalRecord);

            return medicalRecord;

        } catch (Exception e) {
            log.error("医疗分析失败: {}", e.getMessage(), e);
            throw new Exception("医疗分析失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> getRiskStatistics() throws Exception {
        try {
            Map<String, Object> statistics = new HashMap<>();
            
            // 查询风险等级统计
            LambdaQueryWrapper<MedicalRecord> lqw = Wrappers.lambdaQuery();
            List<MedicalRecord> allRecords = baseMapper.selectList(lqw);
            
            Map<String, Long> riskLevelCount = new HashMap<>();
            riskLevelCount.put("LOW", 0L);
            riskLevelCount.put("MEDIUM", 0L);
            riskLevelCount.put("HIGH", 0L);
            riskLevelCount.put("CRITICAL", 0L);
            riskLevelCount.put("UNKNOWN", 0L);
            
            long immediateAttentionCount = 0;
            
            for (MedicalRecord record : allRecords) {
                String riskLevel = record.getRiskLevel();
                if (riskLevel != null) {
                    riskLevelCount.put(riskLevel, riskLevelCount.getOrDefault(riskLevel, 0L) + 1);
                }
                
                if (Boolean.TRUE.equals(record.getRequiresImmediateAttention())) {
                    immediateAttentionCount++;
                }
            }
            
            statistics.put("totalRecords", allRecords.size());
            statistics.put("riskLevelDistribution", riskLevelCount);
            statistics.put("immediateAttentionCount", immediateAttentionCount);
            statistics.put("lastUpdateTime", new Date());
            
            return statistics;
            
        } catch (Exception e) {
            log.error("获取风险统计失败: {}", e.getMessage(), e);
            throw new Exception("获取风险统计失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> generateDiagnosticSuggestion(Long recordId) throws Exception {
        try {
            MedicalRecord record = baseMapper.selectById(recordId);
            if (record == null) {
                throw new Exception("医疗记录不存在");
            }

            Map<String, Object> result = new HashMap<>();

            // 使用传统方法生成诊断建议
            String suggestion = "基于患者数据的传统诊断建议，建议专业医生进一步评估和确诊";

            record.setDiagnosticSuggestion(suggestion);
            baseMapper.updateById(record);

            result.put("success", true);
            result.put("diagnosticSuggestion", suggestion);
            result.put("generatedAt", new Date());

            log.info("✅ 生成传统诊断建议成功 - 记录ID: {}", recordId);

            return result;

        } catch (Exception e) {
            log.error("生成诊断建议失败: {}", e.getMessage(), e);
            throw new Exception("生成诊断建议失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> generateInterventionPlan(Long recordId) throws Exception {
        try {
            MedicalRecord record = baseMapper.selectById(recordId);
            if (record == null) {
                throw new Exception("医疗记录不存在");
            }

            Map<String, Object> result = new HashMap<>();

            // 使用传统方法生成干预方案
            String plan = "基于患者数据的传统干预方案，建议制定个性化治疗计划";

            record.setInterventionPlan(plan);
            baseMapper.updateById(record);

            result.put("success", true);
            result.put("interventionPlan", plan);
            result.put("generatedAt", new Date());

            log.info("✅ 生成传统干预方案成功 - 记录ID: {}", recordId);

            return result;

        } catch (Exception e) {
            log.error("生成干预方案失败: {}", e.getMessage(), e);
            throw new Exception("生成干预方案失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Map<String, Object> getAIModelStatus() throws Exception {
        Map<String, Object> status = new HashMap<>();

        try {
            // 返回传统分析模式状态
            status.put("isAvailable", true);
            status.put("currentModel", "traditional_analysis");
            status.put("status", "ACTIVE");
            status.put("checkTime", new Date());
            status.put("features", Arrays.asList(
                "传统医疗数据分析",
                "基础风险评估",
                "标准诊断建议",
                "通用干预方案"
            ));

            return status;

        } catch (Exception e) {
            log.error("获取分析模型状态失败: {}", e.getMessage(), e);
            throw new Exception("获取分析模型状态失败: " + e.getMessage(), e);
        }
    }

    private LambdaQueryWrapper<MedicalRecord> buildQueryWrapper(MedicalRecord medicalRecord) {
        LambdaQueryWrapper<MedicalRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(medicalRecord.getUserId()), MedicalRecord::getUserId, medicalRecord.getUserId());
        lqw.eq(StringUtils.isNotBlank(medicalRecord.getRiskLevel()), MedicalRecord::getRiskLevel, medicalRecord.getRiskLevel());
        lqw.eq(medicalRecord.getRequiresImmediateAttention() != null, MedicalRecord::getRequiresImmediateAttention, medicalRecord.getRequiresImmediateAttention());
        lqw.orderByDesc(MedicalRecord::getAnalysisTimestamp);
        return lqw;
    }

    // AI相关方法已移除，使用传统分析方法
}
