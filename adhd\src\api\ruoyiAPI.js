/**
 * RuoYi后端API统一服务 - 检测会话相关功能
 */

import { request, formatters } from './axios.js'

// 提交检测会话数据API
export async function submitDetectionSession(sessionData) {
  console.log('🔍 提交检测会话数据到RuoYi后端...')
  console.log('📤 原始会话数据:', sessionData)

  // 使用formatters确保数据类型和格式正确，匹配detection_sessions表结构
  const formattedData = {
    userId: formatters.ensureString(sessionData.userId),
    username: formatters.ensureString(sessionData.username),
    sessionId: formatters.ensureString(sessionData.sessionId),
    startTime: formatters.formatDateForRuoYi(sessionData.startTime),
    endTime: formatters.formatDateForRuoYi(sessionData.endTime),
    duration: formatters.ensureNumber(sessionData.duration, 0),

    // 必须包含avg_attention_level字段
    avgAttentionLevel: formatters.ensureNumber(sessionData.avgAttentionLevel, 0),
    avgAttentionScore: formatters.ensureNumber(sessionData.avgAttentionScore, 0),

    // 注意力等级计数 (level_0_count到level_3_count)
    level0Count: formatters.ensureNumber(sessionData.focusedCount, 0),      // 专注
    level1Count: formatters.ensureNumber(sessionData.normalCount, 0),       // 正常
    level2Count: formatters.ensureNumber(sessionData.distractedCount, 0),   // 分心
    level3Count: formatters.ensureNumber(sessionData.daydreamingCount, 0),  // 发呆

    totalDetections: formatters.ensureNumber(sessionData.totalDetections, 0),
    maxAttentionScore: formatters.ensureNumber(sessionData.maxAttentionScore, 0),
    minAttentionScore: formatters.ensureNumber(sessionData.minAttentionScore, 0)
    // createdAt由后端自动生成
  }

  console.log('📤 格式化后的会话数据:', formattedData)

  try {
    const result = await request.post('/system/sessions/sync', formattedData)
    console.log('📥 会话提交响应结果:', result)
    return result
  } catch (error) {
    console.error('❌ 会话提交异常:', error)
    return {
      success: false,
      message: error.message || '会话提交失败',
      error
    }
  }
}

// 获取用户检测会话列表
export async function getUserSessions(userId, pageNum = 1, pageSize = 10) {
  console.log('📋 获取用户检测会话列表...')
  console.log('👤 用户ID:', userId)

  try {
    const result = await request.get('/system/sessions/list', {
      userId: userId,
      pageNum: pageNum,
      pageSize: pageSize
    })
    
    console.log('📥 会话列表查询结果:', result)
    return result
  } catch (error) {
    console.error('❌ 获取会话列表异常:', error)
    return {
      success: false,
      message: error.message || '获取会话列表失败',
      error
    }
  }
}

// 获取单个检测会话详情
export async function getSessionDetail(sessionId) {
  console.log('📋 获取检测会话详情...')
  console.log('📄 会话ID:', sessionId)

  try {
    const result = await request.get(`/system/sessions/${sessionId}`)
    console.log('📥 会话详情:', result)
    return result
  } catch (error) {
    console.error('❌ 获取会话详情异常:', error)
    return {
      success: false,
      message: error.message || '获取会话详情失败',
      error
    }
  }
}

// 删除检测会话记录
export async function deleteSession(sessionId) {
  console.log('🗑️ 删除检测会话记录...')
  console.log('📄 会话ID:', sessionId)

  try {
    const result = await request.delete(`/system/sessions/${sessionId}`)
    console.log('📥 删除结果:', result)
    return result
  } catch (error) {
    console.error('❌ 删除会话记录异常:', error)
    return {
      success: false,
      message: error.message || '删除会话记录失败',
      error
    }
  }
}

// 获取检测会话统计信息
export async function getSessionStatistics(userId, dateRange = null) {
  console.log('📊 获取检测会话统计信息...')
  console.log('👤 用户ID:', userId)

  try {
    const params = { userId }
    if (dateRange) {
      params.startDate = dateRange.startDate
      params.endDate = dateRange.endDate
    }

    const result = await request.get('/system/sessions/statistics', params)
    console.log('📥 统计信息结果:', result)
    return result
  } catch (error) {
    console.error('❌ 获取统计信息异常:', error)
    return {
      success: false,
      message: error.message || '获取统计信息失败',
      error
    }
  }
}

// 检查服务器连接状态
export async function checkServerStatus() {
  try {
    const result = await request.get('/actuator/health')
    return {
      success: result.success,
      status: result.code || 200,
      message: result.success ? '服务器连接正常' : '服务器连接异常',
      data: result.data
    }
  } catch (error) {
    console.error('❌ 服务器状态检查异常:', error)
    return {
      success: false,
      status: 0,
      message: '无法连接到服务器',
      error
    }
  }
}
