{"doc": " 流程实例管理 控制层\n\n <AUTHOR>\n", "fields": [], "enumConstants": [], "methods": [{"name": "selectRunningInstanceList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInstanceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询正在运行的流程实例列表\n\n @param flowInstanceBo 流程实例\n @param pageQuery      分页\n"}, {"name": "selectFinishInstanceList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInstanceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 查询已结束的流程实例列表\n\n @param flowInstanceBo 流程实例\n @param pageQuery      分页\n"}, {"name": "getInfo", "paramTypes": ["java.lang.Long"], "doc": " 根据业务id查询流程实例详细信息\n\n @param businessId 业务id\n"}, {"name": "deleteByBusinessIds", "paramTypes": ["java.util.List"], "doc": " 按照业务id删除流程实例\n\n @param businessIds 业务id\n"}, {"name": "deleteByInstanceIds", "paramTypes": ["java.util.List"], "doc": " 按照实例id删除流程实例\n\n @param instanceIds 实例id\n"}, {"name": "cancelProcessApply", "paramTypes": ["org.dromara.workflow.domain.bo.FlowCancelBo"], "doc": " 撤销流程\n\n @param bo 参数\n"}, {"name": "active", "paramTypes": ["java.lang.Long", "boolean"], "doc": " 激活/挂起流程实例\n\n @param id     流程实例id\n @param active 激活/挂起\n"}, {"name": "selectCurrentInstanceList", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInstanceBo", "org.dromara.common.mybatis.core.page.PageQuery"], "doc": " 获取当前登陆人发起的流程实例\n\n @param flowInstanceBo 参数\n @param pageQuery      分页\n"}, {"name": "flowHisTaskList", "paramTypes": ["java.lang.String"], "doc": " 获取流程图，流程记录\n\n @param businessId 业务id\n"}, {"name": "instanceVariable", "paramTypes": ["java.lang.Long"], "doc": " 获取流程变量\n\n @param instanceId 流程实例id\n"}, {"name": "invalid", "paramTypes": ["org.dromara.workflow.domain.bo.FlowInvalidBo"], "doc": " 作废流程\n\n @param bo 参数\n"}], "constructors": []}